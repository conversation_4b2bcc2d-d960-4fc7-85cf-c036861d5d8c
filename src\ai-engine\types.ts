/**
 * TainoAI - Custom AI Engine Types
 * No external APIs, fully independent AI system
 */

export interface AIMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  timestamp: Date;
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
    confidence?: number;
  };
}

export interface Conversation {
  id: string;
  userId: string;
  messages: AIMessage[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: {
    title?: string;
    tags?: string[];
    summary?: string;
  };
}

export interface AIModel {
  id: string;
  name: string;
  path: string;
  type: 'llama' | 'mistral' | 'gemma' | 'phi';
  size: string;
  loaded: boolean;
  capabilities: string[];
  performance?: {
    avgResponseTime: number;
    tokensPerSecond: number;
    memoryUsage: number;
  };
}

export interface DocumentChunk {
  id: string;
  content: string;
  embedding?: number[];
  metadata: {
    source: string;
    page?: number;
    section?: string;
    timestamp: Date;
  };
}

export interface KnowledgeBase {
  id: string;
  name: string;
  documents: DocumentChunk[];
  embeddings: Map<string, number[]>;
  createdAt: Date;
  updatedAt: Date;
}

export interface AITool {
  name: string;
  description: string;
  parameters: Record<string, any>;
  execute: (params: any) => Promise<any>;
}

export interface AIAgent {
  id: string;
  name: string;
  personality: string;
  systemPrompt: string;
  tools: AITool[];
  memory: AIMessage[];
  capabilities: string[];
}

export interface WebSearchResult {
  title: string;
  url: string;
  snippet: string;
  relevance: number;
}

export interface AIResponse {
  content: string;
  confidence: number;
  sources?: string[];
  reasoning?: string;
  metadata: {
    model: string;
    tokens: number;
    processingTime: number;
    memoryUsed: number;
  };
}

export interface AIEngineConfig {
  models: {
    primary: string;
    secondary: string;
    fallback: string;
  };
  performance: {
    maxTokens: number;
    temperature: number;
    topP: number;
    topK: number;
    threads: number;
    contextSize: number;
  };
  features: {
    webSearch: boolean;
    documentLearning: boolean;
    voiceInput: boolean;
    imageAnalysis: boolean;
    codeExecution: boolean;
  };
  storage: {
    database: string;
    modelsPath: string;
    documentsPath: string;
    cachePath: string;
  };
}

export interface PromptTemplate {
  id: string;
  name: string;
  template: string;
  variables: string[];
  category: 'system' | 'user' | 'assistant' | 'tool';
}

export interface AIMemory {
  shortTerm: AIMessage[];
  longTerm: Map<string, any>;
  episodic: Conversation[];
  semantic: KnowledgeBase[];
  procedural: AITool[];
}

export interface ReActStep {
  thought: string;
  action: string;
  observation: string;
  reasoning: string;
}

export interface AITask {
  id: string;
  description: string;
  steps: ReActStep[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: any;
  error?: string;
}
