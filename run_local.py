#!/usr/bin/env python3
"""
TainoAI Local Runner
Quick script to run TainoAI locally for testing
"""

import os
import sys
import subprocess
import logging
import time
import webbrowser
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def print_banner():
    """Print TainoAI banner"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    TainoAI Local Runner                     ║
║              Full AI Power - Local Testing                  ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_docker():
    """Check if Docker is available"""
    try:
        result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"✅ Docker available: {result.stdout.strip()}")
            return True
        else:
            logger.error("❌ Docker not found")
            return False
    except FileNotFoundError:
        logger.error("❌ Docker not found. Please install Docker.")
        return False

def check_image():
    """Check if TainoAI Docker image exists"""
    try:
        result = subprocess.run(["docker", "images", "-q", "tainoai"], capture_output=True, text=True)
        if result.stdout.strip():
            logger.info("✅ TainoAI Docker image found")
            return True
        else:
            logger.warning("⚠️ TainoAI Docker image not found")
            return False
    except Exception as e:
        logger.error(f"❌ Error checking Docker image: {e}")
        return False

def build_image():
    """Build TainoAI World-Class Docker image"""
    logger.info("🐳 Building TainoAI World-Class Docker image...")

    try:
        cmd = ["docker", "build", "-f", "Dockerfile.tainoai", "-t", "tainoai", "."]
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                 text=True, universal_newlines=True)

        # Stream output
        for line in process.stdout:
            print(line.strip())

        process.wait()

        if process.returncode == 0:
            logger.info("✅ TainoAI Docker image built successfully")
            return True
        else:
            logger.error("❌ TainoAI Docker build failed")
            return False

    except Exception as e:
        logger.error(f"❌ Error building TainoAI Docker image: {e}")
        return False

def stop_existing_container():
    """Stop any existing TainoAI container"""
    try:
        # Check if container is running
        result = subprocess.run(["docker", "ps", "-q", "-f", "name=tainoai"], 
                              capture_output=True, text=True)
        
        if result.stdout.strip():
            logger.info("🛑 Stopping existing TainoAI container...")
            subprocess.run(["docker", "stop", "tainoai"], capture_output=True)
            subprocess.run(["docker", "rm", "tainoai"], capture_output=True)
            logger.info("✅ Existing container stopped")
            
    except Exception as e:
        logger.warning(f"⚠️ Error stopping existing container: {e}")

def run_container():
    """Run TainoAI Docker container"""
    logger.info("🚀 Starting TainoAI container...")
    
    try:
        cmd = [
            "docker", "run", 
            "-d",  # Detached mode
            "-p", "8000:8000",  # Port mapping
            "--name", "tainoai",  # Container name
            "tainoai"  # Image name
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            container_id = result.stdout.strip()
            logger.info(f"✅ Container started: {container_id[:12]}")
            return True
        else:
            logger.error(f"❌ Failed to start container: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error starting container: {e}")
        return False

def wait_for_startup():
    """Wait for TainoAI to start up"""
    logger.info("⏳ Waiting for TainoAI to start up...")
    
    max_attempts = 30
    for attempt in range(max_attempts):
        try:
            import requests
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                logger.info("✅ TainoAI is ready!")
                return True
        except:
            pass
        
        time.sleep(2)
        print(f"⏳ Attempt {attempt + 1}/{max_attempts}...")
    
    logger.error("❌ TainoAI failed to start within timeout")
    return False

def show_logs():
    """Show container logs"""
    logger.info("📋 Showing TainoAI logs...")
    try:
        subprocess.run(["docker", "logs", "-f", "tainoai"])
    except KeyboardInterrupt:
        logger.info("📋 Log viewing stopped")

def main():
    """Main function"""
    print_banner()
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--logs":
            show_logs()
            return
        elif sys.argv[1] == "--stop":
            stop_existing_container()
            logger.info("🛑 TainoAI stopped")
            return
        elif sys.argv[1] == "--build":
            if not check_docker():
                sys.exit(1)
            if build_image():
                logger.info("✅ Build complete")
            else:
                sys.exit(1)
            return
    
    # Check prerequisites
    if not check_docker():
        sys.exit(1)
    
    # Stop any existing container
    stop_existing_container()
    
    # Check if image exists, build if needed
    if not check_image():
        logger.info("🔨 Building TainoAI image...")
        if not build_image():
            sys.exit(1)
    
    # Run container
    if not run_container():
        sys.exit(1)
    
    # Wait for startup
    if not wait_for_startup():
        logger.error("❌ Startup failed. Check logs with: python run_local.py --logs")
        sys.exit(1)
    
    # Success!
    logger.info("🎉 TainoAI is running successfully!")
    logger.info("🌐 Open your browser to: http://localhost:8000")
    logger.info("📋 View logs with: python run_local.py --logs")
    logger.info("🛑 Stop with: python run_local.py --stop")
    
    # Optionally open browser
    try:
        webbrowser.open("http://localhost:8000")
        logger.info("🌐 Browser opened automatically")
    except:
        logger.info("🌐 Please open http://localhost:8000 in your browser")

if __name__ == "__main__":
    main()
