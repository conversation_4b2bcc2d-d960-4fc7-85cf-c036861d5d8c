# TainoAI - World-Class AI Assistant
# Multi-model deployment with LLaMA 2 7B Chat + Mistral 7B
# Optimized for scalability and production deployment

# Stage 1: Build llama.cpp with optimizations
FROM ubuntu:22.04 AS llama-builder

# Install build dependencies with optimizations
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    python3 \
    python3-pip \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Clone and build llama.cpp with optimizations
WORKDIR /build
RUN git clone https://github.com/ggerganov/llama.cpp.git
WORKDIR /build/llama.cpp

# Build with optimizations for production
RUN make -j$(nproc) main server quantize \
    LLAMA_OPENBLAS=1 \
    LLAMA_NATIVE=1 \
    LLAMA_FAST=1

# Create organized model directory structure
RUN mkdir -p /app/models/llama-2-7b-chat /app/models/mistral-7b /app/llama.cpp /app/data

# Download LLaMA 2 7B Chat Model (3.8GB)
RUN echo "Downloading LLaMA 2 7B Chat Model (3.8GB)..." && \
    wget --progress=bar:force:noscroll -O /app/models/llama-2-7b-chat/llama-2-7b-chat.Q4_K_M.gguf \
    "https://huggingface.co/TheBloke/Llama-2-7B-Chat-GGUF/resolve/main/llama-2-7b-chat.Q4_K_M.gguf" && \
    echo "LLaMA 2 download complete: $(du -h /app/models/llama-2-7b-chat/llama-2-7b-chat.Q4_K_M.gguf)"

# Download Mistral 7B Model (4.1GB)
RUN echo "Downloading Mistral 7B Model (4.1GB)..." && \
    wget --progress=bar:force:noscroll -O /app/models/mistral-7b/mistral-7b-instruct-v0.1.Q4_K_M.gguf \
    "https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.1-GGUF/resolve/main/mistral-7b-instruct-v0.1.Q4_K_M.gguf" && \
    echo "Mistral 7B download complete: $(du -h /app/models/mistral-7b/mistral-7b-instruct-v0.1.Q4_K_M.gguf)"

# Copy llama.cpp binaries to organized structure
RUN cp main server quantize /app/llama.cpp/ && \
    chmod +x /app/llama.cpp/* && \
    echo "Total models size: $(du -sh /app/models)"

# Stage 2: Build React Frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./
COPY next.config.js ./
COPY tailwind.config.js ./
COPY postcss.config.js ./

# Install dependencies
RUN npm ci --only=production --no-audit --no-fund

# Copy source code
COPY app/ ./app/
COPY components/ ./components/
COPY lib/ ./lib/

# Build and export static frontend
RUN npm run build && npm run export

# Stage 3: Production Runtime - Organized & Optimized
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies with optimizations
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    htop \
    procps \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy requirements and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy organized llama.cpp structure from builder
COPY --from=llama-builder /app/llama.cpp ./llama.cpp/
COPY --from=llama-builder /app/models ./models/
COPY --from=llama-builder /app/data ./data/

# Copy built frontend (keeping your design intact)
COPY --from=frontend-builder /app/out/ ./static/

# Copy backend code in organized structure
COPY main.py ./
COPY llama_engine.py ./
COPY document_processor.py ./
COPY download_model.py ./

# Create organized directory structure
RUN mkdir -p data/conversations data/documents data/uploads && \
    mkdir -p logs && \
    chmod +x ./llama.cpp/* && \
    ls -la ./models/ && \
    echo "Model size: $(du -h ./models/mistral-7b-instruct-v0.1.Q4_K_M.gguf)"

# Set optimized environment variables for TainoAI World-Class AI
ENV PRIMARY_MODEL_PATH=./models/mistral-7b/mistral-7b-instruct-v0.1.Q4_K_M.gguf
ENV SECONDARY_MODEL_PATH=./models/llama-2-7b-chat/llama-2-7b-chat.Q4_K_M.gguf
ENV PRIMARY_MODEL_TYPE=mistral-7b
ENV SECONDARY_MODEL_TYPE=llama-2-7b-chat
ENV LLAMA_BINARY=./llama.cpp/main
ENV LLAMA_SERVER_BINARY=./llama.cpp/server
ENV LLAMA_THREADS=2
ENV LLAMA_CONTEXT_SIZE=2048
ENV LLAMA_MAX_TOKENS=512
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV TAINOAI_MODE=WORLD_CLASS_AI
ENV TAINOAI_VERSION=9.0.0

# Expose port (Heroku will set PORT dynamically)
EXPOSE 8000

# Enhanced health check for world-class AI system (disabled for Heroku)
# HEALTHCHECK --interval=60s --timeout=30s --start-period=60s --retries=3 \
#     CMD curl -f http://localhost:8000/health || exit 1

# Run TainoAI with world-class AI power
CMD ["python", "main.py"]
