/**
 * TainoAI Knowledge Manager - Document Learning and Retrieval
 */

import { DocumentChunk, KnowledgeBase } from '../types';
import { MemoryManager } from './MemoryManager';
import fs from 'fs';
import path from 'path';

export class KnowledgeManager {
  private memoryManager: MemoryManager;
  private knowledgeBases: Map<string, KnowledgeBase> = new Map();
  private isInitialized = false;

  constructor() {
    this.memoryManager = new MemoryManager('./data/knowledge.db');
  }

  async initialize(): Promise<void> {
    try {
      await this.memoryManager.initialize();
      await this.loadExistingKnowledge();
      this.isInitialized = true;
      console.log('✅ Knowledge Manager initialized');
    } catch (error) {
      console.error('❌ Failed to initialize Knowledge Manager:', error);
      throw error;
    }
  }

  private async loadExistingKnowledge(): Promise<void> {
    try {
      const chunks = await this.memoryManager.getKnowledgeChunks();
      console.log(`📚 Loaded ${chunks.length} knowledge chunks`);
      
      // Group chunks by source into knowledge bases
      const sourceGroups: Record<string, any[]> = {};
      chunks.forEach(chunk => {
        if (!sourceGroups[chunk.source]) {
          sourceGroups[chunk.source] = [];
        }
        sourceGroups[chunk.source].push(chunk);
      });

      // Create knowledge bases
      for (const [source, sourceChunks] of Object.entries(sourceGroups)) {
        const kb: KnowledgeBase = {
          id: this.generateId(),
          name: source,
          documents: sourceChunks.map(chunk => ({
            id: chunk.id,
            content: chunk.content,
            embedding: chunk.embedding,
            metadata: {
              source: chunk.source,
              timestamp: chunk.createdAt,
              ...chunk.metadata,
            },
          })),
          embeddings: new Map(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        this.knowledgeBases.set(source, kb);
      }

    } catch (error) {
      console.warn('⚠️ Could not load existing knowledge:', error);
    }
  }

  async addDocument(
    content: string,
    source: string,
    metadata: any = {}
  ): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Knowledge Manager not initialized');
    }

    console.log(`📄 Adding document from: ${source}`);

    try {
      // Split content into chunks
      const chunks = this.chunkDocument(content, source, metadata);

      // Generate embeddings for chunks
      const chunksWithEmbeddings = await Promise.all(
        chunks.map(async chunk => ({
          ...chunk,
          embedding: await this.generateEmbedding(chunk.content),
        }))
      );

      // Store chunks in memory
      for (const chunk of chunksWithEmbeddings) {
        await this.memoryManager.storeKnowledgeChunk(
          chunk.id,
          chunk.content,
          chunk.metadata.source,
          chunk.embedding,
          chunk.metadata
        );
      }

      // Update or create knowledge base
      await this.updateKnowledgeBase(source, chunksWithEmbeddings);

      console.log(`✅ Added ${chunks.length} chunks from ${source}`);

    } catch (error) {
      console.error('❌ Failed to add document:', error);
      throw error;
    }
  }

  private chunkDocument(
    content: string,
    source: string,
    metadata: any
  ): DocumentChunk[] {
    const chunks: DocumentChunk[] = [];
    const chunkSize = 500; // Characters per chunk
    const overlap = 50; // Character overlap between chunks

    // Split by paragraphs first
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    
    let currentChunk = '';
    let chunkIndex = 0;

    for (const paragraph of paragraphs) {
      if (currentChunk.length + paragraph.length > chunkSize && currentChunk.length > 0) {
        // Create chunk
        chunks.push({
          id: `${source}_chunk_${chunkIndex}`,
          content: currentChunk.trim(),
          metadata: {
            source,
            chunkIndex,
            timestamp: new Date(),
            ...metadata,
          },
        });

        // Start new chunk with overlap
        const words = currentChunk.split(' ');
        const overlapWords = words.slice(-Math.floor(overlap / 5)); // Approximate word overlap
        currentChunk = overlapWords.join(' ') + ' ' + paragraph;
        chunkIndex++;
      } else {
        currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
      }
    }

    // Add final chunk
    if (currentChunk.trim().length > 0) {
      chunks.push({
        id: `${source}_chunk_${chunkIndex}`,
        content: currentChunk.trim(),
        metadata: {
          source,
          chunkIndex,
          timestamp: new Date(),
          ...metadata,
        },
      });
    }

    return chunks;
  }

  private async generateEmbedding(text: string): Promise<number[]> {
    // Simple TF-IDF-like embedding for now
    // In production, you'd use a proper embedding model
    
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);

    const vocabulary = [...new Set(words)];
    const embedding = new Array(100).fill(0); // Fixed size embedding

    // Simple hash-based embedding
    vocabulary.forEach((word, index) => {
      const hash = this.simpleHash(word);
      const embeddingIndex = Math.abs(hash) % embedding.length;
      embedding[embeddingIndex] += 1 / vocabulary.length;
    });

    // Normalize
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => magnitude > 0 ? val / magnitude : 0);
  }

  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash;
  }

  private async updateKnowledgeBase(
    source: string,
    chunks: DocumentChunk[]
  ): Promise<void> {
    let kb = this.knowledgeBases.get(source);

    if (!kb) {
      kb = {
        id: this.generateId(),
        name: source,
        documents: [],
        embeddings: new Map(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    }

    // Add new chunks
    kb.documents.push(...chunks);
    kb.updatedAt = new Date();

    // Update embeddings map
    chunks.forEach(chunk => {
      if (chunk.embedding) {
        kb!.embeddings.set(chunk.id, chunk.embedding);
      }
    });

    this.knowledgeBases.set(source, kb);
  }

  async searchSimilar(
    query: string,
    maxResults: number = 5,
    source?: string
  ): Promise<DocumentChunk[]> {
    if (!this.isInitialized) {
      throw new Error('Knowledge Manager not initialized');
    }

    try {
      // Generate query embedding
      const queryEmbedding = await this.generateEmbedding(query);

      // Get all relevant chunks
      const allChunks: Array<DocumentChunk & { similarity: number }> = [];

      for (const [kbSource, kb] of this.knowledgeBases) {
        if (source && kbSource !== source) continue;

        for (const chunk of kb.documents) {
          if (chunk.embedding) {
            const similarity = this.cosineSimilarity(queryEmbedding, chunk.embedding);
            allChunks.push({ ...chunk, similarity });
          }
        }
      }

      // Sort by similarity and return top results
      return allChunks
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, maxResults)
        .map(({ similarity, ...chunk }) => chunk);

    } catch (error) {
      console.error('❌ Search failed:', error);
      return [];
    }
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0;

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    if (normA === 0 || normB === 0) return 0;

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  async searchKeywords(
    keywords: string[],
    maxResults: number = 10,
    source?: string
  ): Promise<DocumentChunk[]> {
    if (!this.isInitialized) {
      throw new Error('Knowledge Manager not initialized');
    }

    const results: Array<DocumentChunk & { score: number }> = [];

    for (const [kbSource, kb] of this.knowledgeBases) {
      if (source && kbSource !== source) continue;

      for (const chunk of kb.documents) {
        let score = 0;
        const contentLower = chunk.content.toLowerCase();

        keywords.forEach(keyword => {
          const keywordLower = keyword.toLowerCase();
          const matches = (contentLower.match(new RegExp(keywordLower, 'g')) || []).length;
          score += matches * keyword.length;
        });

        if (score > 0) {
          results.push({ ...chunk, score });
        }
      }
    }

    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, maxResults)
      .map(({ score, ...chunk }) => chunk);
  }

  async getKnowledgeBase(source: string): Promise<KnowledgeBase | undefined> {
    return this.knowledgeBases.get(source);
  }

  async getAllKnowledgeBases(): Promise<KnowledgeBase[]> {
    return Array.from(this.knowledgeBases.values());
  }

  async deleteKnowledgeBase(source: string): Promise<void> {
    const kb = this.knowledgeBases.get(source);
    if (!kb) return;

    // Delete from memory
    for (const chunk of kb.documents) {
      // Note: MemoryManager doesn't have a delete method yet
      // This would need to be implemented
    }

    this.knowledgeBases.delete(source);
    console.log(`🗑️ Deleted knowledge base: ${source}`);
  }

  async getStats(): Promise<any> {
    const stats = {
      knowledgeBases: this.knowledgeBases.size,
      totalChunks: 0,
      totalSources: new Set<string>(),
      averageChunkSize: 0,
      oldestDocument: null as Date | null,
      newestDocument: null as Date | null,
    };

    let totalCharacters = 0;

    for (const kb of this.knowledgeBases.values()) {
      stats.totalChunks += kb.documents.length;
      stats.totalSources.add(kb.name);

      for (const chunk of kb.documents) {
        totalCharacters += chunk.content.length;

        const timestamp = chunk.metadata.timestamp;
        if (timestamp instanceof Date) {
          if (!stats.oldestDocument || timestamp < stats.oldestDocument) {
            stats.oldestDocument = timestamp;
          }
          if (!stats.newestDocument || timestamp > stats.newestDocument) {
            stats.newestDocument = timestamp;
          }
        }
      }
    }

    stats.averageChunkSize = stats.totalChunks > 0 ? 
      Math.round(totalCharacters / stats.totalChunks) : 0;

    return {
      ...stats,
      totalSources: stats.totalSources.size,
    };
  }

  private generateId(): string {
    return `kb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async close(): Promise<void> {
    if (this.memoryManager) {
      await this.memoryManager.close();
    }
  }
}

export default KnowledgeManager;
