{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=false!"]}