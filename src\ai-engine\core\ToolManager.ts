/**
 * TainoAI Tool Manager - ReAct-style Tool Orchestration
 */

import { AITool, ReActStep } from '../types';

export class ToolManager {
  private tools: Map<string, AITool> = new Map();
  private executionHistory: ReActStep[] = [];

  addTool(tool: AITool): void {
    this.tools.set(tool.name, tool);
    console.log(`🔧 Tool registered: ${tool.name}`);
  }

  getTool(name: string): AITool | undefined {
    return this.tools.get(name);
  }

  getAllTools(): AITool[] {
    return Array.from(this.tools.values());
  }

  getToolDescriptions(): string[] {
    return Array.from(this.tools.values()).map(tool => 
      `${tool.name}: ${tool.description}`
    );
  }

  async executeTool(name: string, parameters: any): Promise<any> {
    const tool = this.tools.get(name);
    if (!tool) {
      throw new Error(`Tool not found: ${name}`);
    }

    console.log(`🔧 Executing tool: ${name} with params:`, parameters);
    
    try {
      const result = await tool.execute(parameters);
      console.log(`✅ Tool ${name} completed successfully`);
      return result;
    } catch (error) {
      console.error(`❌ Tool ${name} failed:`, error);
      throw error;
    }
  }

  // ReAct-style reasoning and tool execution
  async executeReActChain(
    query: string,
    maxSteps: number = 5,
    onStep?: (step: ReActStep) => void
  ): Promise<{ result: string; steps: ReActStep[] }> {
    const steps: ReActStep[] = [];
    let currentQuery = query;
    let finalResult = '';

    for (let i = 0; i < maxSteps; i++) {
      // Thought phase
      const thought = await this.generateThought(currentQuery, steps);
      
      // Action phase
      const action = await this.selectAction(thought, currentQuery);
      
      // Execute action
      let observation = '';
      try {
        if (action.startsWith('TOOL:')) {
          const toolCall = this.parseToolCall(action);
          const result = await this.executeTool(toolCall.name, toolCall.parameters);
          observation = `Tool ${toolCall.name} returned: ${JSON.stringify(result)}`;
        } else if (action.startsWith('ANSWER:')) {
          finalResult = action.replace('ANSWER:', '').trim();
          observation = 'Final answer provided';
        } else {
          observation = 'No specific action taken';
        }
      } catch (error) {
        observation = `Error: ${error}`;
      }

      // Reasoning phase
      const reasoning = await this.generateReasoning(thought, action, observation);

      const step: ReActStep = {
        thought,
        action,
        observation,
        reasoning,
      };

      steps.push(step);
      this.executionHistory.push(step);

      if (onStep) {
        onStep(step);
      }

      // Check if we have a final answer
      if (finalResult) {
        break;
      }

      // Update query for next iteration
      currentQuery = `${query}\n\nPrevious steps:\n${this.formatSteps(steps)}`;
    }

    return {
      result: finalResult || 'Unable to complete the task within the step limit',
      steps,
    };
  }

  private async generateThought(query: string, previousSteps: ReActStep[]): Promise<string> {
    // Simple heuristic-based thought generation
    // In a full implementation, this would use the AI model
    
    if (previousSteps.length === 0) {
      return `I need to understand what the user is asking: "${query}". Let me think about what tools or information I need.`;
    }

    const lastStep = previousSteps[previousSteps.length - 1];
    if (lastStep.observation.includes('Error')) {
      return 'The previous action failed. I need to try a different approach.';
    }

    if (lastStep.observation.includes('Tool')) {
      return 'I got some information from the tool. Let me analyze it and decide what to do next.';
    }

    return 'Let me continue working on this step by step.';
  }

  private async selectAction(thought: string, query: string): Promise<string> {
    // Simple action selection based on query analysis
    const queryLower = query.toLowerCase();

    // Check for web search needs
    if (queryLower.includes('search') || queryLower.includes('find') || 
        queryLower.includes('latest') || queryLower.includes('current')) {
      return 'TOOL:web_search({"query": "' + this.extractSearchQuery(query) + '"})';
    }

    // Check for code execution needs
    if (queryLower.includes('calculate') || queryLower.includes('compute') ||
        queryLower.includes('run') || queryLower.includes('execute')) {
      return 'TOOL:code_executor({"code": "' + this.extractCode(query) + '", "language": "python"})';
    }

    // Check for document processing needs
    if (queryLower.includes('document') || queryLower.includes('file') ||
        queryLower.includes('analyze') || queryLower.includes('summarize')) {
      return 'TOOL:document_processor({"action": "analyze", "content": "' + query + '"})';
    }

    // Default to providing an answer
    return `ANSWER: Based on my knowledge, ${this.generateBasicAnswer(query)}`;
  }

  private extractSearchQuery(query: string): string {
    // Extract search terms from the query
    const searchWords = ['search', 'find', 'lookup', 'latest', 'current'];
    const words = query.split(' ');
    
    // Remove search command words and return the rest
    const filteredWords = words.filter(word => 
      !searchWords.includes(word.toLowerCase())
    );
    
    return filteredWords.join(' ').trim();
  }

  private extractCode(query: string): string {
    // Extract code from the query
    const codeMatch = query.match(/```[\s\S]*?```/);
    if (codeMatch) {
      return codeMatch[0].replace(/```/g, '').trim();
    }
    
    // If no code block, return the query as potential code
    return query;
  }

  private generateBasicAnswer(query: string): string {
    // Generate a basic answer when no tools are needed
    return `I understand you're asking about "${query}". Let me provide you with a thoughtful response based on my knowledge.`;
  }

  private async generateReasoning(thought: string, action: string, observation: string): Promise<string> {
    // Generate reasoning about the step
    if (observation.includes('Error')) {
      return 'This approach didn\'t work. I should try a different method or tool.';
    }

    if (action.startsWith('TOOL:')) {
      return 'I used a tool to gather information. This should help me provide a better answer.';
    }

    if (action.startsWith('ANSWER:')) {
      return 'I provided a final answer based on the available information and reasoning.';
    }

    return 'I\'m continuing to work through this step by step.';
  }

  private parseToolCall(action: string): { name: string; parameters: any } {
    // Parse tool call from action string
    // Format: TOOL:tool_name({"param": "value"})
    
    const match = action.match(/TOOL:(\w+)\((.*)\)/);
    if (!match) {
      throw new Error(`Invalid tool call format: ${action}`);
    }

    const name = match[1];
    const paramString = match[2];

    try {
      const parameters = JSON.parse(paramString);
      return { name, parameters };
    } catch (error) {
      throw new Error(`Invalid tool parameters: ${paramString}`);
    }
  }

  private formatSteps(steps: ReActStep[]): string {
    return steps.map((step, i) => 
      `Step ${i + 1}:\nThought: ${step.thought}\nAction: ${step.action}\nObservation: ${step.observation}\nReasoning: ${step.reasoning}\n`
    ).join('\n');
  }

  // Tool validation and management
  validateTool(tool: AITool): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!tool.name || tool.name.trim().length === 0) {
      issues.push('Tool name is required');
    }

    if (!tool.description || tool.description.trim().length === 0) {
      issues.push('Tool description is required');
    }

    if (typeof tool.execute !== 'function') {
      issues.push('Tool must have an execute function');
    }

    if (this.tools.has(tool.name)) {
      issues.push(`Tool with name '${tool.name}' already exists`);
    }

    return {
      isValid: issues.length === 0,
      issues,
    };
  }

  removeTool(name: string): boolean {
    return this.tools.delete(name);
  }

  getExecutionHistory(): ReActStep[] {
    return [...this.executionHistory];
  }

  clearExecutionHistory(): void {
    this.executionHistory = [];
  }

  // Tool performance monitoring
  getToolUsageStats(): Record<string, { calls: number; successes: number; failures: number }> {
    const stats: Record<string, { calls: number; successes: number; failures: number }> = {};
    
    for (const step of this.executionHistory) {
      if (step.action.startsWith('TOOL:')) {
        const toolCall = this.parseToolCall(step.action);
        const toolName = toolCall.name;
        
        if (!stats[toolName]) {
          stats[toolName] = { calls: 0, successes: 0, failures: 0 };
        }
        
        stats[toolName].calls++;
        
        if (step.observation.includes('Error')) {
          stats[toolName].failures++;
        } else {
          stats[toolName].successes++;
        }
      }
    }
    
    return stats;
  }
}

export default ToolManager;
