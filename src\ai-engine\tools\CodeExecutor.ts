/**
 * TainoAI Code Executor - Safe Code Execution Environment
 */

import { AITool } from '../types';
import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const writeFile = promisify(fs.writeFile);
const unlink = promisify(fs.unlink);

export class CodeExecutor implements AITool {
  name = 'code_executor';
  description = 'Execute code safely in various programming languages';
  parameters = {
    code: { type: 'string', description: 'Code to execute' },
    language: { 
      type: 'string', 
      description: 'Programming language: python, javascript, bash, etc.',
      enum: ['python', 'javascript', 'bash', 'node']
    },
    timeout: { type: 'number', description: 'Execution timeout in seconds (default: 10)' },
    args: { type: 'array', description: 'Command line arguments' },
  };

  private tempDir = './temp';

  constructor() {
    this.ensureTempDir();
  }

  private ensureTempDir(): void {
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  async execute(params: {
    code: string;
    language: 'python' | 'javascript' | 'bash' | 'node';
    timeout?: number;
    args?: string[];
  }): Promise<any> {
    const { code, language, timeout = 10, args = [] } = params;

    console.log(`⚡ Executing ${language} code`);

    try {
      // Validate code for safety
      this.validateCode(code, language);

      // Execute based on language
      switch (language) {
        case 'python':
          return await this.executePython(code, timeout, args);
        case 'javascript':
        case 'node':
          return await this.executeJavaScript(code, timeout, args);
        case 'bash':
          return await this.executeBash(code, timeout, args);
        default:
          throw new Error(`Unsupported language: ${language}`);
      }
    } catch (error) {
      console.error('❌ Code execution failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        output: '',
        stderr: '',
        executionTime: 0,
      };
    }
  }

  private validateCode(code: string, language: string): void {
    // Basic security checks
    const dangerousPatterns = [
      /import\s+os/i,
      /import\s+subprocess/i,
      /import\s+sys/i,
      /require\s*\(\s*['"]fs['"]\s*\)/i,
      /require\s*\(\s*['"]child_process['"]\s*\)/i,
      /require\s*\(\s*['"]os['"]\s*\)/i,
      /eval\s*\(/i,
      /exec\s*\(/i,
      /system\s*\(/i,
      /shell_exec/i,
      /file_get_contents/i,
      /file_put_contents/i,
      /fopen/i,
      /fwrite/i,
      /unlink/i,
      /rmdir/i,
      /mkdir/i,
      /chmod/i,
      /\.\.\/|\.\.\\/, // Path traversal
      /\/etc\/|\/proc\/|\/sys\//, // System directories
      /rm\s+-rf/i,
      /del\s+\/s/i,
      /format\s+c:/i,
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(code)) {
        throw new Error(`Potentially dangerous code detected: ${pattern.source}`);
      }
    }

    // Language-specific validations
    if (language === 'python') {
      this.validatePythonCode(code);
    } else if (language === 'javascript' || language === 'node') {
      this.validateJavaScriptCode(code);
    } else if (language === 'bash') {
      this.validateBashCode(code);
    }
  }

  private validatePythonCode(code: string): void {
    const pythonDangerous = [
      /__import__/i,
      /getattr/i,
      /setattr/i,
      /delattr/i,
      /globals\s*\(/i,
      /locals\s*\(/i,
      /vars\s*\(/i,
      /dir\s*\(/i,
      /open\s*\(/i,
      /input\s*\(/i,
      /raw_input\s*\(/i,
    ];

    for (const pattern of pythonDangerous) {
      if (pattern.test(code)) {
        throw new Error(`Dangerous Python operation detected: ${pattern.source}`);
      }
    }
  }

  private validateJavaScriptCode(code: string): void {
    const jsDangerous = [
      /process\./i,
      /global\./i,
      /Buffer\./i,
      /require\s*\(/i,
      /import\s+/i,
      /fetch\s*\(/i,
      /XMLHttpRequest/i,
      /WebSocket/i,
      /localStorage/i,
      /sessionStorage/i,
      /document\./i,
      /window\./i,
    ];

    for (const pattern of jsDangerous) {
      if (pattern.test(code)) {
        throw new Error(`Dangerous JavaScript operation detected: ${pattern.source}`);
      }
    }
  }

  private validateBashCode(code: string): void {
    const bashDangerous = [
      /sudo/i,
      /su\s/i,
      /passwd/i,
      /useradd/i,
      /userdel/i,
      /groupadd/i,
      /groupdel/i,
      /mount/i,
      /umount/i,
      /fdisk/i,
      /mkfs/i,
      /dd\s/i,
      /wget/i,
      /curl/i,
      /nc\s/i,
      /netcat/i,
      /ssh/i,
      /scp/i,
      /rsync/i,
    ];

    for (const pattern of bashDangerous) {
      if (pattern.test(code)) {
        throw new Error(`Dangerous bash command detected: ${pattern.source}`);
      }
    }
  }

  private async executePython(code: string, timeout: number, args: string[]): Promise<any> {
    const filename = `temp_${Date.now()}.py`;
    const filepath = path.join(this.tempDir, filename);

    try {
      // Write code to temporary file
      await writeFile(filepath, code);

      // Execute Python code
      const result = await this.runCommand('python3', [filepath, ...args], timeout);

      return {
        success: true,
        output: result.stdout,
        stderr: result.stderr,
        exitCode: result.exitCode,
        executionTime: result.executionTime,
        language: 'python',
      };

    } finally {
      // Clean up temporary file
      try {
        await unlink(filepath);
      } catch (error) {
        console.warn('Failed to clean up temp file:', filepath);
      }
    }
  }

  private async executeJavaScript(code: string, timeout: number, args: string[]): Promise<any> {
    const filename = `temp_${Date.now()}.js`;
    const filepath = path.join(this.tempDir, filename);

    try {
      // Wrap code in safe environment
      const wrappedCode = `
(function() {
  'use strict';
  
  // Disable dangerous globals
  const process = undefined;
  const global = undefined;
  const Buffer = undefined;
  const require = undefined;
  
  // Safe console for output
  const console = {
    log: (...args) => process.stdout.write(args.join(' ') + '\\n'),
    error: (...args) => process.stderr.write(args.join(' ') + '\\n'),
  };
  
  try {
    ${code}
  } catch (error) {
    console.error('Error:', error.message);
  }
})();
`;

      await writeFile(filepath, wrappedCode);

      // Execute JavaScript code
      const result = await this.runCommand('node', [filepath, ...args], timeout);

      return {
        success: true,
        output: result.stdout,
        stderr: result.stderr,
        exitCode: result.exitCode,
        executionTime: result.executionTime,
        language: 'javascript',
      };

    } finally {
      // Clean up temporary file
      try {
        await unlink(filepath);
      } catch (error) {
        console.warn('Failed to clean up temp file:', filepath);
      }
    }
  }

  private async executeBash(code: string, timeout: number, args: string[]): Promise<any> {
    const filename = `temp_${Date.now()}.sh`;
    const filepath = path.join(this.tempDir, filename);

    try {
      // Add safety header to bash script
      const safeCode = `#!/bin/bash
set -e
set -u
set -o pipefail

# Restrict to current directory
cd "${this.tempDir}"

${code}
`;

      await writeFile(filepath, safeCode);

      // Make executable
      fs.chmodSync(filepath, 0o755);

      // Execute bash script
      const result = await this.runCommand('bash', [filepath, ...args], timeout);

      return {
        success: true,
        output: result.stdout,
        stderr: result.stderr,
        exitCode: result.exitCode,
        executionTime: result.executionTime,
        language: 'bash',
      };

    } finally {
      // Clean up temporary file
      try {
        await unlink(filepath);
      } catch (error) {
        console.warn('Failed to clean up temp file:', filepath);
      }
    }
  }

  private runCommand(
    command: string, 
    args: string[], 
    timeout: number
  ): Promise<{
    stdout: string;
    stderr: string;
    exitCode: number;
    executionTime: number;
  }> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      let stdout = '';
      let stderr = '';

      const child = spawn(command, args, {
        cwd: this.tempDir,
        env: {
          ...process.env,
          PATH: process.env.PATH,
          // Restrict environment
          HOME: this.tempDir,
          TMPDIR: this.tempDir,
        },
        stdio: ['pipe', 'pipe', 'pipe'],
      });

      // Set timeout
      const timeoutId = setTimeout(() => {
        child.kill('SIGKILL');
        reject(new Error(`Execution timeout after ${timeout} seconds`));
      }, timeout * 1000);

      // Collect output
      child.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        clearTimeout(timeoutId);
        const executionTime = Date.now() - startTime;

        resolve({
          stdout: stdout.trim(),
          stderr: stderr.trim(),
          exitCode: code || 0,
          executionTime,
        });
      });

      child.on('error', (error) => {
        clearTimeout(timeoutId);
        reject(error);
      });
    });
  }

  // Utility methods for common calculations
  async executeCalculation(expression: string): Promise<any> {
    const pythonCode = `
import math

# Safe evaluation of mathematical expressions
def safe_eval(expr):
    # Allow only safe mathematical operations
    allowed_names = {
        'abs': abs, 'round': round, 'min': min, 'max': max,
        'sum': sum, 'pow': pow, 'sqrt': math.sqrt,
        'sin': math.sin, 'cos': math.cos, 'tan': math.tan,
        'log': math.log, 'log10': math.log10, 'exp': math.exp,
        'pi': math.pi, 'e': math.e,
    }
    
    # Remove any dangerous characters
    safe_expr = ''.join(c for c in expr if c.isalnum() or c in '+-*/()., ')
    
    try:
        result = eval(safe_expr, {"__builtins__": {}}, allowed_names)
        return result
    except Exception as e:
        return f"Error: {e}"

result = safe_eval("${expression}")
print(f"Result: {result}")
`;

    return await this.executePython(pythonCode, 5, []);
  }

  async executeDataAnalysis(data: any[], operation: string): Promise<any> {
    const pythonCode = `
import json

# Load data
data = ${JSON.stringify(data)}

# Perform analysis
if "${operation}" == "mean":
    result = sum(data) / len(data) if data else 0
elif "${operation}" == "median":
    sorted_data = sorted(data)
    n = len(sorted_data)
    result = sorted_data[n//2] if n % 2 == 1 else (sorted_data[n//2-1] + sorted_data[n//2]) / 2
elif "${operation}" == "mode":
    from collections import Counter
    counter = Counter(data)
    result = counter.most_common(1)[0][0] if counter else None
elif "${operation}" == "sum":
    result = sum(data)
elif "${operation}" == "count":
    result = len(data)
else:
    result = "Unknown operation"

print(f"Operation: ${operation}")
print(f"Data: {data}")
print(f"Result: {result}")
`;

    return await this.executePython(pythonCode, 10, []);
  }
}

export default CodeExecutor;
