# TainoAI - World-Class AI Assistant
# Docker Compose for development and production deployment

version: '3.8'

services:
  tainoai:
    build:
      context: .
      dockerfile: Dockerfile.tainoai
    container_name: tainoai-world-class
    ports:
      - "8000:8000"
    environment:
      # TainoAI Configuration
      - TAINOAI_MODE=WORLD_CLASS_AI
      - TAINOAI_VERSION=1.0.11
      
      # Model Configuration
      - PRIMARY_MODEL_PATH=./models/mistral-7b/mistral-7b-instruct-v0.1.Q4_K_M.gguf
      - SECONDARY_MODEL_PATH=./models/llama-2-7b-chat/llama-2-7b-chat.Q4_K_M.gguf
      - PRIMARY_MODEL_TYPE=mistral-7b
      - SECONDARY_MODEL_TYPE=llama-2-7b-chat
      
      # Llama.cpp Configuration
      - LLAMA_BINARY=./llama.cpp/main
      - LLAMA_SERVER_BINARY=./llama.cpp/server
      - LLAMA_THREADS=4
      - LLAMA_CONTEXT_SIZE=4096
      - LLAMA_MAX_TOKENS=1024
      
      # Server Configuration
      - PORT=8000
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    
    volumes:
      # Persist conversation data
      - tainoai_data:/app/data
      - tainoai_logs:/app/logs
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # Resource limits for production
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

  # Optional: PostgreSQL for production data storage
  postgres:
    image: postgres:15-alpine
    container_name: tainoai-postgres
    environment:
      - POSTGRES_DB=tainoai
      - POSTGRES_USER=tainoai
      - POSTGRES_PASSWORD=tainoai_secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    profiles:
      - production

  # Optional: Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: tainoai-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    profiles:
      - production

volumes:
  tainoai_data:
    driver: local
  tainoai_logs:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    name: tainoai-network
