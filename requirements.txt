# TainoAI - Full AI Power for Heroku with Llama.cpp
# Optimized for Full Mistral 7B Model (3.9GB) deployment

# Core FastAPI stack (optimized)
fastapi==0.104.1
uvicorn==0.24.0
gunicorn==21.2.0
python-multipart==0.0.6

# Data models
pydantic==2.5.0

# HTTP requests for model downloading and web search
requests==2.31.0
duckduckgo-search==3.9.6

# Document processing (lightweight but capable)
PyPDF2==3.0.1
python-docx==1.1.0
openpyxl==3.1.2

# Additional utilities for full AI mode
pathlib2==2.3.7

# Performance and monitoring
psutil==5.9.6

# NO heavy ML dependencies (using llama.cpp instead):
# - No torch/tensorflow (llama.cpp handles AI)
# - No transformers (using quantized model)
# - No sentence-transformers (file-based search)
# - No pandas (lightweight data processing)

# Full AI power through llama.cpp binary + 3.9GB model
