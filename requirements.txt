# TainoAI - Ultra-Lightweight for Hero<PERSON> (<450MB)
# Optimized AI Assistant with Document Learning

# Core FastAPI stack (minimal)
fastapi==0.104.1
uvicorn==0.24.0
python-multipart==0.0.6

# Data models
pydantic==2.5.0

# HTTP requests (lightweight)
requests==2.31.0

# Document processing (lightweight alternatives)
PyPDF2==3.0.1
python-docx==1.1.0
openpyxl==3.1.2

# NO heavy dependencies:
# - No pandas (too large)
# - No sentence-transformers (huge ML models)
# - No aiohttp (FastAPI handles HTTP)
# - No beautifulsoup4 (requests only)
# - No duckduckgo-search (too heavy)

# File storage only - JSON based
