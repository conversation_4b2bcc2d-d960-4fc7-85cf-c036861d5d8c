/**
 * TainoAI Model Setup - Download and Configure AI Models
 */

import fs from 'fs';
import path from 'path';
import https from 'https';
import { promisify } from 'util';

const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);

interface ModelInfo {
  name: string;
  filename: string;
  url: string;
  size: string;
  description: string;
  type: 'llama' | 'mistral' | 'gemma' | 'phi';
}

const AVAILABLE_MODELS: ModelInfo[] = [
  {
    name: 'Mistral 7B Instruct',
    filename: 'mistral-7b-instruct-v0.1.Q4_K_M.gguf',
    url: 'https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.1-GGUF/resolve/main/mistral-7b-instruct-v0.1.Q4_K_M.gguf',
    size: '4.1GB',
    description: 'High-quality instruction-following model, excellent for conversations',
    type: 'mistral',
  },
  {
    name: 'LLaMA 2 7B Chat',
    filename: 'llama-2-7b-chat.Q4_K_M.gguf',
    url: 'https://huggingface.co/TheBloke/Llama-2-7B-Chat-GGUF/resolve/main/llama-2-7b-chat.Q4_K_M.gguf',
    size: '3.8GB',
    description: 'Meta\'s LLaMA 2 optimized for chat applications',
    type: 'llama',
  },
  {
    name: 'Phi-2',
    filename: 'phi-2.Q4_K_M.gguf',
    url: 'https://huggingface.co/TheBloke/phi-2-GGUF/resolve/main/phi-2.Q4_K_M.gguf',
    size: '1.6GB',
    description: 'Compact but powerful model, good for resource-constrained environments',
    type: 'phi',
  },
  {
    name: 'CodeLlama 7B Instruct',
    filename: 'codellama-7b-instruct.Q4_K_M.gguf',
    url: 'https://huggingface.co/TheBloke/CodeLlama-7B-Instruct-GGUF/resolve/main/codellama-7b-instruct.Q4_K_M.gguf',
    size: '3.8GB',
    description: 'Specialized for code generation and programming tasks',
    type: 'llama',
  },
];

class ModelSetup {
  private modelsDir = './models';

  async setup(): Promise<void> {
    console.log('🚀 TainoAI Model Setup');
    console.log('='.repeat(50));

    try {
      await this.ensureDirectories();
      await this.showAvailableModels();
      await this.downloadRecommendedModels();
      await this.createModelConfig();
      
      console.log('\n✅ Model setup complete!');
      console.log('🎯 TainoAI is ready to run with local AI models');
      
    } catch (error) {
      console.error('❌ Model setup failed:', error);
      process.exit(1);
    }
  }

  private async ensureDirectories(): Promise<void> {
    const dirs = [
      this.modelsDir,
      './data',
      './documents',
      './cache',
      './temp',
    ];

    for (const dir of dirs) {
      if (!fs.existsSync(dir)) {
        await mkdir(dir, { recursive: true });
        console.log(`📁 Created directory: ${dir}`);
      }
    }
  }

  private async showAvailableModels(): Promise<void> {
    console.log('\n📦 Available AI Models:');
    console.log('-'.repeat(50));

    AVAILABLE_MODELS.forEach((model, index) => {
      console.log(`${index + 1}. ${model.name}`);
      console.log(`   File: ${model.filename}`);
      console.log(`   Size: ${model.size}`);
      console.log(`   Type: ${model.type}`);
      console.log(`   Description: ${model.description}`);
      console.log('');
    });
  }

  private async downloadRecommendedModels(): Promise<void> {
    console.log('🔽 Downloading recommended models...');
    
    // Download Mistral 7B (primary model)
    const mistralModel = AVAILABLE_MODELS.find(m => m.type === 'mistral');
    if (mistralModel) {
      await this.downloadModel(mistralModel);
    }

    // Download Phi-2 (lightweight fallback)
    const phiModel = AVAILABLE_MODELS.find(m => m.type === 'phi');
    if (phiModel) {
      await this.downloadModel(phiModel);
    }
  }

  private async downloadModel(model: ModelInfo): Promise<void> {
    const filePath = path.join(this.modelsDir, model.filename);

    // Check if model already exists
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${model.name} already exists`);
      return;
    }

    console.log(`📥 Downloading ${model.name} (${model.size})...`);
    console.log(`   URL: ${model.url}`);

    try {
      await this.downloadFile(model.url, filePath);
      console.log(`✅ Downloaded ${model.name}`);
    } catch (error) {
      console.error(`❌ Failed to download ${model.name}:`, error);
      
      // Create a placeholder file with download instructions
      const instructions = `# ${model.name} Download Instructions

This model needs to be downloaded manually due to size constraints.

Model: ${model.name}
File: ${model.filename}
Size: ${model.size}
URL: ${model.url}

To download:
1. Visit the URL above
2. Download the file
3. Place it in the models directory: ${this.modelsDir}
4. Rename it to: ${model.filename}

Description: ${model.description}
`;

      await writeFile(filePath + '.instructions.txt', instructions);
      console.log(`📝 Created download instructions: ${filePath}.instructions.txt`);
    }
  }

  private downloadFile(url: string, filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const file = fs.createWriteStream(filePath);
      let downloadedBytes = 0;
      let totalBytes = 0;

      const request = https.get(url, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
          return;
        }

        totalBytes = parseInt(response.headers['content-length'] || '0', 10);
        
        response.on('data', (chunk) => {
          downloadedBytes += chunk.length;
          if (totalBytes > 0) {
            const progress = ((downloadedBytes / totalBytes) * 100).toFixed(1);
            process.stdout.write(`\r   Progress: ${progress}% (${this.formatBytes(downloadedBytes)}/${this.formatBytes(totalBytes)})`);
          }
        });

        response.pipe(file);

        file.on('finish', () => {
          file.close();
          console.log('\n   Download complete!');
          resolve();
        });

        file.on('error', (error) => {
          fs.unlink(filePath, () => {}); // Delete partial file
          reject(error);
        });
      });

      request.on('error', (error) => {
        fs.unlink(filePath, () => {}); // Delete partial file
        reject(error);
      });

      // Set timeout
      request.setTimeout(300000, () => { // 5 minutes
        request.destroy();
        reject(new Error('Download timeout'));
      });
    });
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private async createModelConfig(): Promise<void> {
    const config = {
      version: '1.0.11',
      models: {
        primary: {
          name: 'Mistral 7B Instruct',
          filename: 'mistral-7b-instruct-v0.1.Q4_K_M.gguf',
          type: 'mistral',
          contextSize: 2048,
          maxTokens: 512,
        },
        secondary: {
          name: 'LLaMA 2 7B Chat',
          filename: 'llama-2-7b-chat.Q4_K_M.gguf',
          type: 'llama',
          contextSize: 2048,
          maxTokens: 512,
        },
        fallback: {
          name: 'Phi-2',
          filename: 'phi-2.Q4_K_M.gguf',
          type: 'phi',
          contextSize: 1024,
          maxTokens: 256,
        },
      },
      performance: {
        threads: 4,
        temperature: 0.8,
        topP: 0.9,
        topK: 40,
      },
      features: {
        webSearch: true,
        documentLearning: true,
        codeExecution: true,
        voiceInput: false,
        imageAnalysis: false,
      },
      paths: {
        models: './models',
        data: './data',
        documents: './documents',
        cache: './cache',
        temp: './temp',
      },
    };

    const configPath = './tainoai-config.json';
    await writeFile(configPath, JSON.stringify(config, null, 2));
    console.log(`📝 Created configuration file: ${configPath}`);
  }

  async checkModels(): Promise<void> {
    console.log('🔍 Checking available models...');
    
    const modelFiles = fs.readdirSync(this.modelsDir)
      .filter(file => file.endsWith('.gguf'));

    if (modelFiles.length === 0) {
      console.log('❌ No models found. Run setup to download models.');
      return;
    }

    console.log(`✅ Found ${modelFiles.length} model(s):`);
    modelFiles.forEach(file => {
      const filePath = path.join(this.modelsDir, file);
      const stats = fs.statSync(filePath);
      console.log(`   - ${file} (${this.formatBytes(stats.size)})`);
    });
  }

  async listModels(): Promise<void> {
    console.log('📋 TainoAI Model Information:');
    console.log('='.repeat(50));

    await this.showAvailableModels();
    await this.checkModels();
  }
}

// CLI interface
async function main(): Promise<void> {
  const setup = new ModelSetup();
  const command = process.argv[2];

  switch (command) {
    case 'setup':
      await setup.setup();
      break;
    
    case 'check':
      await setup.checkModels();
      break;
    
    case 'list':
      await setup.listModels();
      break;
    
    default:
      console.log('TainoAI Model Setup');
      console.log('');
      console.log('Commands:');
      console.log('  setup  - Download and configure AI models');
      console.log('  check  - Check which models are available');
      console.log('  list   - List all available models');
      console.log('');
      console.log('Usage: npm run setup-models [command]');
      break;
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Error:', error);
    process.exit(1);
  });
}

export default ModelSetup;
