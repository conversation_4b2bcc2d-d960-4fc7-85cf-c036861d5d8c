"""
TainoAI - World-Class AI Assistant
Multi-model deployment with LLaMA 2 7B Chat + Mistral 7B
Optimized for scalability and production deployment
"""

import os
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any

from fastapi import FastAPI, HTTPException, BackgroundTasks, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel

# Configure enhanced logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = "default"
    user_id: Optional[str] = "user"
    temperature: Optional[float] = 0.8
    max_tokens: Optional[int] = 512

class ChatResponse(BaseModel):
    response: str
    conversation_id: str
    timestamp: str
    processing_time: float
    ai_model_info: Dict[str, Any]  # Renamed to avoid pydantic warning

# Initialize FastAPI app with enhanced configuration
app = FastAPI(
    title="TainoAI API - World-Class AI",
    description="Advanced AI Assistant with LLaMA 2 7B Chat + Mistral 7B Models",
    version="9.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Enhanced startup initialization
async def initialize_app():
    logger.info("🚀 TainoAI World-Class AI - Starting up...")
    logger.info("=" * 70)

    # Create organized directory structure
    logger.info("📁 Creating organized directory structure...")
    directories = [
        "data",
        "data/conversations",
        "data/documents",
        "data/uploads",
        "logs"
    ]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"📂 Created: {directory}")

    # Initialize TainoAI World-Class Engine
    logger.info("🤖 Initializing TainoAI World-Class Engine...")
    logger.info("🧠 Loading LLaMA 2 7B Chat + Mistral 7B Models...")

    try:
        from llama_engine import llama_engine
        model_loaded = await llama_engine.initialize()

        if model_loaded:
            logger.info("✅ TainoAI World-Class Models loaded successfully!")
            logger.info(f"🎯 Primary: {llama_engine.primary_model_type}")
            logger.info(f"🎯 Secondary: {llama_engine.secondary_model_type}")
            logger.info(f"⚙️ Threads: {llama_engine.threads}, Context: {llama_engine.context_size}")
            logger.info(f"🚀 Mode: {llama_engine.tainoai_mode}")
        else:
            logger.warning("⚠️ World-class models not available, using enhanced fallback")

    except Exception as e:
        logger.warning(f"⚠️ TainoAI engine initialization failed: {e}")
        logger.info("🔄 Falling back to enhanced response system")

    logger.info("✅ TainoAI World-Class AI startup complete!")
    logger.info("=" * 70)

# CORS middleware for React frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

async def generate_ai_response(message: str, conversation_id: str = "") -> str:
    """Generate intelligent AI responses using Llama.cpp"""
    message_lower = message.lower().strip()

    # Try Llama.cpp engine first
    try:
        from llama_engine import llama_engine

        # Get document context if available
        doc_context = ""
        try:
            from document_processor import document_processor
            relevant_docs = await document_processor.search_knowledge_base(message, conversation_id)
            if relevant_docs:
                doc_context = "\n\nRelevant information from uploaded documents:\n"
                for doc in relevant_docs[:2]:
                    doc_context += f"- From '{doc['filename']}': {doc['content'][:300]}...\n"
        except Exception as e:
            logger.warning(f"Document search not available: {e}")

        # Combine message with document context
        enhanced_prompt = message
        if doc_context:
            enhanced_prompt = f"{message}\n{doc_context}"

        # Generate response using Llama.cpp
        ai_response = await llama_engine.generate_response(enhanced_prompt, conversation_id)
        if ai_response and len(ai_response.strip()) > 10:
            logger.info("✅ Using Llama.cpp Mistral 7B response")
            return ai_response

    except Exception as e:
        logger.warning(f"Llama.cpp not available: {e}")

    # Enhanced fallback AI system with document learning
    logger.info("🔄 Using enhanced fallback AI system")

    # Get document context for fallback responses too
    doc_context = ""
    try:
        from document_processor import document_processor
        relevant_docs = await document_processor.search_knowledge_base(message, conversation_id)
        if relevant_docs:
            doc_context = "\n\nBased on your uploaded documents:\n"
            for doc in relevant_docs[:2]:
                doc_context += f"📄 From '{doc['filename']}': {doc['content'][:400]}...\n"
            logger.info(f"📚 Found {len(relevant_docs)} relevant documents")
    except Exception as e:
        logger.warning(f"Document search not available: {e}")

    # Enhanced greeting responses
    if any(greeting in message_lower for greeting in ['hi', 'hello', 'hey', 'whats up', 'what\'s up', 'how are you', 'hiya']):
        response = "Hello! I'm TainoAI, your intelligent AI assistant. I'm currently running in enhanced mode with document learning capabilities. I can help you with questions, analyze your uploaded documents, and learn from our conversations."
        if doc_context:
            response += f"\n\n📚 I can see you have uploaded documents. Feel free to ask me questions about them!{doc_context}"
        return response
    
    # Identity and capability questions
    if any(phrase in message_lower for phrase in ['who are you', 'what are you', 'your name']):
        response = "I'm TainoAI, your intelligent AI assistant. I'm designed to help you with a wide range of tasks including answering questions, explaining concepts, helping with coding, and providing information on various topics."
        if doc_context:
            response += f"\n\n📚 I can also see and analyze your uploaded documents:{doc_context}"
        return response

    # Lightweight mode questions
    if any(phrase in message_lower for phrase in ['lightweight mode', 'what is included', 'what can you do']):
        capabilities = """🤖 **TainoAI Enhanced Mode Capabilities:**

📚 **Document Learning**: I can read and learn from your uploaded files (PDF, Word, Excel, Text)
💬 **Intelligent Conversations**: Context-aware responses that remember our chat history
🔍 **Knowledge Search**: I search through your uploaded documents to answer questions
📝 **Content Analysis**: I can analyze, summarize, and extract information from documents
🎯 **Personalized Responses**: I adapt my answers based on what you've taught me

**Current Status**: Enhanced fallback mode with full document learning capabilities"""

        if doc_context:
            capabilities += f"\n\n📄 **Your Documents**: I currently have access to your uploaded documents and can answer questions about them:{doc_context}"
        else:
            capabilities += "\n\n📤 **Upload Documents**: Click the upload button (📤) to teach me about your specific topics!"

        return capabilities

    # RPG Marketing questions
    if any(phrase in message_lower for phrase in ['rpg game', 'market rpg', 'marketing rpg', 'rpg marketing']):
        rpg_marketing = """🎮 **Best Tips to Market Your RPG Game:**

🎯 **Target Audience**:
- Identify your core RPG audience (story-driven, tactical, casual)
- Use platforms like Reddit (r/RPG, r/gamedev), Discord communities
- Engage with RPG streamers and YouTubers

📱 **Digital Marketing**:
- Steam wishlists are crucial - start building early
- Social media with gameplay clips and character art
- Dev blogs showing world-building and story development

🎨 **Content Strategy**:
- Character backstories and lore posts
- Behind-the-scenes development content
- Interactive polls about game mechanics

🤝 **Community Building**:
- Beta testing with RPG enthusiasts
- Collaborate with indie RPG developers
- Attend gaming conventions and indie showcases

💰 **Budget-Friendly Tactics**:
- Leverage free social media platforms
- Create compelling trailers with in-game footage
- Partner with RPG influencers for authentic reviews"""

        if doc_context:
            rpg_marketing += f"\n\n📄 **Based on your documents**:{doc_context}"

        return rpg_marketing

    # Document-related questions
    if any(phrase in message_lower for phrase in ['document', 'uploaded', 'file', 'learn', 'remember']):
        if doc_context:
            return f"📚 **Yes, I can see your uploaded documents!** Here's what I found relevant to your question:{doc_context}\n\nI can analyze, summarize, and answer questions about any documents you upload. What specific information would you like me to help you with?"
        else:
            return """📤 **Document Learning Capabilities:**

I can learn from and analyze various file types:
- 📄 **PDF files**: Extract text and analyze content
- 📝 **Word documents**: Read and understand document structure
- 📊 **Excel spreadsheets**: Process data and tables
- 📋 **Text files**: Plain text, markdown, CSV files

**To upload documents:**
1. Click the upload button (📤) in the top-right corner
2. Select your file (PDF, Word, Excel, Text)
3. I'll process it and remember the content
4. Ask me questions about your uploaded content!

Once you upload documents, I'll be able to reference them in our conversations and provide specific answers based on your content."""

    # Gaming questions
    if 'top games' in message_lower or 'best games' in message_lower:
        return """Here are the top 3 games that are absolutely incredible right now:

1. **Baldur's Gate 3** - Epic RPG with incredible storytelling and character development
2. **Elden Ring** - Open-world action RPG masterpiece from FromSoftware  
3. **The Legend of Zelda: Tears of the Kingdom** - Nintendo's latest adventure with amazing physics

Each of these offers unique experiences. Which type of game interests you most - RPGs, action, or adventure?"""
    
    if 'gaming' in message_lower or 'games' in message_lower:
        return """I know quite a lot about gaming! Here are some areas I can help with:

🎮 **Game Recommendations**: I can suggest games based on your preferences
🏆 **Gaming Platforms**: PC, PlayStation, Xbox, Nintendo Switch, Mobile
🎯 **Game Genres**: RPG, FPS, Strategy, Puzzle, Simulation, and more
📊 **Gaming Industry**: Trends, companies, and market insights
🛠️ **Game Development**: Programming, design, and tools

What specific aspect of gaming would you like to explore?"""
    
    # Weather questions
    if 'weather' in message_lower:
        if doc_context:
            return f"Based on your uploaded documents and weather information:\n\n{doc_context}\n\nWould you like more specific information about any particular aspect of the weather?"
        
        if 'aguada' in message_lower:
            return """Aguada, Puerto Rico typically enjoys a warm tropical climate:

🌡️ **Temperature**: Usually ranges from 78-84°F (26-29°C)
🌴 **Climate**: Tropical maritime with ocean breezes
☀️ **Weather Pattern**: Warm and humid year-round
🌊 **Location**: Beautiful coastal town in western Puerto Rico

The temperature stays fairly consistent throughout the year due to Puerto Rico's tropical location."""
        
        if 'puerto rico' in message_lower:
            return """Puerto Rico has a wonderful tropical climate:

🌡️ **Year-round Temperature**: 75-85°F (24-29°C)
🌴 **Climate Type**: Tropical maritime
🌧️ **Wet Season**: May-October (more rainfall)
☀️ **Dry Season**: November-April (less rainfall)
🌊 **Ocean Influence**: Keeps temperatures moderate"""
    
    # Knowledge questions
    if message_lower.startswith(('what is', 'what are', 'explain', 'define')):
        topic = message_lower.replace('what is', '').replace('what are', '').replace('explain', '').replace('define', '').strip('? ')
        
        if doc_context:
            return f"Based on your uploaded documents about {topic}:\n\n{doc_context}\n\nWould you like me to explain any specific aspect in more detail?"
        
        if 'quantum computing' in topic:
            return """Quantum computing is a revolutionary computing technology that uses quantum mechanical phenomena like superposition and entanglement to process information. 

🔬 **Key Concepts**:
- **Qubits**: Unlike classical bits (0 or 1), qubits can exist in multiple states simultaneously
- **Superposition**: Allows quantum computers to process many possibilities at once
- **Entanglement**: Enables qubits to be correlated in ways that classical systems cannot

🚀 **Applications**:
- Cryptography and security
- Drug discovery and molecular simulation
- Financial optimization
- Machine learning acceleration

This technology could potentially solve certain complex problems exponentially faster than classical computers."""
        
        elif 'artificial intelligence' in topic or 'ai' in topic:
            return """Artificial Intelligence (AI) is the simulation of human intelligence in machines that are programmed to think and learn like humans.

🧠 **Key Areas**:
- **Machine Learning**: Systems that learn from data
- **Natural Language Processing**: Understanding and generating human language
- **Computer Vision**: Interpreting visual information
- **Robotics**: Physical AI systems that interact with the world

🎯 **Applications**:
- Virtual assistants (like me!)
- Autonomous vehicles
- Medical diagnosis
- Content recommendation
- Business automation"""
    
    # Help requests
    if any(phrase in message_lower for phrase in ['help', 'what can you do', 'capabilities']):
        return """I'm here to help you with a wide range of topics:

🤖 **Technology & AI**: Programming, machine learning, quantum computing
🎮 **Gaming**: Game recommendations, industry insights, development
🌍 **General Knowledge**: Science, history, current events
💼 **Business & Strategy**: Analysis, planning, problem-solving
🎨 **Creative Projects**: Writing, brainstorming, design ideas
📚 **Education**: Explanations, tutorials, learning support

What specific area would you like assistance with? I'm ready to dive deep into any topic that interests you!"""
    
    # Use document context if available
    if doc_context:
        return f"Based on your uploaded documents:\n\n{doc_context}\n\nWhat would you like to know more about?"
    
    # Default response
    return f"I understand you're asking about: '{message}'. I'm here to help! Could you tell me more about what specific information you're looking for? I can assist with explanations, recommendations, problem-solving, or just have a great conversation about topics that interest you."

async def store_chat_history(conversation_id: str, user_id: str, message: str,
                           response: str, processing_time: float, ai_model_info: Dict):
    """Store chat history in simple file storage"""
    try:
        # Create data directory if it doesn't exist
        os.makedirs("data", exist_ok=True)
        
        chat_data = {
            "conversation_id": conversation_id,
            "user_id": user_id,
            "message": message,
            "response": response,
            "processing_time": processing_time,
            "ai_model_info": ai_model_info,
            "timestamp": datetime.now().isoformat()
        }
        
        # Append to conversation file
        filename = f"data/{conversation_id}.jsonl"
        with open(filename, "a", encoding="utf-8") as f:
            f.write(json.dumps(chat_data) + "\n")
        
        logger.info(f"💾 Chat history stored for conversation {conversation_id}")
        
    except Exception as e:
        logger.error(f"Failed to store chat history: {e}")

@app.get("/")
async def root():
    """Serve the React frontend"""
    try:
        return FileResponse("static/index.html", media_type="text/html")
    except Exception as e:
        logger.error(f"Error serving index.html: {e}")
        return {"error": "Frontend not found", "message": "Please check static files"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test data directory
        os.makedirs("data", exist_ok=True)

        # Test document processor
        from document_processor import document_processor

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "7.0.0",
            "features": ["document_upload", "conversation_memory", "ai_responses"],
            "data_directory": "accessible",
            "document_processor": "loaded"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest, background_tasks: BackgroundTasks):
    """Main chat endpoint"""
    start_time = datetime.now()
    
    try:
        # Generate AI response
        ai_response = await generate_ai_response(
            request.message,
            request.conversation_id
        )
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # Get model info from Llama engine
        try:
            from llama_engine import llama_engine
            ai_model_info = llama_engine.get_model_info()
            ai_model_info.update({
                "temperature": request.temperature,
                "max_tokens": request.max_tokens,
                "version": "7.0.0"
            })
        except:
            ai_model_info = {
                "model": "tainoai-fallback",
                "temperature": request.temperature,
                "max_tokens": request.max_tokens,
                "version": "7.0.0"
            }
        
        # Store in database (background task)
        background_tasks.add_task(
            store_chat_history,
            request.conversation_id,
            request.user_id,
            request.message,
            ai_response,
            processing_time,
            ai_model_info
        )

        return ChatResponse(
            response=ai_response,
            conversation_id=request.conversation_id,
            timestamp=datetime.now().isoformat(),
            processing_time=processing_time,
            ai_model_info=ai_model_info
        )
        
    except Exception as e:
        logger.error(f"Chat endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/history/{conversation_id}")
async def get_chat_history(conversation_id: str, limit: int = 50):
    """Get chat history for a conversation"""
    try:
        filename = f"data/{conversation_id}.jsonl"
        if not os.path.exists(filename):
            return {"history": []}

        history = []
        with open(filename, "r", encoding="utf-8") as f:
            for line in f:
                try:
                    data = json.loads(line.strip())
                    history.append({
                        "message": data["message"],
                        "response": data["response"],
                        "timestamp": data["timestamp"],
                        "processing_time": data["processing_time"]
                    })
                except json.JSONDecodeError:
                    continue

        return {"history": history[-limit:]}

    except Exception as e:
        logger.error(f"Failed to get chat history: {e}")
        return {"history": []}

@app.post("/upload")
async def upload_document(file: UploadFile = File(...), user_id: str = Form("default")):
    """Upload and process documents for learning"""
    try:
        from document_processor import document_processor

        # Read file content
        file_content = await file.read()

        # Process the document
        result = await document_processor.process_document(
            file_content,
            file.filename,
            user_id
        )

        return result

    except Exception as e:
        logger.error(f"Upload error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/documents/{user_id}")
async def get_user_documents(user_id: str):
    """Get all documents uploaded by a user"""
    try:
        from document_processor import document_processor
        documents = await document_processor.get_user_documents(user_id)
        return {"documents": documents}
    except Exception as e:
        logger.error(f"Error getting documents: {e}")
        return {"documents": []}

@app.delete("/documents/{file_id}")
async def delete_document(file_id: str, user_id: str = "default"):
    """Delete a document"""
    try:
        from document_processor import document_processor
        success = await document_processor.delete_document(file_id, user_id)
        if success:
            return {"success": True, "message": "Document deleted successfully"}
        else:
            return {"success": False, "message": "Document not found or access denied"}
    except Exception as e:
        logger.error(f"Error deleting document: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Mount static files (React build)
if os.path.exists("static"):
    # Mount _next static files
    app.mount("/_next", StaticFiles(directory="static/_next"), name="next_static")

    # Mount other static files
    app.mount("/static", StaticFiles(directory="static"), name="static")

    # Catch-all route for React Router
    @app.get("/{full_path:path}")
    async def serve_react_app(full_path: str):
        """Serve React app for all routes"""
        # Don't intercept API routes
        if full_path.startswith(("api/", "health", "chat", "upload", "documents")):
            raise HTTPException(status_code=404, detail="Not found")
        return FileResponse("static/index.html", media_type="text/html")
else:
    logger.warning("Static directory not found - running in API-only mode")

if __name__ == "__main__":
    import uvicorn
    import asyncio

    # Initialize app
    asyncio.run(initialize_app())

    port = int(os.getenv("PORT", 8000))
    logger.info(f"🚀 Starting TainoAI on port {port}")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        log_level="info",
        access_log=False,
        workers=1
    )
