"""
TainoAI - World-Class AI Assistant
Multi-model deployment with LLaMA 2 7B Chat + Mistral 7B + Claude
Optimized for scalability and production deployment
"""

import os
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

from fastapi import FastAPI, HTTPException, BackgroundTasks, UploadFile, File, Form, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, HTMLResponse, JSONResponse
from pydantic import BaseModel
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure enhanced logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Get configuration
CLAUDE_API_KEY = os.getenv('CLAUDE_API_KEY')
IS_PRODUCTION = os.getenv('NODE_ENV') == 'production'
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))

app = FastAPI(
    title="TainoAI API - World-Class AI",
    description="Advanced AI Assistant with LLaMA 2 7B Chat + Mistral 7B Models + Claude",
    version="1.0.11"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Serve static files from multiple directories
STATIC_PATHS = {
    'out': os.path.join(ROOT_DIR, 'out'),
    'static': os.path.join(ROOT_DIR, 'static'),
    '_next': os.path.join(ROOT_DIR, 'out', '_next'),
}

# Mount static directories
for prefix, path in STATIC_PATHS.items():
    if os.path.exists(path):
        logger.info(f"Mounting static directory: {path} at /{prefix}")
        app.mount(f"/{prefix}", StaticFiles(directory=path, html=True), name=prefix)

@app.get("/")
async def root(request: Request):
    """Serve the frontend"""
    logger.info(f"Serving root path. Production mode: {IS_PRODUCTION}")
    logger.info(f"Current directory: {os.getcwd()}")
    
    # List of possible index.html locations
    index_locations = [
        os.path.join(ROOT_DIR, 'out', 'index.html'),
        os.path.join(ROOT_DIR, 'static', 'index.html'),
        os.path.join(ROOT_DIR, 'index.html'),
    ]
    
    # Try to serve index.html from various locations
    for index_path in index_locations:
        if os.path.exists(index_path):
            logger.info(f"Serving index.html from: {index_path}")
            return FileResponse(index_path)
    
    # If no index.html is found, return debug information
    debug_info = {
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "environment": {
            "production": IS_PRODUCTION,
            "root_dir": ROOT_DIR,
            "cwd": os.getcwd(),
        },
        "static_paths": {
            path: os.path.exists(path) for path in STATIC_PATHS.values()
        },
        "files_in_root": os.listdir(ROOT_DIR)
    }
    
    logger.warning("No index.html found. Returning debug info.")
    return JSONResponse(content=debug_info)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    static_status = {
        path: os.path.exists(full_path)
        for path, full_path in STATIC_PATHS.items()
    }
    
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "static_files": static_status,
        "models": {
            "primary": os.getenv("PRIMARY_MODEL_TYPE", "mistral-7b"),
            "secondary": os.getenv("SECONDARY_MODEL_TYPE", "llama-2-7b-chat"),
            "claude": "available" if CLAUDE_API_KEY else "not configured"
        }
    }

@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all incoming requests"""
    logger.info(f"Request: {request.method} {request.url.path}")
    response = await call_next(request)
    logger.info(f"Response status: {response.status_code}")
    return response