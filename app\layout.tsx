import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Toaster } from 'react-hot-toast'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'TainoAI Rebirth v6.0 GENESIS',
  description: 'Independent AI Assistant - Built from scratch with Next.js, FastAPI, and PyTorch',
  keywords: ['AI', 'Assistant', 'TainoAI', 'Independent', 'PyTorch', 'FastAPI', 'Next.js'],
  authors: [{ name: 'TainoAI Team' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#9c6bff',
  openGraph: {
    title: 'TainoAI Rebirth v6.0 GENESIS',
    description: 'Independent AI Assistant - Built from scratch',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TainoAI Rebirth v6.0 GENESIS',
    description: 'Independent AI Assistant - Built from scratch',
  },
  robots: {
    index: true,
    follow: true,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="theme-color" content="#9c6bff" />
      </head>
      <body className={`${inter.className} antialiased`}>
        <div className="min-h-screen animated-gradient">
          {children}
        </div>
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              color: '#fff',
            },
          }}
        />
      </body>
    </html>
  )
}
