"""
TainoAI Web Search Module
Provides real-time web search capabilities for current information
"""

import os
import logging
import asyncio
from typing import List, Dict, Optional
import json

logger = logging.getLogger(__name__)

class TainoAIWebSearch:
    def __init__(self):
        self.search_timeout = 10
        self.max_results = 5
        
    async def search_web(self, query: str) -> List[Dict]:
        """Search the web for current information"""
        try:
            # Try DuckDuckGo search first (no API key required)
            results = await self._duckduckgo_search(query)
            if results:
                return results
            
            # Fallback to basic search simulation
            return await self._fallback_search(query)
            
        except Exception as e:
            logger.error(f"Web search failed: {e}")
            return []
    
    async def _duckduckgo_search(self, query: str) -> List[Dict]:
        """Search using DuckDuckGo (no API key required)"""
        try:
            # Try to import and use duckduckgo_search
            import duckduckgo_search
            
            # Perform search
            results = duckduckgo_search.ddg(query, max_results=self.max_results)
            
            formatted_results = []
            for result in results:
                formatted_results.append({
                    "title": result.get("title", ""),
                    "snippet": result.get("body", ""),
                    "url": result.get("href", ""),
                    "source": "DuckDuckGo"
                })
            
            logger.info(f"🔍 Found {len(formatted_results)} web search results for: {query}")
            return formatted_results
            
        except ImportError:
            logger.warning("duckduckgo_search not available, using fallback")
            return []
        except Exception as e:
            logger.error(f"DuckDuckGo search error: {e}")
            return []
    
    async def _fallback_search(self, query: str) -> List[Dict]:
        """Fallback search with simulated results"""
        query_lower = query.lower()
        
        # Provide relevant fallback information based on query
        if any(term in query_lower for term in ['weather', 'temperature', 'climate']):
            return [{
                "title": "Weather Information",
                "snippet": "For current weather information, I recommend checking local weather services or apps. Weather patterns vary by location and change frequently.",
                "url": "https://weather.com",
                "source": "TainoAI Knowledge"
            }]
        
        elif any(term in query_lower for term in ['news', 'current', 'latest', 'today']):
            return [{
                "title": "Current News and Information",
                "snippet": "For the latest news and current events, I recommend checking reputable news sources. Information changes rapidly in today's world.",
                "url": "https://news.google.com",
                "source": "TainoAI Knowledge"
            }]
        
        elif any(term in query_lower for term in ['stock', 'market', 'price', 'trading']):
            return [{
                "title": "Market Information",
                "snippet": "For current market data and stock prices, check financial news sources or trading platforms. Markets are dynamic and change constantly.",
                "url": "https://finance.yahoo.com",
                "source": "TainoAI Knowledge"
            }]
        
        else:
            return [{
                "title": f"Information about: {query}",
                "snippet": f"While I don't have real-time web access in this mode, I can help you understand {query} based on my knowledge. For the most current information, consider checking relevant websites or databases.",
                "url": "",
                "source": "TainoAI Knowledge"
            }]
    
    def format_search_results(self, results: List[Dict]) -> str:
        """Format search results for inclusion in AI responses"""
        if not results:
            return ""
        
        formatted = "\n\n🌐 **Current Web Information:**\n"
        for i, result in enumerate(results[:3], 1):  # Limit to top 3 results
            formatted += f"\n{i}. **{result['title']}**\n"
            formatted += f"   {result['snippet'][:200]}...\n"
            if result['url']:
                formatted += f"   Source: {result['url']}\n"
        
        return formatted
    
    async def search_and_format(self, query: str) -> str:
        """Search web and return formatted results"""
        results = await self.search_web(query)
        return self.format_search_results(results)

# Global web search instance
web_search = TainoAIWebSearch()

# Function to detect if a query needs web search
def needs_web_search(query: str) -> bool:
    """Determine if a query would benefit from web search"""
    query_lower = query.lower()
    
    # Keywords that suggest current/real-time information is needed
    current_keywords = [
        'latest', 'current', 'today', 'now', 'recent', 'new',
        'weather', 'temperature', 'news', 'stock', 'price',
        'market', 'trending', 'happening', 'update', 'live'
    ]
    
    return any(keyword in query_lower for keyword in current_keywords)

# Enhanced search function for integration with TainoAI
async def enhance_prompt_with_web_search(prompt: str) -> tuple:
    """Enhance prompt with web search if needed"""
    if needs_web_search(prompt):
        try:
            search_results = await web_search.search_and_format(prompt)
            return prompt, search_results
        except Exception as e:
            logger.error(f"Web search enhancement failed: {e}")
            return prompt, ""
    
    return prompt, ""
