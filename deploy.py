#!/usr/bin/env python3
"""
TainoAI Deployment Script
Automates the full deployment process for TainoAI with Mistral 7B
"""

import os
import sys
import subprocess
import logging
import time
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TainoAIDeployer:
    def __init__(self):
        self.project_root = Path.cwd()
        self.model_path = self.project_root / "models" / "mistral-7b-instruct-v0.1.Q4_K_M.gguf"
        
    def print_banner(self):
        """Print TainoAI deployment banner"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    TainoAI Deployment                       ║
║              Full AI Power with Mistral 7B                  ║
║                     Version 8.0.0                           ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        
    def check_prerequisites(self):
        """Check if all prerequisites are installed"""
        logger.info("🔍 Checking prerequisites...")
        
        # Check Docker
        try:
            result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ Docker: {result.stdout.strip()}")
            else:
                logger.error("❌ Docker not found. Please install Docker.")
                return False
        except FileNotFoundError:
            logger.error("❌ Docker not found. Please install Docker.")
            return False
        
        # Check Python
        python_version = sys.version.split()[0]
        logger.info(f"✅ Python: {python_version}")
        
        # Check Node.js (for frontend)
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ Node.js: {result.stdout.strip()}")
            else:
                logger.warning("⚠️ Node.js not found. Frontend build may fail.")
        except FileNotFoundError:
            logger.warning("⚠️ Node.js not found. Frontend build may fail.")
        
        return True
    
    def download_model(self):
        """Download the Mistral 7B model"""
        logger.info("📥 Downloading Mistral 7B model (3.9GB)...")
        
        if self.model_path.exists():
            size_gb = self.model_path.stat().st_size / (1024**3)
            logger.info(f"✅ Model already exists: {size_gb:.2f} GB")
            return True
        
        try:
            # Run the model downloader
            result = subprocess.run([sys.executable, "download_model.py"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ Model download completed successfully")
                return True
            else:
                logger.error(f"❌ Model download failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error downloading model: {e}")
            return False
    
    def build_docker_image(self):
        """Build the Docker image"""
        logger.info("🐳 Building Docker image...")
        
        try:
            # Build the Docker image
            cmd = ["docker", "build", "-f", "Dockerfile.llama", "-t", "tainoai", "."]
            logger.info(f"Running: {' '.join(cmd)}")
            
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                                     text=True, universal_newlines=True)
            
            # Stream output
            for line in process.stdout:
                print(line.strip())
            
            process.wait()
            
            if process.returncode == 0:
                logger.info("✅ Docker image built successfully")
                return True
            else:
                logger.error("❌ Docker build failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error building Docker image: {e}")
            return False
    
    def test_local_deployment(self):
        """Test the deployment locally"""
        logger.info("🧪 Testing local deployment...")
        
        try:
            # Run container in detached mode
            cmd = ["docker", "run", "-d", "-p", "8000:8000", "--name", "tainoai-test", "tainoai"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"❌ Failed to start container: {result.stderr}")
                return False
            
            container_id = result.stdout.strip()
            logger.info(f"🚀 Container started: {container_id[:12]}")
            
            # Wait for startup
            logger.info("⏳ Waiting for TainoAI to start...")
            time.sleep(10)
            
            # Test health endpoint
            try:
                import requests
                response = requests.get("http://localhost:8000/health", timeout=30)
                if response.status_code == 200:
                    logger.info("✅ Health check passed")
                    logger.info("🎉 TainoAI is running at http://localhost:8000")
                    
                    # Stop test container
                    subprocess.run(["docker", "stop", "tainoai-test"], capture_output=True)
                    subprocess.run(["docker", "rm", "tainoai-test"], capture_output=True)
                    
                    return True
                else:
                    logger.error(f"❌ Health check failed: {response.status_code}")
                    return False
                    
            except Exception as e:
                logger.error(f"❌ Health check error: {e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing deployment: {e}")
            return False
    
    def prepare_heroku_deployment(self):
        """Prepare for Heroku deployment"""
        logger.info("☁️ Preparing Heroku deployment...")
        
        # Check if heroku.yml exists
        heroku_yml = self.project_root / "heroku.yml"
        if heroku_yml.exists():
            logger.info("✅ heroku.yml found")
        else:
            logger.error("❌ heroku.yml not found")
            return False
        
        # Instructions for Heroku deployment
        instructions = """
🚀 Heroku Deployment Instructions:

1. Install Heroku CLI: https://devcenter.heroku.com/articles/heroku-cli

2. Login to Heroku:
   heroku login

3. Create Heroku app:
   heroku create your-tainoai-app

4. Set stack to container:
   heroku stack:set container -a your-tainoai-app

5. Deploy:
   git add .
   git commit -m "Deploy TainoAI Full AI"
   git push heroku main

6. Open your app:
   heroku open -a your-tainoai-app

Note: First deployment may take 10-15 minutes due to model download.
        """
        
        print(instructions)
        return True
    
    def deploy(self):
        """Main deployment process"""
        self.print_banner()
        
        logger.info("🚀 Starting TainoAI deployment process...")
        
        # Step 1: Check prerequisites
        if not self.check_prerequisites():
            logger.error("❌ Prerequisites check failed")
            return False
        
        # Step 2: Download model
        if not self.download_model():
            logger.error("❌ Model download failed")
            return False
        
        # Step 3: Build Docker image
        if not self.build_docker_image():
            logger.error("❌ Docker build failed")
            return False
        
        # Step 4: Test locally
        if not self.test_local_deployment():
            logger.error("❌ Local test failed")
            return False
        
        # Step 5: Prepare Heroku deployment
        if not self.prepare_heroku_deployment():
            logger.error("❌ Heroku preparation failed")
            return False
        
        logger.info("🎉 TainoAI deployment preparation complete!")
        logger.info("✅ Ready for local use and Heroku deployment")
        
        return True

def main():
    """Main function"""
    deployer = TainoAIDeployer()
    
    # Check for command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--model-only":
            deployer.print_banner()
            return deployer.download_model()
        elif sys.argv[1] == "--build-only":
            deployer.print_banner()
            return deployer.build_docker_image()
        elif sys.argv[1] == "--test-only":
            deployer.print_banner()
            return deployer.test_local_deployment()
    
    # Full deployment
    success = deployer.deploy()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
