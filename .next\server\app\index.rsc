2:I[7831,[],""]
3:I[9344,["358","static/chunks/358-a27b6b812f6c7297.js","931","static/chunks/app/page-9673c80446eed57c.js"],""]
4:I[5613,[],""]
5:I[1778,[],""]
6:I[5925,["185","static/chunks/app/layout-8eeef4ccf1db1d10.js"],"Toaster"]
0:["rW6ZEqJlgRYO7lRbSlWjj",[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",{"children":["__PAGE__",{},["$L1",["$","$L2",null,{"propsForComponent":{"params":{}},"Component":"$3","isStaticGeneration":true}],null]]},[null,["$","html",null,{"lang":"en","className":"dark","children":[["$","head",null,{"children":[["$","link",null,{"rel":"icon","href":"/favicon.ico"}],["$","link",null,{"rel":"apple-touch-icon","href":"/apple-touch-icon.png"}],["$","meta",null,{"name":"theme-color","content":"#9c6bff"}]]}],["$","body",null,{"className":"__className_e8ce0c antialiased","children":[["$","div",null,{"className":"min-h-screen animated-gradient","children":["$","$L4",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[],"styles":null}]}],["$","$L6",null,{"position":"top-right","toastOptions":{"duration":4000,"style":{"background":"rgba(255, 255, 255, 0.1)","backdropFilter":"blur(10px)","border":"1px solid rgba(255, 255, 255, 0.2)","color":"#fff"}}}]]}]]}],null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/ec77bf9adada83e3.css","precedence":"next","crossOrigin":""}]],"$L7"]]]]
7:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"TainoAI Rebirth v6.0 GENESIS"}],["$","meta","3",{"name":"description","content":"Independent AI Assistant - Built from scratch with Next.js, FastAPI, and PyTorch"}],["$","meta","4",{"name":"author","content":"TainoAI Team"}],["$","meta","5",{"name":"keywords","content":"AI,Assistant,TainoAI,Independent,PyTorch,FastAPI,Next.js"}],["$","meta","6",{"name":"robots","content":"index, follow"}],["$","meta","7",{"property":"og:title","content":"TainoAI Rebirth v6.0 GENESIS"}],["$","meta","8",{"property":"og:description","content":"Independent AI Assistant - Built from scratch"}],["$","meta","9",{"property":"og:locale","content":"en_US"}],["$","meta","10",{"property":"og:type","content":"website"}],["$","meta","11",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","12",{"name":"twitter:title","content":"TainoAI Rebirth v6.0 GENESIS"}],["$","meta","13",{"name":"twitter:description","content":"Independent AI Assistant - Built from scratch"}]]
1:null
