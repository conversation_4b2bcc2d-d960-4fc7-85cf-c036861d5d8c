"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/number/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@radix-ui/number/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp)\n/* harmony export */ });\n// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n    return Math.min(max, Math.max(min, value));\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL251bWJlci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEscUNBQXFDO0FBQ3JDLFNBQVNBLE1BQU1DLEtBQUssRUFBRSxDQUFDQyxLQUFLQyxJQUFJO0lBQzlCLE9BQU9DLEtBQUtGLEdBQUcsQ0FBQ0MsS0FBS0MsS0FBS0QsR0FBRyxDQUFDRCxLQUFLRDtBQUNyQztBQUdFLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFpbm9haS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvbnVtYmVyL2Rpc3QvaW5kZXgubWpzPzBhNzciXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvY29yZS9udW1iZXIvc3JjL251bWJlci50c1xuZnVuY3Rpb24gY2xhbXAodmFsdWUsIFttaW4sIG1heF0pIHtcbiAgcmV0dXJuIE1hdGgubWluKG1heCwgTWF0aC5tYXgobWluLCB2YWx1ZSkpO1xufVxuZXhwb3J0IHtcbiAgY2xhbXBcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiY2xhbXAiLCJ2YWx1ZSIsIm1pbiIsIm1heCIsIk1hdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n    return function handleEvent(event) {\n        originalEventHandler?.(event);\n        if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n            return ourEventHandler?.(event);\n        }\n    };\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNENBQTRDO0FBQzVDLFNBQVNBLHFCQUFxQkMsb0JBQW9CLEVBQUVDLGVBQWUsRUFBRSxFQUFFQywyQkFBMkIsSUFBSSxFQUFFLEdBQUcsQ0FBQyxDQUFDO0lBQzNHLE9BQU8sU0FBU0MsWUFBWUMsS0FBSztRQUMvQkosdUJBQXVCSTtRQUN2QixJQUFJRiw2QkFBNkIsU0FBUyxDQUFDRSxNQUFNQyxnQkFBZ0IsRUFBRTtZQUNqRSxPQUFPSixrQkFBa0JHO1FBQzNCO0lBQ0Y7QUFDRjtBQUdFLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFpbm9haS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcHJpbWl0aXZlL2Rpc3QvaW5kZXgubWpzPzE4NjgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvY29yZS9wcmltaXRpdmUvc3JjL3ByaW1pdGl2ZS50c3hcbmZ1bmN0aW9uIGNvbXBvc2VFdmVudEhhbmRsZXJzKG9yaWdpbmFsRXZlbnRIYW5kbGVyLCBvdXJFdmVudEhhbmRsZXIsIHsgY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID0gdHJ1ZSB9ID0ge30pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIGhhbmRsZUV2ZW50KGV2ZW50KSB7XG4gICAgb3JpZ2luYWxFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgaWYgKGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9PT0gZmFsc2UgfHwgIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHtcbiAgICAgIHJldHVybiBvdXJFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgfVxuICB9O1xufVxuZXhwb3J0IHtcbiAgY29tcG9zZUV2ZW50SGFuZGxlcnNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiY29tcG9zZUV2ZW50SGFuZGxlcnMiLCJvcmlnaW5hbEV2ZW50SGFuZGxlciIsIm91ckV2ZW50SGFuZGxlciIsImNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCIsImhhbmRsZUV2ZW50IiwiZXZlbnQiLCJkZWZhdWx0UHJldmVudGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-avatar/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage),\n/* harmony export */   Fallback: () => (/* binding */ Fallback),\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createAvatarScope: () => (/* binding */ createAvatarScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_is_hydrated__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-is-hydrated */ \"(ssr)/./node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarFallback,AvatarImage,Fallback,Image,Root,createAvatarScope auto */ // src/avatar.tsx\n\n\n\n\n\n\n\nvar AVATAR_NAME = \"Avatar\";\nvar [createAvatarContext, createAvatarScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(AVATAR_NAME);\nvar [AvatarProvider, useAvatarContext] = createAvatarContext(AVATAR_NAME);\nvar Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"idle\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AvatarProvider, {\n        scope: __scopeAvatar,\n        imageLoadingStatus,\n        onImageLoadingStatusChange: setImageLoadingStatus,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n            ...avatarProps,\n            ref: forwardedRef\n        })\n    });\n});\nAvatar.displayName = AVATAR_NAME;\nvar IMAGE_NAME = \"AvatarImage\";\nvar AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, src, onLoadingStatusChange = ()=>{}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps);\n    const handleLoadingStatusChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__.useCallbackRef)((status)=>{\n        onLoadingStatusChange(status);\n        context.onImageLoadingStatusChange(status);\n    });\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)(()=>{\n        if (imageLoadingStatus !== \"idle\") {\n            handleLoadingStatusChange(imageLoadingStatus);\n        }\n    }, [\n        imageLoadingStatus,\n        handleLoadingStatusChange\n    ]);\n    return imageLoadingStatus === \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.img, {\n        ...imageProps,\n        ref: forwardedRef,\n        src\n    }) : null;\n});\nAvatarImage.displayName = IMAGE_NAME;\nvar FALLBACK_NAME = \"AvatarFallback\";\nvar AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = react__WEBPACK_IMPORTED_MODULE_0__.useState(delayMs === void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (delayMs !== void 0) {\n            const timerId = window.setTimeout(()=>setCanRender(true), delayMs);\n            return ()=>window.clearTimeout(timerId);\n        }\n    }, [\n        delayMs\n    ]);\n    return canRender && context.imageLoadingStatus !== \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n        ...fallbackProps,\n        ref: forwardedRef\n    }) : null;\n});\nAvatarFallback.displayName = FALLBACK_NAME;\nfunction resolveLoadingStatus(image, src) {\n    if (!image) {\n        return \"idle\";\n    }\n    if (!src) {\n        return \"error\";\n    }\n    if (image.src !== src) {\n        image.src = src;\n    }\n    return image.complete && image.naturalWidth > 0 ? \"loaded\" : \"loading\";\n}\nfunction useImageLoadingStatus(src, { referrerPolicy, crossOrigin }) {\n    const isHydrated = (0,_radix_ui_react_use_is_hydrated__WEBPACK_IMPORTED_MODULE_6__.useIsHydrated)();\n    const imageRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const image = (()=>{\n        if (!isHydrated) return null;\n        if (!imageRef.current) {\n            imageRef.current = new window.Image();\n        }\n        return imageRef.current;\n    })();\n    const [loadingStatus, setLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>resolveLoadingStatus(image, src));\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)(()=>{\n        setLoadingStatus(resolveLoadingStatus(image, src));\n    }, [\n        image,\n        src\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)(()=>{\n        const updateStatus = (status)=>()=>{\n                setLoadingStatus(status);\n            };\n        if (!image) return;\n        const handleLoad = updateStatus(\"loaded\");\n        const handleError = updateStatus(\"error\");\n        image.addEventListener(\"load\", handleLoad);\n        image.addEventListener(\"error\", handleError);\n        if (referrerPolicy) {\n            image.referrerPolicy = referrerPolicy;\n        }\n        if (typeof crossOrigin === \"string\") {\n            image.crossOrigin = crossOrigin;\n        }\n        return ()=>{\n            image.removeEventListener(\"load\", handleLoad);\n            image.removeEventListener(\"error\", handleError);\n        };\n    }, [\n        image,\n        crossOrigin,\n        referrerPolicy\n    ]);\n    return loadingStatus;\n}\nvar Root = Avatar;\nvar Image = AvatarImage;\nvar Fallback = AvatarFallback;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWF2YXRhci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs2SEFFQSxpQkFBaUI7QUFDYztBQUM4QjtBQUNLO0FBQ0U7QUFDZDtBQUNVO0FBQ3hCO0FBQ3hDLElBQUlPLGNBQWM7QUFDbEIsSUFBSSxDQUFDQyxxQkFBcUJDLGtCQUFrQixHQUFHUiwyRUFBa0JBLENBQUNNO0FBQ2xFLElBQUksQ0FBQ0csZ0JBQWdCQyxpQkFBaUIsR0FBR0gsb0JBQW9CRDtBQUM3RCxJQUFJSyx1QkFBU1osNkNBQWdCLENBQzNCLENBQUNjLE9BQU9DO0lBQ04sTUFBTSxFQUFFQyxhQUFhLEVBQUUsR0FBR0MsYUFBYSxHQUFHSDtJQUMxQyxNQUFNLENBQUNJLG9CQUFvQkMsc0JBQXNCLEdBQUduQiwyQ0FBYyxDQUFDO0lBQ25FLE9BQU8sYUFBYSxHQUFHTSxzREFBR0EsQ0FDeEJJLGdCQUNBO1FBQ0VXLE9BQU9MO1FBQ1BFO1FBQ0FJLDRCQUE0Qkg7UUFDNUJJLFVBQVUsYUFBYSxHQUFHakIsc0RBQUdBLENBQUNGLGdFQUFTQSxDQUFDb0IsSUFBSSxFQUFFO1lBQUUsR0FBR1AsV0FBVztZQUFFUSxLQUFLVjtRQUFhO0lBQ3BGO0FBRUo7QUFFRkgsT0FBT2MsV0FBVyxHQUFHbkI7QUFDckIsSUFBSW9CLGFBQWE7QUFDakIsSUFBSUMsNEJBQWM1Qiw2Q0FBZ0IsQ0FDaEMsQ0FBQ2MsT0FBT0M7SUFDTixNQUFNLEVBQUVDLGFBQWEsRUFBRWEsR0FBRyxFQUFFQyx3QkFBd0IsS0FDcEQsQ0FBQyxFQUFFLEdBQUdDLFlBQVksR0FBR2pCO0lBQ3JCLE1BQU1rQixVQUFVckIsaUJBQWlCZ0IsWUFBWVg7SUFDN0MsTUFBTUUscUJBQXFCZSxzQkFBc0JKLEtBQUtFO0lBQ3RELE1BQU1HLDRCQUE0QmhDLGdGQUFjQSxDQUFDLENBQUNpQztRQUNoREwsc0JBQXNCSztRQUN0QkgsUUFBUVYsMEJBQTBCLENBQUNhO0lBQ3JDO0lBQ0FoQyxrRkFBZUEsQ0FBQztRQUNkLElBQUllLHVCQUF1QixRQUFRO1lBQ2pDZ0IsMEJBQTBCaEI7UUFDNUI7SUFDRixHQUFHO1FBQUNBO1FBQW9CZ0I7S0FBMEI7SUFDbEQsT0FBT2hCLHVCQUF1QixXQUFXLGFBQWEsR0FBR1osc0RBQUdBLENBQUNGLGdFQUFTQSxDQUFDZ0MsR0FBRyxFQUFFO1FBQUUsR0FBR0wsVUFBVTtRQUFFTixLQUFLVjtRQUFjYztJQUFJLEtBQUs7QUFDM0g7QUFFRkQsWUFBWUYsV0FBVyxHQUFHQztBQUMxQixJQUFJVSxnQkFBZ0I7QUFDcEIsSUFBSUMsK0JBQWlCdEMsNkNBQWdCLENBQ25DLENBQUNjLE9BQU9DO0lBQ04sTUFBTSxFQUFFQyxhQUFhLEVBQUV1QixPQUFPLEVBQUUsR0FBR0MsZUFBZSxHQUFHMUI7SUFDckQsTUFBTWtCLFVBQVVyQixpQkFBaUIwQixlQUFlckI7SUFDaEQsTUFBTSxDQUFDeUIsV0FBV0MsYUFBYSxHQUFHMUMsMkNBQWMsQ0FBQ3VDLFlBQVksS0FBSztJQUNsRXZDLDRDQUFlLENBQUM7UUFDZCxJQUFJdUMsWUFBWSxLQUFLLEdBQUc7WUFDdEIsTUFBTUssVUFBVUMsT0FBT0MsVUFBVSxDQUFDLElBQU1KLGFBQWEsT0FBT0g7WUFDNUQsT0FBTyxJQUFNTSxPQUFPRSxZQUFZLENBQUNIO1FBQ25DO0lBQ0YsR0FBRztRQUFDTDtLQUFRO0lBQ1osT0FBT0UsYUFBYVQsUUFBUWQsa0JBQWtCLEtBQUssV0FBVyxhQUFhLEdBQUdaLHNEQUFHQSxDQUFDRixnRUFBU0EsQ0FBQ29CLElBQUksRUFBRTtRQUFFLEdBQUdnQixhQUFhO1FBQUVmLEtBQUtWO0lBQWEsS0FBSztBQUMvSTtBQUVGdUIsZUFBZVosV0FBVyxHQUFHVztBQUM3QixTQUFTVyxxQkFBcUJDLEtBQUssRUFBRXBCLEdBQUc7SUFDdEMsSUFBSSxDQUFDb0IsT0FBTztRQUNWLE9BQU87SUFDVDtJQUNBLElBQUksQ0FBQ3BCLEtBQUs7UUFDUixPQUFPO0lBQ1Q7SUFDQSxJQUFJb0IsTUFBTXBCLEdBQUcsS0FBS0EsS0FBSztRQUNyQm9CLE1BQU1wQixHQUFHLEdBQUdBO0lBQ2Q7SUFDQSxPQUFPb0IsTUFBTUMsUUFBUSxJQUFJRCxNQUFNRSxZQUFZLEdBQUcsSUFBSSxXQUFXO0FBQy9EO0FBQ0EsU0FBU2xCLHNCQUFzQkosR0FBRyxFQUFFLEVBQUV1QixjQUFjLEVBQUVDLFdBQVcsRUFBRTtJQUNqRSxNQUFNQyxhQUFhakQsOEVBQWFBO0lBQ2hDLE1BQU1rRCxXQUFXdkQseUNBQVksQ0FBQztJQUM5QixNQUFNaUQsUUFBUSxDQUFDO1FBQ2IsSUFBSSxDQUFDSyxZQUFZLE9BQU87UUFDeEIsSUFBSSxDQUFDQyxTQUFTRSxPQUFPLEVBQUU7WUFDckJGLFNBQVNFLE9BQU8sR0FBRyxJQUFJWixPQUFPYSxLQUFLO1FBQ3JDO1FBQ0EsT0FBT0gsU0FBU0UsT0FBTztJQUN6QjtJQUNBLE1BQU0sQ0FBQ0UsZUFBZUMsaUJBQWlCLEdBQUc1RCwyQ0FBYyxDQUN0RCxJQUFNZ0QscUJBQXFCQyxPQUFPcEI7SUFFcEMxQixrRkFBZUEsQ0FBQztRQUNkeUQsaUJBQWlCWixxQkFBcUJDLE9BQU9wQjtJQUMvQyxHQUFHO1FBQUNvQjtRQUFPcEI7S0FBSTtJQUNmMUIsa0ZBQWVBLENBQUM7UUFDZCxNQUFNMEQsZUFBZSxDQUFDMUIsU0FBVztnQkFDL0J5QixpQkFBaUJ6QjtZQUNuQjtRQUNBLElBQUksQ0FBQ2MsT0FBTztRQUNaLE1BQU1hLGFBQWFELGFBQWE7UUFDaEMsTUFBTUUsY0FBY0YsYUFBYTtRQUNqQ1osTUFBTWUsZ0JBQWdCLENBQUMsUUFBUUY7UUFDL0JiLE1BQU1lLGdCQUFnQixDQUFDLFNBQVNEO1FBQ2hDLElBQUlYLGdCQUFnQjtZQUNsQkgsTUFBTUcsY0FBYyxHQUFHQTtRQUN6QjtRQUNBLElBQUksT0FBT0MsZ0JBQWdCLFVBQVU7WUFDbkNKLE1BQU1JLFdBQVcsR0FBR0E7UUFDdEI7UUFDQSxPQUFPO1lBQ0xKLE1BQU1nQixtQkFBbUIsQ0FBQyxRQUFRSDtZQUNsQ2IsTUFBTWdCLG1CQUFtQixDQUFDLFNBQVNGO1FBQ3JDO0lBQ0YsR0FBRztRQUFDZDtRQUFPSTtRQUFhRDtLQUFlO0lBQ3ZDLE9BQU9PO0FBQ1Q7QUFDQSxJQUFJTyxPQUFPdEQ7QUFDWCxJQUFJOEMsUUFBUTlCO0FBQ1osSUFBSXVDLFdBQVc3QjtBQVNiLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFpbm9haS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtYXZhdGFyL2Rpc3QvaW5kZXgubWpzPzYzOWEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbi8vIHNyYy9hdmF0YXIudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGNyZWF0ZUNvbnRleHRTY29wZSB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtY29udGV4dFwiO1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2tSZWYgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWZcIjtcbmltcG9ydCB7IHVzZUxheW91dEVmZmVjdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3RcIjtcbmltcG9ydCB7IFByaW1pdGl2ZSB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlXCI7XG5pbXBvcnQgeyB1c2VJc0h5ZHJhdGVkIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC11c2UtaXMtaHlkcmF0ZWRcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIEFWQVRBUl9OQU1FID0gXCJBdmF0YXJcIjtcbnZhciBbY3JlYXRlQXZhdGFyQ29udGV4dCwgY3JlYXRlQXZhdGFyU2NvcGVdID0gY3JlYXRlQ29udGV4dFNjb3BlKEFWQVRBUl9OQU1FKTtcbnZhciBbQXZhdGFyUHJvdmlkZXIsIHVzZUF2YXRhckNvbnRleHRdID0gY3JlYXRlQXZhdGFyQ29udGV4dChBVkFUQVJfTkFNRSk7XG52YXIgQXZhdGFyID0gUmVhY3QuZm9yd2FyZFJlZihcbiAgKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7IF9fc2NvcGVBdmF0YXIsIC4uLmF2YXRhclByb3BzIH0gPSBwcm9wcztcbiAgICBjb25zdCBbaW1hZ2VMb2FkaW5nU3RhdHVzLCBzZXRJbWFnZUxvYWRpbmdTdGF0dXNdID0gUmVhY3QudXNlU3RhdGUoXCJpZGxlXCIpO1xuICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgICAgQXZhdGFyUHJvdmlkZXIsXG4gICAgICB7XG4gICAgICAgIHNjb3BlOiBfX3Njb3BlQXZhdGFyLFxuICAgICAgICBpbWFnZUxvYWRpbmdTdGF0dXMsXG4gICAgICAgIG9uSW1hZ2VMb2FkaW5nU3RhdHVzQ2hhbmdlOiBzZXRJbWFnZUxvYWRpbmdTdGF0dXMsXG4gICAgICAgIGNoaWxkcmVuOiAvKiBAX19QVVJFX18gKi8ganN4KFByaW1pdGl2ZS5zcGFuLCB7IC4uLmF2YXRhclByb3BzLCByZWY6IGZvcndhcmRlZFJlZiB9KVxuICAgICAgfVxuICAgICk7XG4gIH1cbik7XG5BdmF0YXIuZGlzcGxheU5hbWUgPSBBVkFUQVJfTkFNRTtcbnZhciBJTUFHRV9OQU1FID0gXCJBdmF0YXJJbWFnZVwiO1xudmFyIEF2YXRhckltYWdlID0gUmVhY3QuZm9yd2FyZFJlZihcbiAgKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7IF9fc2NvcGVBdmF0YXIsIHNyYywgb25Mb2FkaW5nU3RhdHVzQ2hhbmdlID0gKCkgPT4ge1xuICAgIH0sIC4uLmltYWdlUHJvcHMgfSA9IHByb3BzO1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VBdmF0YXJDb250ZXh0KElNQUdFX05BTUUsIF9fc2NvcGVBdmF0YXIpO1xuICAgIGNvbnN0IGltYWdlTG9hZGluZ1N0YXR1cyA9IHVzZUltYWdlTG9hZGluZ1N0YXR1cyhzcmMsIGltYWdlUHJvcHMpO1xuICAgIGNvbnN0IGhhbmRsZUxvYWRpbmdTdGF0dXNDaGFuZ2UgPSB1c2VDYWxsYmFja1JlZigoc3RhdHVzKSA9PiB7XG4gICAgICBvbkxvYWRpbmdTdGF0dXNDaGFuZ2Uoc3RhdHVzKTtcbiAgICAgIGNvbnRleHQub25JbWFnZUxvYWRpbmdTdGF0dXNDaGFuZ2Uoc3RhdHVzKTtcbiAgICB9KTtcbiAgICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgICAgaWYgKGltYWdlTG9hZGluZ1N0YXR1cyAhPT0gXCJpZGxlXCIpIHtcbiAgICAgICAgaGFuZGxlTG9hZGluZ1N0YXR1c0NoYW5nZShpbWFnZUxvYWRpbmdTdGF0dXMpO1xuICAgICAgfVxuICAgIH0sIFtpbWFnZUxvYWRpbmdTdGF0dXMsIGhhbmRsZUxvYWRpbmdTdGF0dXNDaGFuZ2VdKTtcbiAgICByZXR1cm4gaW1hZ2VMb2FkaW5nU3RhdHVzID09PSBcImxvYWRlZFwiID8gLyogQF9fUFVSRV9fICovIGpzeChQcmltaXRpdmUuaW1nLCB7IC4uLmltYWdlUHJvcHMsIHJlZjogZm9yd2FyZGVkUmVmLCBzcmMgfSkgOiBudWxsO1xuICB9XG4pO1xuQXZhdGFySW1hZ2UuZGlzcGxheU5hbWUgPSBJTUFHRV9OQU1FO1xudmFyIEZBTExCQUNLX05BTUUgPSBcIkF2YXRhckZhbGxiYWNrXCI7XG52YXIgQXZhdGFyRmFsbGJhY2sgPSBSZWFjdC5mb3J3YXJkUmVmKFxuICAocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgX19zY29wZUF2YXRhciwgZGVsYXlNcywgLi4uZmFsbGJhY2tQcm9wcyB9ID0gcHJvcHM7XG4gICAgY29uc3QgY29udGV4dCA9IHVzZUF2YXRhckNvbnRleHQoRkFMTEJBQ0tfTkFNRSwgX19zY29wZUF2YXRhcik7XG4gICAgY29uc3QgW2NhblJlbmRlciwgc2V0Q2FuUmVuZGVyXSA9IFJlYWN0LnVzZVN0YXRlKGRlbGF5TXMgPT09IHZvaWQgMCk7XG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIGlmIChkZWxheU1zICE9PSB2b2lkIDApIHtcbiAgICAgICAgY29uc3QgdGltZXJJZCA9IHdpbmRvdy5zZXRUaW1lb3V0KCgpID0+IHNldENhblJlbmRlcih0cnVlKSwgZGVsYXlNcyk7XG4gICAgICAgIHJldHVybiAoKSA9PiB3aW5kb3cuY2xlYXJUaW1lb3V0KHRpbWVySWQpO1xuICAgICAgfVxuICAgIH0sIFtkZWxheU1zXSk7XG4gICAgcmV0dXJuIGNhblJlbmRlciAmJiBjb250ZXh0LmltYWdlTG9hZGluZ1N0YXR1cyAhPT0gXCJsb2FkZWRcIiA/IC8qIEBfX1BVUkVfXyAqLyBqc3goUHJpbWl0aXZlLnNwYW4sIHsgLi4uZmFsbGJhY2tQcm9wcywgcmVmOiBmb3J3YXJkZWRSZWYgfSkgOiBudWxsO1xuICB9XG4pO1xuQXZhdGFyRmFsbGJhY2suZGlzcGxheU5hbWUgPSBGQUxMQkFDS19OQU1FO1xuZnVuY3Rpb24gcmVzb2x2ZUxvYWRpbmdTdGF0dXMoaW1hZ2UsIHNyYykge1xuICBpZiAoIWltYWdlKSB7XG4gICAgcmV0dXJuIFwiaWRsZVwiO1xuICB9XG4gIGlmICghc3JjKSB7XG4gICAgcmV0dXJuIFwiZXJyb3JcIjtcbiAgfVxuICBpZiAoaW1hZ2Uuc3JjICE9PSBzcmMpIHtcbiAgICBpbWFnZS5zcmMgPSBzcmM7XG4gIH1cbiAgcmV0dXJuIGltYWdlLmNvbXBsZXRlICYmIGltYWdlLm5hdHVyYWxXaWR0aCA+IDAgPyBcImxvYWRlZFwiIDogXCJsb2FkaW5nXCI7XG59XG5mdW5jdGlvbiB1c2VJbWFnZUxvYWRpbmdTdGF0dXMoc3JjLCB7IHJlZmVycmVyUG9saWN5LCBjcm9zc09yaWdpbiB9KSB7XG4gIGNvbnN0IGlzSHlkcmF0ZWQgPSB1c2VJc0h5ZHJhdGVkKCk7XG4gIGNvbnN0IGltYWdlUmVmID0gUmVhY3QudXNlUmVmKG51bGwpO1xuICBjb25zdCBpbWFnZSA9ICgoKSA9PiB7XG4gICAgaWYgKCFpc0h5ZHJhdGVkKSByZXR1cm4gbnVsbDtcbiAgICBpZiAoIWltYWdlUmVmLmN1cnJlbnQpIHtcbiAgICAgIGltYWdlUmVmLmN1cnJlbnQgPSBuZXcgd2luZG93LkltYWdlKCk7XG4gICAgfVxuICAgIHJldHVybiBpbWFnZVJlZi5jdXJyZW50O1xuICB9KSgpO1xuICBjb25zdCBbbG9hZGluZ1N0YXR1cywgc2V0TG9hZGluZ1N0YXR1c10gPSBSZWFjdC51c2VTdGF0ZShcbiAgICAoKSA9PiByZXNvbHZlTG9hZGluZ1N0YXR1cyhpbWFnZSwgc3JjKVxuICApO1xuICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgIHNldExvYWRpbmdTdGF0dXMocmVzb2x2ZUxvYWRpbmdTdGF0dXMoaW1hZ2UsIHNyYykpO1xuICB9LCBbaW1hZ2UsIHNyY10pO1xuICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHVwZGF0ZVN0YXR1cyA9IChzdGF0dXMpID0+ICgpID0+IHtcbiAgICAgIHNldExvYWRpbmdTdGF0dXMoc3RhdHVzKTtcbiAgICB9O1xuICAgIGlmICghaW1hZ2UpIHJldHVybjtcbiAgICBjb25zdCBoYW5kbGVMb2FkID0gdXBkYXRlU3RhdHVzKFwibG9hZGVkXCIpO1xuICAgIGNvbnN0IGhhbmRsZUVycm9yID0gdXBkYXRlU3RhdHVzKFwiZXJyb3JcIik7XG4gICAgaW1hZ2UuYWRkRXZlbnRMaXN0ZW5lcihcImxvYWRcIiwgaGFuZGxlTG9hZCk7XG4gICAgaW1hZ2UuYWRkRXZlbnRMaXN0ZW5lcihcImVycm9yXCIsIGhhbmRsZUVycm9yKTtcbiAgICBpZiAocmVmZXJyZXJQb2xpY3kpIHtcbiAgICAgIGltYWdlLnJlZmVycmVyUG9saWN5ID0gcmVmZXJyZXJQb2xpY3k7XG4gICAgfVxuICAgIGlmICh0eXBlb2YgY3Jvc3NPcmlnaW4gPT09IFwic3RyaW5nXCIpIHtcbiAgICAgIGltYWdlLmNyb3NzT3JpZ2luID0gY3Jvc3NPcmlnaW47XG4gICAgfVxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBpbWFnZS5yZW1vdmVFdmVudExpc3RlbmVyKFwibG9hZFwiLCBoYW5kbGVMb2FkKTtcbiAgICAgIGltYWdlLnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJlcnJvclwiLCBoYW5kbGVFcnJvcik7XG4gICAgfTtcbiAgfSwgW2ltYWdlLCBjcm9zc09yaWdpbiwgcmVmZXJyZXJQb2xpY3ldKTtcbiAgcmV0dXJuIGxvYWRpbmdTdGF0dXM7XG59XG52YXIgUm9vdCA9IEF2YXRhcjtcbnZhciBJbWFnZSA9IEF2YXRhckltYWdlO1xudmFyIEZhbGxiYWNrID0gQXZhdGFyRmFsbGJhY2s7XG5leHBvcnQge1xuICBBdmF0YXIsXG4gIEF2YXRhckZhbGxiYWNrLFxuICBBdmF0YXJJbWFnZSxcbiAgRmFsbGJhY2ssXG4gIEltYWdlLFxuICBSb290LFxuICBjcmVhdGVBdmF0YXJTY29wZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHRTY29wZSIsInVzZUNhbGxiYWNrUmVmIiwidXNlTGF5b3V0RWZmZWN0IiwiUHJpbWl0aXZlIiwidXNlSXNIeWRyYXRlZCIsImpzeCIsIkFWQVRBUl9OQU1FIiwiY3JlYXRlQXZhdGFyQ29udGV4dCIsImNyZWF0ZUF2YXRhclNjb3BlIiwiQXZhdGFyUHJvdmlkZXIiLCJ1c2VBdmF0YXJDb250ZXh0IiwiQXZhdGFyIiwiZm9yd2FyZFJlZiIsInByb3BzIiwiZm9yd2FyZGVkUmVmIiwiX19zY29wZUF2YXRhciIsImF2YXRhclByb3BzIiwiaW1hZ2VMb2FkaW5nU3RhdHVzIiwic2V0SW1hZ2VMb2FkaW5nU3RhdHVzIiwidXNlU3RhdGUiLCJzY29wZSIsIm9uSW1hZ2VMb2FkaW5nU3RhdHVzQ2hhbmdlIiwiY2hpbGRyZW4iLCJzcGFuIiwicmVmIiwiZGlzcGxheU5hbWUiLCJJTUFHRV9OQU1FIiwiQXZhdGFySW1hZ2UiLCJzcmMiLCJvbkxvYWRpbmdTdGF0dXNDaGFuZ2UiLCJpbWFnZVByb3BzIiwiY29udGV4dCIsInVzZUltYWdlTG9hZGluZ1N0YXR1cyIsImhhbmRsZUxvYWRpbmdTdGF0dXNDaGFuZ2UiLCJzdGF0dXMiLCJpbWciLCJGQUxMQkFDS19OQU1FIiwiQXZhdGFyRmFsbGJhY2siLCJkZWxheU1zIiwiZmFsbGJhY2tQcm9wcyIsImNhblJlbmRlciIsInNldENhblJlbmRlciIsInVzZUVmZmVjdCIsInRpbWVySWQiLCJ3aW5kb3ciLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwicmVzb2x2ZUxvYWRpbmdTdGF0dXMiLCJpbWFnZSIsImNvbXBsZXRlIiwibmF0dXJhbFdpZHRoIiwicmVmZXJyZXJQb2xpY3kiLCJjcm9zc09yaWdpbiIsImlzSHlkcmF0ZWQiLCJpbWFnZVJlZiIsInVzZVJlZiIsImN1cnJlbnQiLCJJbWFnZSIsImxvYWRpbmdTdGF0dXMiLCJzZXRMb2FkaW5nU3RhdHVzIiwidXBkYXRlU3RhdHVzIiwiaGFuZGxlTG9hZCIsImhhbmRsZUVycm9yIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJSb290IiwiRmFsbGJhY2siXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n    if (typeof ref === \"function\") {\n        return ref(value);\n    } else if (ref !== null && ref !== void 0) {\n        ref.current = value;\n    }\n}\nfunction composeRefs(...refs) {\n    return (node)=>{\n        let hasCleanup = false;\n        const cleanups = refs.map((ref)=>{\n            const cleanup = setRef(ref, node);\n            if (!hasCleanup && typeof cleanup == \"function\") {\n                hasCleanup = true;\n            }\n            return cleanup;\n        });\n        if (hasCleanup) {\n            return ()=>{\n                for(let i = 0; i < cleanups.length; i++){\n                    const cleanup = cleanups[i];\n                    if (typeof cleanup == \"function\") {\n                        cleanup();\n                    } else {\n                        setRef(refs[i], null);\n                    }\n                }\n            };\n        }\n    };\n}\nfunction useComposedRefs(...refs) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n    const Context = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const Provider = (props)=>{\n        const { children, ...context } = props;\n        const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n            value,\n            children\n        });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName) {\n        const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n        if (context) return context;\n        if (defaultContext !== void 0) return defaultContext;\n        throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [\n        Provider,\n        useContext2\n    ];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n    let defaultContexts = [];\n    function createContext3(rootComponentName, defaultContext) {\n        const BaseContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        const index = defaultContexts.length;\n        defaultContexts = [\n            ...defaultContexts,\n            defaultContext\n        ];\n        const Provider = (props)=>{\n            const { scope, children, ...context } = props;\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n                value,\n                children\n            });\n        };\n        Provider.displayName = rootComponentName + \"Provider\";\n        function useContext2(consumerName, scope) {\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n            if (context) return context;\n            if (defaultContext !== void 0) return defaultContext;\n            throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n        }\n        return [\n            Provider,\n            useContext2\n        ];\n    }\n    const createScope = ()=>{\n        const scopeContexts = defaultContexts.map((defaultContext)=>{\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        });\n        return function useScope(scope) {\n            const contexts = scope?.[scopeName] || scopeContexts;\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${scopeName}`]: {\n                        ...scope,\n                        [scopeName]: contexts\n                    }\n                }), [\n                scope,\n                contexts\n            ]);\n        };\n    };\n    createScope.scopeName = scopeName;\n    return [\n        createContext3,\n        composeContextScopes(createScope, ...createContextScopeDeps)\n    ];\n}\nfunction composeContextScopes(...scopes) {\n    const baseScope = scopes[0];\n    if (scopes.length === 1) return baseScope;\n    const createScope = ()=>{\n        const scopeHooks = scopes.map((createScope2)=>({\n                useScope: createScope2(),\n                scopeName: createScope2.scopeName\n            }));\n        return function useComposedScopes(overrideScopes) {\n            const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName })=>{\n                const scopeProps = useScope(overrideScopes);\n                const currentScope = scopeProps[`__scope${scopeName}`];\n                return {\n                    ...nextScopes2,\n                    ...currentScope\n                };\n            }, {});\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${baseScope.scopeName}`]: nextScopes\n                }), [\n                nextScopes\n            ]);\n        };\n    };\n    createScope.scopeName = baseScope.scopeName;\n    return createScope;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: () => (/* binding */ DirectionProvider),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useDirection: () => (/* binding */ useDirection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/direction/src/direction.tsx\n\n\nvar DirectionContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar DirectionProvider = (props)=>{\n    const { dir, children } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DirectionContext.Provider, {\n        value: dir,\n        children\n    });\n};\nfunction useDirection(localDir) {\n    const globalDir = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DirectionContext);\n    return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBLDZDQUE2QztBQUNkO0FBQ1M7QUFDeEMsSUFBSUUsaUNBQW1CRixnREFBbUIsQ0FBQyxLQUFLO0FBQ2hELElBQUlJLG9CQUFvQixDQUFDQztJQUN2QixNQUFNLEVBQUVDLEdBQUcsRUFBRUMsUUFBUSxFQUFFLEdBQUdGO0lBQzFCLE9BQU8sYUFBYSxHQUFHSixzREFBR0EsQ0FBQ0MsaUJBQWlCTSxRQUFRLEVBQUU7UUFBRUMsT0FBT0g7UUFBS0M7SUFBUztBQUMvRTtBQUNBLFNBQVNHLGFBQWFDLFFBQVE7SUFDNUIsTUFBTUMsWUFBWVosNkNBQWdCLENBQUNFO0lBQ25DLE9BQU9TLFlBQVlDLGFBQWE7QUFDbEM7QUFDQSxJQUFJSixXQUFXSjtBQUtiLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFpbm9haS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtZGlyZWN0aW9uL2Rpc3QvaW5kZXgubWpzPzc1ODMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvZGlyZWN0aW9uL3NyYy9kaXJlY3Rpb24udHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIERpcmVjdGlvbkNvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KHZvaWQgMCk7XG52YXIgRGlyZWN0aW9uUHJvdmlkZXIgPSAocHJvcHMpID0+IHtcbiAgY29uc3QgeyBkaXIsIGNoaWxkcmVuIH0gPSBwcm9wcztcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goRGlyZWN0aW9uQ29udGV4dC5Qcm92aWRlciwgeyB2YWx1ZTogZGlyLCBjaGlsZHJlbiB9KTtcbn07XG5mdW5jdGlvbiB1c2VEaXJlY3Rpb24obG9jYWxEaXIpIHtcbiAgY29uc3QgZ2xvYmFsRGlyID0gUmVhY3QudXNlQ29udGV4dChEaXJlY3Rpb25Db250ZXh0KTtcbiAgcmV0dXJuIGxvY2FsRGlyIHx8IGdsb2JhbERpciB8fCBcImx0clwiO1xufVxudmFyIFByb3ZpZGVyID0gRGlyZWN0aW9uUHJvdmlkZXI7XG5leHBvcnQge1xuICBEaXJlY3Rpb25Qcm92aWRlcixcbiAgUHJvdmlkZXIsXG4gIHVzZURpcmVjdGlvblxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImpzeCIsIkRpcmVjdGlvbkNvbnRleHQiLCJjcmVhdGVDb250ZXh0IiwiRGlyZWN0aW9uUHJvdmlkZXIiLCJwcm9wcyIsImRpciIsImNoaWxkcmVuIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZURpcmVjdGlvbiIsImxvY2FsRGlyIiwiZ2xvYmFsRGlyIiwidXNlQ29udGV4dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        if (node) {\n            let timeoutId;\n            const ownerWindow = node.ownerDocument.defaultView ?? window;\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node && isCurrentAnimation) {\n                    send(\"ANIMATION_END\");\n                    if (!prevPresentRef.current) {\n                        const currentFillMode = node.style.animationFillMode;\n                        node.style.animationFillMode = \"forwards\";\n                        timeoutId = ownerWindow.setTimeout(()=>{\n                            if (node.style.animationFillMode === \"forwards\") {\n                                node.style.animationFillMode = currentFillMode;\n                            }\n                        });\n                    }\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                ownerWindow.clearTimeout(timeoutId);\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            stylesRef.current = node2 ? getComputedStyle(node2) : null;\n            setNode(node2);\n        }, [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n    \"a\",\n    \"button\",\n    \"div\",\n    \"form\",\n    \"h2\",\n    \"h3\",\n    \"img\",\n    \"input\",\n    \"label\",\n    \"li\",\n    \"nav\",\n    \"ol\",\n    \"p\",\n    \"select\",\n    \"span\",\n    \"svg\",\n    \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node)=>{\n    const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n    const Node = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { asChild, ...primitiveProps } = props;\n        const Comp = asChild ? Slot : node;\n        if (false) {}\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, {\n            ...primitiveProps,\n            ref: forwardedRef\n        });\n    });\n    Node.displayName = `Primitive.${node}`;\n    return {\n        ...primitive,\n        [node]: Node\n    };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n    if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>target.dispatchEvent(event));\n}\nvar Root = Primitive;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-scroll-area/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Corner: () => (/* binding */ Corner),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollAreaCorner: () => (/* binding */ ScrollAreaCorner),\n/* harmony export */   ScrollAreaScrollbar: () => (/* binding */ ScrollAreaScrollbar),\n/* harmony export */   ScrollAreaThumb: () => (/* binding */ ScrollAreaThumb),\n/* harmony export */   ScrollAreaViewport: () => (/* binding */ ScrollAreaViewport),\n/* harmony export */   Scrollbar: () => (/* binding */ Scrollbar),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createScrollAreaScope: () => (/* binding */ createScrollAreaScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Corner,Root,ScrollArea,ScrollAreaCorner,ScrollAreaScrollbar,ScrollAreaThumb,ScrollAreaViewport,Scrollbar,Thumb,Viewport,createScrollAreaScope auto */ // src/scroll-area.tsx\n\n\n\n\n\n\n\n\n\n\n// src/use-state-machine.ts\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// src/scroll-area.tsx\n\nvar SCROLL_AREA_NAME = \"ScrollArea\";\nvar [createScrollAreaContext, createScrollAreaScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SCROLL_AREA_NAME);\nvar [ScrollAreaProvider, useScrollAreaContext] = createScrollAreaContext(SCROLL_AREA_NAME);\nvar ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, type = \"hover\", dir, scrollHideDelay = 600, ...scrollAreaProps } = props;\n    const [scrollArea, setScrollArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarX, setScrollbarX] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarY, setScrollbarY] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [cornerWidth, setCornerWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [cornerHeight, setCornerHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setScrollArea(node));\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaProvider, {\n        scope: __scopeScrollArea,\n        type,\n        dir: direction,\n        scrollHideDelay,\n        scrollArea,\n        viewport,\n        onViewportChange: setViewport,\n        content,\n        onContentChange: setContent,\n        scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            dir: direction,\n            ...scrollAreaProps,\n            ref: composedRefs,\n            style: {\n                position: \"relative\",\n                // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n                [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n                [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n                ...props.style\n            }\n        })\n    });\n});\nScrollArea.displayName = SCROLL_AREA_NAME;\nvar VIEWPORT_NAME = \"ScrollAreaViewport\";\nvar ScrollAreaViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n                \"data-radix-scroll-area-viewport\": \"\",\n                ...viewportProps,\n                ref: composedRefs,\n                style: {\n                    /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */ overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n                    overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n                    ...props.style\n                },\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n                    ref: context.onContentChange,\n                    style: {\n                        minWidth: \"100%\",\n                        display: \"table\"\n                    },\n                    children\n                })\n            })\n        ]\n    });\n});\nScrollAreaViewport.displayName = VIEWPORT_NAME;\nvar SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nvar ScrollAreaScrollbar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n        return ()=>{\n            isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n        };\n    }, [\n        isHorizontal,\n        onScrollbarXEnabledChange,\n        onScrollbarYEnabledChange\n    ]);\n    return context.type === \"hover\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarHover, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"scroll\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarScroll, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"auto\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"always\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n        ...scrollbarProps,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\nvar ScrollAreaScrollbarHover = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const scrollArea = context.scrollArea;\n        let hideTimer = 0;\n        if (scrollArea) {\n            const handlePointerEnter = ()=>{\n                window.clearTimeout(hideTimer);\n                setVisible(true);\n            };\n            const handlePointerLeave = ()=>{\n                hideTimer = window.setTimeout(()=>setVisible(false), context.scrollHideDelay);\n            };\n            scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n            scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n            return ()=>{\n                window.clearTimeout(hideTimer);\n                scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n                scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n            };\n        }\n    }, [\n        context.scrollArea,\n        context.scrollHideDelay\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarScroll = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const debounceScrollEnd = useDebounceCallback(()=>send(\"SCROLL_END\"), 100);\n    const [state, send] = useStateMachine(\"hidden\", {\n        hidden: {\n            SCROLL: \"scrolling\"\n        },\n        scrolling: {\n            SCROLL_END: \"idle\",\n            POINTER_ENTER: \"interacting\"\n        },\n        interacting: {\n            SCROLL: \"interacting\",\n            POINTER_LEAVE: \"idle\"\n        },\n        idle: {\n            HIDE: \"hidden\",\n            SCROLL: \"scrolling\",\n            POINTER_ENTER: \"interacting\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (state === \"idle\") {\n            const hideTimer = window.setTimeout(()=>send(\"HIDE\"), context.scrollHideDelay);\n            return ()=>window.clearTimeout(hideTimer);\n        }\n    }, [\n        state,\n        context.scrollHideDelay,\n        send\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = context.viewport;\n        const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n        if (viewport) {\n            let prevScrollPos = viewport[scrollDirection];\n            const handleScroll = ()=>{\n                const scrollPos = viewport[scrollDirection];\n                const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n                if (hasScrollInDirectionChanged) {\n                    send(\"SCROLL\");\n                    debounceScrollEnd();\n                }\n                prevScrollPos = scrollPos;\n            };\n            viewport.addEventListener(\"scroll\", handleScroll);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll);\n        }\n    }, [\n        context.viewport,\n        isHorizontal,\n        send,\n        debounceScrollEnd\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || state !== \"hidden\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\",\n            ...scrollbarProps,\n            ref: forwardedRef,\n            onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerEnter, ()=>send(\"POINTER_ENTER\")),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerLeave, ()=>send(\"POINTER_LEAVE\"))\n        })\n    });\n});\nvar ScrollAreaScrollbarAuto = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { forceMount, ...scrollbarProps } = props;\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const handleResize = useDebounceCallback(()=>{\n        if (context.viewport) {\n            const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n            const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n            setVisible(isHorizontal ? isOverflowX : isOverflowY);\n        }\n    }, 10);\n    useResizeObserver(context.viewport, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarVisible = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { orientation = \"vertical\", ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const thumbRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerOffsetRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const [sizes, setSizes] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        content: 0,\n        viewport: 0,\n        scrollbar: {\n            size: 0,\n            paddingStart: 0,\n            paddingEnd: 0\n        }\n    });\n    const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n    const commonProps = {\n        ...scrollbarProps,\n        sizes,\n        onSizesChange: setSizes,\n        hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n        onThumbChange: (thumb)=>thumbRef.current = thumb,\n        onThumbPointerUp: ()=>pointerOffsetRef.current = 0,\n        onThumbPointerDown: (pointerPos)=>pointerOffsetRef.current = pointerPos\n    };\n    function getScrollPosition(pointerPos, dir) {\n        return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n    }\n    if (orientation === \"horizontal\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarX, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollLeft;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n                    thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollLeft = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) {\n                    context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n                }\n            }\n        });\n    }\n    if (orientation === \"vertical\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarY, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollTop;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n                    thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollTop = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n            }\n        });\n    }\n    return null;\n});\nvar ScrollAreaScrollbarX = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarXChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n    }, [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"horizontal\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            bottom: 0,\n            left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            [\"--radix-scroll-area-thumb-width\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.x),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.x),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollLeft + event.deltaX;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollWidth,\n                    viewport: context.viewport.offsetWidth,\n                    scrollbar: {\n                        size: ref.current.clientWidth,\n                        paddingStart: toInt(computedStyle.paddingLeft),\n                        paddingEnd: toInt(computedStyle.paddingRight)\n                    }\n                });\n            }\n        }\n    });\n});\nvar ScrollAreaScrollbarY = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarYChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n    }, [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"vertical\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            top: 0,\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: \"var(--radix-scroll-area-corner-height)\",\n            [\"--radix-scroll-area-thumb-height\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.y),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.y),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollTop + event.deltaY;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollHeight,\n                    viewport: context.viewport.offsetHeight,\n                    scrollbar: {\n                        size: ref.current.clientHeight,\n                        paddingStart: toInt(computedStyle.paddingTop),\n                        paddingEnd: toInt(computedStyle.paddingBottom)\n                    }\n                });\n            }\n        }\n    });\n});\nvar [ScrollbarProvider, useScrollbarContext] = createScrollAreaContext(SCROLLBAR_NAME);\nvar ScrollAreaScrollbarImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, sizes, hasThumb, onThumbChange, onThumbPointerUp, onThumbPointerDown, onThumbPositionChange, onDragScroll, onWheelScroll, onResize, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n    const [scrollbar, setScrollbar] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setScrollbar(node));\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevWebkitUserSelectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const viewport = context.viewport;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const handleWheelScroll = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onWheelScroll);\n    const handleThumbPositionChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPositionChange);\n    const handleResize = useDebounceCallback(onResize, 10);\n    function handleDragScroll(event) {\n        if (rectRef.current) {\n            const x = event.clientX - rectRef.current.left;\n            const y = event.clientY - rectRef.current.top;\n            onDragScroll({\n                x,\n                y\n            });\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleWheel = (event)=>{\n            const element = event.target;\n            const isScrollbarWheel = scrollbar?.contains(element);\n            if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n        };\n        document.addEventListener(\"wheel\", handleWheel, {\n            passive: false\n        });\n        return ()=>document.removeEventListener(\"wheel\", handleWheel, {\n                passive: false\n            });\n    }, [\n        viewport,\n        scrollbar,\n        maxScrollPos,\n        handleWheelScroll\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleThumbPositionChange, [\n        sizes,\n        handleThumbPositionChange\n    ]);\n    useResizeObserver(scrollbar, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollbarProvider, {\n        scope: __scopeScrollArea,\n        scrollbar,\n        hasThumb,\n        onThumbChange: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbChange),\n        onThumbPointerUp: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerUp),\n        onThumbPositionChange: handleThumbPositionChange,\n        onThumbPointerDown: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerDown),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            ...scrollbarProps,\n            ref: composeRefs,\n            style: {\n                position: \"absolute\",\n                ...scrollbarProps.style\n            },\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                const mainPointer = 0;\n                if (event.button === mainPointer) {\n                    const element = event.target;\n                    element.setPointerCapture(event.pointerId);\n                    rectRef.current = scrollbar.getBoundingClientRect();\n                    prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n                    document.body.style.webkitUserSelect = \"none\";\n                    if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n                    handleDragScroll(event);\n                }\n            }),\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerMove, handleDragScroll),\n            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                const element = event.target;\n                if (element.hasPointerCapture(event.pointerId)) {\n                    element.releasePointerCapture(event.pointerId);\n                }\n                document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n                if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n                rectRef.current = null;\n            })\n        })\n    });\n});\nvar THUMB_NAME = \"ScrollAreaThumb\";\nvar ScrollAreaThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || scrollbarContext.hasThumb,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaThumbImpl, {\n            ref: forwardedRef,\n            ...thumbProps\n        })\n    });\n});\nvar ScrollAreaThumbImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>scrollbarContext.onThumbChange(node));\n    const removeUnlinkedScrollListenerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const debounceScrollEnd = useDebounceCallback(()=>{\n        if (removeUnlinkedScrollListenerRef.current) {\n            removeUnlinkedScrollListenerRef.current();\n            removeUnlinkedScrollListenerRef.current = void 0;\n        }\n    }, 100);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = scrollAreaContext.viewport;\n        if (viewport) {\n            const handleScroll = ()=>{\n                debounceScrollEnd();\n                if (!removeUnlinkedScrollListenerRef.current) {\n                    const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n                    removeUnlinkedScrollListenerRef.current = listener;\n                    onThumbPositionChange();\n                }\n            };\n            onThumbPositionChange();\n            viewport.addEventListener(\"scroll\", handleScroll);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll);\n        }\n    }, [\n        scrollAreaContext.viewport,\n        debounceScrollEnd,\n        onThumbPositionChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\",\n        ...thumbProps,\n        ref: composedRef,\n        style: {\n            width: \"var(--radix-scroll-area-thumb-width)\",\n            height: \"var(--radix-scroll-area-thumb-height)\",\n            ...style\n        },\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownCapture, (event)=>{\n            const thumb = event.target;\n            const thumbRect = thumb.getBoundingClientRect();\n            const x = event.clientX - thumbRect.left;\n            const y = event.clientY - thumbRect.top;\n            scrollbarContext.onThumbPointerDown({\n                x,\n                y\n            });\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n    });\n});\nScrollAreaThumb.displayName = THUMB_NAME;\nvar CORNER_NAME = \"ScrollAreaCorner\";\nvar ScrollAreaCorner = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaCornerImpl, {\n        ...props,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaCorner.displayName = CORNER_NAME;\nvar ScrollAreaCornerImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, ...cornerProps } = props;\n    const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n    const [width, setWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [height, setHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const hasSize = Boolean(width && height);\n    useResizeObserver(context.scrollbarX, ()=>{\n        const height2 = context.scrollbarX?.offsetHeight || 0;\n        context.onCornerHeightChange(height2);\n        setHeight(height2);\n    });\n    useResizeObserver(context.scrollbarY, ()=>{\n        const width2 = context.scrollbarY?.offsetWidth || 0;\n        context.onCornerWidthChange(width2);\n        setWidth(width2);\n    });\n    return hasSize ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        ...cornerProps,\n        ref: forwardedRef,\n        style: {\n            width,\n            height,\n            position: \"absolute\",\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: 0,\n            ...props.style\n        }\n    }) : null;\n});\nfunction toInt(value) {\n    return value ? parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n    const ratio = viewportSize / contentSize;\n    return isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n    const ratio = getThumbRatio(sizes.viewport, sizes.content);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n    return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer(pointerPos, pointerOffset, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const thumbCenter = thumbSizePx / 2;\n    const offset = pointerOffset || thumbCenter;\n    const thumbOffsetFromEnd = thumbSizePx - offset;\n    const minPointerPos = sizes.scrollbar.paddingStart + offset;\n    const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const scrollRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const interpolate = linearScale([\n        minPointerPos,\n        maxPointerPos\n    ], scrollRange);\n    return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll(scrollPos, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const maxThumbPos = scrollbar - thumbSizePx;\n    const scrollClampRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const scrollWithoutMomentum = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_9__.clamp)(scrollPos, scrollClampRange);\n    const interpolate = linearScale([\n        0,\n        maxScrollPos\n    ], [\n        0,\n        maxThumbPos\n    ]);\n    return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n    return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nvar addUnlinkedScrollListener = (node, handler = ()=>{})=>{\n    let prevPosition = {\n        left: node.scrollLeft,\n        top: node.scrollTop\n    };\n    let rAF = 0;\n    (function loop() {\n        const position = {\n            left: node.scrollLeft,\n            top: node.scrollTop\n        };\n        const isHorizontalScroll = prevPosition.left !== position.left;\n        const isVerticalScroll = prevPosition.top !== position.top;\n        if (isHorizontalScroll || isVerticalScroll) handler();\n        prevPosition = position;\n        rAF = window.requestAnimationFrame(loop);\n    })();\n    return ()=>window.cancelAnimationFrame(rAF);\n};\nfunction useDebounceCallback(callback, delay) {\n    const handleCallback = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(callback);\n    const debounceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>()=>window.clearTimeout(debounceTimerRef.current), []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(debounceTimerRef.current);\n        debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n    }, [\n        handleCallback,\n        delay\n    ]);\n}\nfunction useResizeObserver(element, onResize) {\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__.useLayoutEffect)(()=>{\n        let rAF = 0;\n        if (element) {\n            const resizeObserver = new ResizeObserver(()=>{\n                cancelAnimationFrame(rAF);\n                rAF = window.requestAnimationFrame(handleResize);\n            });\n            resizeObserver.observe(element);\n            return ()=>{\n                window.cancelAnimationFrame(rAF);\n                resizeObserver.unobserve(element);\n            };\n        }\n    }, [\n        element,\n        handleResize\n    ]);\n}\nvar Root = ScrollArea;\nvar Viewport = ScrollAreaViewport;\nvar Scrollbar = ScrollAreaScrollbar;\nvar Thumb = ScrollAreaThumb;\nvar Corner = ScrollAreaCorner;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n    const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n    const Slot2 = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n        const slottable = childrenArray.find(isSlottable);\n        if (slottable) {\n            const newElement = slottable.props.children;\n            const newChildren = childrenArray.map((child)=>{\n                if (child === slottable) {\n                    if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n                } else {\n                    return child;\n                }\n            });\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n                ...slotProps,\n                ref: forwardedRef,\n                children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null\n            });\n        }\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n            ...slotProps,\n            ref: forwardedRef,\n            children\n        });\n    });\n    Slot2.displayName = `${ownerName}.Slot`;\n    return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n    const SlotClone = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n            const childrenRef = getElementRef(children);\n            const props2 = mergeProps(slotProps, children.props);\n            if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n                props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n            }\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n        }\n        return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n    });\n    SlotClone.displayName = `${ownerName}.SlotClone`;\n    return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n    const Slottable2 = ({ children })=>{\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n            children\n        });\n    };\n    Slottable2.displayName = `${ownerName}.Slottable`;\n    Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n    return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n    const overrideProps = {\n        ...childProps\n    };\n    for(const propName in childProps){\n        const slotPropValue = slotProps[propName];\n        const childPropValue = childProps[propName];\n        const isHandler = /^on[A-Z]/.test(propName);\n        if (isHandler) {\n            if (slotPropValue && childPropValue) {\n                overrideProps[propName] = (...args)=>{\n                    const result = childPropValue(...args);\n                    slotPropValue(...args);\n                    return result;\n                };\n            } else if (slotPropValue) {\n                overrideProps[propName] = slotPropValue;\n            }\n        } else if (propName === \"style\") {\n            overrideProps[propName] = {\n                ...slotPropValue,\n                ...childPropValue\n            };\n        } else if (propName === \"className\") {\n            overrideProps[propName] = [\n                slotPropValue,\n                childPropValue\n            ].filter(Boolean).join(\" \");\n        }\n    }\n    return {\n        ...slotProps,\n        ...overrideProps\n    };\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n    const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        callbackRef.current = callback;\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>callbackRef.current?.(...args), []);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSwyREFBMkQ7QUFDNUI7QUFDL0IsU0FBU0MsZUFBZUMsUUFBUTtJQUM5QixNQUFNQyxjQUFjSCx5Q0FBWSxDQUFDRTtJQUNqQ0YsNENBQWUsQ0FBQztRQUNkRyxZQUFZRyxPQUFPLEdBQUdKO0lBQ3hCO0lBQ0EsT0FBT0YsMENBQWEsQ0FBQyxJQUFNLENBQUMsR0FBR1EsT0FBU0wsWUFBWUcsT0FBTyxNQUFNRSxPQUFPLEVBQUU7QUFDNUU7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL3RhaW5vYWkvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanM/MTFmMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtY2FsbGJhY2stcmVmL3NyYy91c2UtY2FsbGJhY2stcmVmLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VDYWxsYmFja1JlZihjYWxsYmFjaykge1xuICBjb25zdCBjYWxsYmFja1JlZiA9IFJlYWN0LnVzZVJlZihjYWxsYmFjayk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2FsbGJhY2tSZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKC4uLmFyZ3MpID0+IGNhbGxiYWNrUmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlQ2FsbGJhY2tSZWZcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VDYWxsYmFja1JlZiIsImNhbGxiYWNrIiwiY2FsbGJhY2tSZWYiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJjdXJyZW50IiwidXNlTWVtbyIsImFyZ3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsHydrated: () => (/* binding */ useIsHydrated)\n/* harmony export */ });\n/* harmony import */ var use_sync_external_store_shim__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sync-external-store/shim */ \"(ssr)/./node_modules/use-sync-external-store/shim/index.js\");\n// src/use-is-hydrated.tsx\n\nfunction useIsHydrated() {\n    return (0,use_sync_external_store_shim__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore)(subscribe, ()=>true, ()=>false);\n}\nfunction subscribe() {\n    return ()=>{};\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1pcy1oeWRyYXRlZC9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLDBCQUEwQjtBQUMwQztBQUNwRSxTQUFTQztJQUNQLE9BQU9ELGtGQUFvQkEsQ0FDekJFLFdBQ0EsSUFBTSxNQUNOLElBQU07QUFFVjtBQUNBLFNBQVNBO0lBQ1AsT0FBTyxLQUNQO0FBQ0Y7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL3RhaW5vYWkvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1pcy1oeWRyYXRlZC9kaXN0L2luZGV4Lm1qcz83YTUxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy91c2UtaXMtaHlkcmF0ZWQudHN4XG5pbXBvcnQgeyB1c2VTeW5jRXh0ZXJuYWxTdG9yZSB9IGZyb20gXCJ1c2Utc3luYy1leHRlcm5hbC1zdG9yZS9zaGltXCI7XG5mdW5jdGlvbiB1c2VJc0h5ZHJhdGVkKCkge1xuICByZXR1cm4gdXNlU3luY0V4dGVybmFsU3RvcmUoXG4gICAgc3Vic2NyaWJlLFxuICAgICgpID0+IHRydWUsXG4gICAgKCkgPT4gZmFsc2VcbiAgKTtcbn1cbmZ1bmN0aW9uIHN1YnNjcmliZSgpIHtcbiAgcmV0dXJuICgpID0+IHtcbiAgfTtcbn1cbmV4cG9ydCB7XG4gIHVzZUlzSHlkcmF0ZWRcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsidXNlU3luY0V4dGVybmFsU3RvcmUiLCJ1c2VJc0h5ZHJhdGVkIiwic3Vic2NyaWJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : ()=>{};\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsNkRBQTZEO0FBQzlCO0FBQy9CLElBQUlDLG1CQUFtQkMsWUFBWUMsV0FBV0gsa0RBQXFCLEdBQUcsS0FDdEU7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL3RhaW5vYWkvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzPzJkNmYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWxheW91dC1lZmZlY3Qvc3JjL3VzZS1sYXlvdXQtZWZmZWN0LnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG52YXIgdXNlTGF5b3V0RWZmZWN0MiA9IGdsb2JhbFRoaXM/LmRvY3VtZW50ID8gUmVhY3QudXNlTGF5b3V0RWZmZWN0IDogKCkgPT4ge1xufTtcbmV4cG9ydCB7XG4gIHVzZUxheW91dEVmZmVjdDIgYXMgdXNlTGF5b3V0RWZmZWN0XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlTGF5b3V0RWZmZWN0MiIsImdsb2JhbFRoaXMiLCJkb2N1bWVudCIsInVzZUxheW91dEVmZmVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ })

};
;