(()=>{var e={};e.id=165,e.ids=[165],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},9709:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=n(482),s=n(9108),o=n(2563),i=n.n(o),a=n(8300),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);n.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.t.bind(n,9361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(n.bind(n,2917)),"C:\\Users\\<USER>\\Documents\\augment-projects\\tainoai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,9361,23)),"next/dist/client/components/not-found-error"]}],c=[],p="/_not-found",u={require:n,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1917:(e,t,n)=>{Promise.resolve().then(n.bind(n,4669))},9437:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,2583,23)),Promise.resolve().then(n.t.bind(n,6840,23)),Promise.resolve().then(n.t.bind(n,8771,23)),Promise.resolve().then(n.t.bind(n,3225,23)),Promise.resolve().then(n.t.bind(n,9295,23)),Promise.resolve().then(n.t.bind(n,3982,23))},2917:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>l,metadata:()=>a});var r=n(5036),s=n(265),o=n.n(s);n(7272);var i=n(9636);let a={title:"TainoAI Rebirth v6.0 GENESIS",description:"Independent AI Assistant - Built from scratch with Next.js, FastAPI, and PyTorch",keywords:["AI","Assistant","TainoAI","Independent","PyTorch","FastAPI","Next.js"],authors:[{name:"TainoAI Team"}],viewport:"width=device-width, initial-scale=1",themeColor:"#9c6bff",openGraph:{title:"TainoAI Rebirth v6.0 GENESIS",description:"Independent AI Assistant - Built from scratch",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"TainoAI Rebirth v6.0 GENESIS",description:"Independent AI Assistant - Built from scratch"},robots:{index:!0,follow:!0}};function l({children:e}){return(0,r.jsxs)("html",{lang:"en",className:"dark",children:[(0,r.jsxs)("head",{children:[r.jsx("link",{rel:"icon",href:"/favicon.ico"}),r.jsx("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),r.jsx("meta",{name:"theme-color",content:"#9c6bff"})]}),(0,r.jsxs)("body",{className:`${o().className} antialiased`,children:[r.jsx("div",{className:"min-h-screen animated-gradient",children:e}),r.jsx(i.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"rgba(255, 255, 255, 0.1)",backdropFilter:"blur(10px)",border:"1px solid rgba(255, 255, 255, 0.2)",color:"#fff"}}})]})]})}},7272:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[816],()=>n(9709));module.exports=r})();