(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{6007:function(e,t,r){Promise.resolve().then(r.bind(r,9344))},9344:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return _}});var s=r(7437),a=r(2265),l=r(8173),i=r(1393),o=r(7256),n=r(6061),c=r(7042),d=r(4769);function u(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,d.m6)((0,c.W)(t))}let h=(0,n.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:i=!1,...n}=e,c=i?o.g7:"button";return(0,s.jsx)(c,{className:u(h({variant:a,size:l,className:r})),ref:t,...n})});m.displayName="Button";let x=a.forwardRef((e,t)=>{let{className:r,type:a,...l}=e;return(0,s.jsx)("input",{type:a,className:u("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...l})});x.displayName="Input";var b=r(1465);let f=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(b.fC,{ref:t,className:u("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",r),...a})});f.displayName=b.fC.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(b.Ee,{ref:t,className:u("aspect-square h-full w-full",r),...a})}).displayName=b.Ee.displayName;let p=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(b.NY,{ref:t,className:u("flex h-full w-full items-center justify-center rounded-full bg-muted",r),...a})});p.displayName=b.NY.displayName;var g=r(88);let w=a.forwardRef((e,t)=>{let{className:r,children:a,...l}=e;return(0,s.jsxs)(g.fC,{ref:t,className:u("relative overflow-hidden",r),...l,children:[(0,s.jsx)(g.l_,{className:"h-full w-full rounded-[inherit]",children:a}),(0,s.jsx)(y,{}),(0,s.jsx)(g.Ns,{})]})});w.displayName=g.fC.displayName;let y=a.forwardRef((e,t)=>{let{className:r,orientation:a="vertical",...l}=e;return(0,s.jsx)(g.gb,{ref:t,orientation:a,className:u("flex touch-none select-none transition-colors","vertical"===a&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",r),...l,children:(0,s.jsx)(g.q4,{className:"relative flex-1 rounded-full bg-border"})})});y.displayName=g.gb.displayName;var v=r(3966),j=r(1541),N=r(4135),k=r(3088),C=r(5432),D=r(6637),E=r(1138),S=r(2851),T=r(7972),A=r(6020),Z=r(5925);function _(){let[e,t]=(0,a.useState)([]),[r,o]=(0,a.useState)(""),[n,c]=(0,a.useState)(!1),[d,u]=(0,a.useState)(!1),[h,b]=(0,a.useState)(!0),[g,y]=(0,a.useState)(!1),[_,P]=(0,a.useState)([]),[I,R]=(0,a.useState)(!1),z=(0,a.useRef)(null),F=(0,a.useRef)(null),O=(0,a.useRef)(null),U=()=>{var e;null===(e=z.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})};(0,a.useEffect)(()=>{U()},[e]);let W=async()=>{if(!r.trim()||n)return;let e={id:Date.now().toString(),content:r.trim(),role:"user",timestamp:new Date};t(t=>[...t,e]),o(""),c(!0),u(!0);try{let r=await fetch("/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:e.content,conversation_id:"default",user_id:"user",temperature:.8,max_tokens:512})});if(!r.ok)throw Error("HTTP error! status: ".concat(r.status));let s=await r.json();setTimeout(()=>{u(!1);let e={id:(Date.now()+1).toString(),content:s.response,role:"assistant",timestamp:new Date};t(t=>[...t,e])},1e3)}catch(e){console.error("Error:",e),u(!1),Z.toast.error("Failed to send message. Please try again.")}finally{c(!1)}},Y=async e=>{var t;let r=null===(t=e.target.files)||void 0===t?void 0:t[0];if(!r)return;R(!0);let s=new FormData;s.append("file",r),s.append("user_id","default");try{let e=await fetch("/upload",{method:"POST",body:s}),t=await e.json();t.success?(Z.toast.success("Document uploaded successfully! ".concat(t.message)),q()):Z.toast.error(t.error||"Upload failed")}catch(e){console.error("Upload error:",e),Z.toast.error("Failed to upload document")}finally{R(!1),O.current&&(O.current.value="")}},q=async()=>{try{let e=await fetch("/documents/default"),t=await e.json();P(t.documents||[])}catch(e){console.error("Error loading documents:",e)}},G=async e=>{try{let t=await fetch("/documents/".concat(e,"?user_id=default"),{method:"DELETE"});(await t.json()).success?(Z.toast.success("Document deleted successfully"),q()):Z.toast.error("Failed to delete document")}catch(e){console.error("Delete error:",e),Z.toast.error("Failed to delete document")}};return(0,a.useEffect)(()=>{q()},[]),(0,s.jsx)("div",{className:"min-h-screen transition-all duration-500 ".concat(h?"bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900":"bg-gradient-to-br from-purple-100 via-blue-100 to-indigo-100"),children:(0,s.jsxs)("div",{className:"container mx-auto max-w-4xl h-screen flex flex-col p-4",children:[(0,s.jsx)(l.E.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"backdrop-blur-md rounded-2xl border p-6 mb-4 ".concat(h?"bg-white/10 border-white/20 text-white":"bg-white/40 border-white/60 text-gray-800"),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(f,{className:"w-12 h-12 border-2 border-purple-400",children:(0,s.jsx)(p,{className:"bg-gradient-to-r from-purple-600 to-blue-600",children:(0,s.jsx)(v.Z,{className:"w-6 h-6 text-white"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent",children:"TainoAI"}),(0,s.jsx)("p",{className:"text-sm ".concat(h?"text-gray-300":"text-gray-600"),children:"ChatGPT-style AI Assistant"})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(m,{variant:"ghost",size:"sm",onClick:()=>y(!g),className:"".concat(h?"hover:bg-white/10":"hover:bg-black/10"),children:(0,s.jsx)(j.Z,{className:"w-4 h-4"})}),(0,s.jsx)(m,{variant:"ghost",size:"sm",onClick:()=>{b(!h),Z.toast.success("Switched to ".concat(h?"light":"dark"," theme"))},className:"".concat(h?"hover:bg-white/10":"hover:bg-black/10"),children:h?(0,s.jsx)(N.Z,{className:"w-4 h-4"}):(0,s.jsx)(k.Z,{className:"w-4 h-4"})}),(0,s.jsx)(m,{variant:"ghost",size:"sm",onClick:()=>{t([]),Z.toast.success("Conversation reset!")},className:"".concat(h?"hover:bg-white/10":"hover:bg-black/10"),children:(0,s.jsx)(C.Z,{className:"w-4 h-4"})})]})]})}),g&&(0,s.jsxs)(l.E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"backdrop-blur-md rounded-2xl border p-6 mb-4 ".concat(h?"bg-white/10 border-white/20 text-white":"bg-white/40 border-white/60 text-gray-800"),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"\uD83D\uDCDA Document Learning"}),(0,s.jsx)("span",{className:"text-sm opacity-70",children:"Upload documents to teach TainoAI"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"border-2 border-dashed rounded-xl p-6 text-center ".concat(h?"border-white/30":"border-gray-300"),children:[(0,s.jsx)(j.Z,{className:"w-8 h-8 mx-auto mb-2 opacity-60"}),(0,s.jsxs)("p",{className:"mb-4",children:[(0,s.jsx)("strong",{children:"Supported formats:"}),(0,s.jsx)("br",{}),"PDF, Word, Excel, Text, CSV"]}),(0,s.jsx)("input",{ref:O,type:"file",onChange:Y,accept:".pdf,.docx,.doc,.xlsx,.xls,.txt,.md,.csv",className:"hidden"}),(0,s.jsx)(m,{onClick:()=>{var e;return null===(e=O.current)||void 0===e?void 0:e.click()},disabled:I,className:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white",children:I?"Uploading...":"Choose File"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"font-medium mb-3",children:["\uD83D\uDCC4 Your Documents (",_.length,")"]}),(0,s.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:0===_.length?(0,s.jsx)("p",{className:"text-sm opacity-60",children:"No documents uploaded yet"}):_.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg ".concat(h?"bg-white/10":"bg-white/30"),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(D.Z,{className:"w-4 h-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:e.filename}),(0,s.jsxs)("p",{className:"text-xs opacity-60",children:[e.word_count," words"]})]})]}),(0,s.jsx)(m,{variant:"ghost",size:"sm",onClick:()=>G(e.id),className:"text-red-400 hover:text-red-300",children:(0,s.jsx)(E.Z,{className:"w-4 h-4"})})]},e.id))})]})]})]}),(0,s.jsx)("div",{className:"flex-1 backdrop-blur-md rounded-2xl border p-4 mb-4 overflow-hidden ".concat(h?"bg-white/5 border-white/10":"bg-white/30 border-white/40"),children:(0,s.jsx)(w,{className:"h-full",children:(0,s.jsxs)("div",{className:"space-y-4 pr-4",children:[(0,s.jsx)(i.M,{children:0===e.length?(0,s.jsxs)(l.E.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center py-12",children:[(0,s.jsx)("div",{className:"w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-center",children:(0,s.jsx)(S.Z,{className:"w-10 h-10 text-white"})}),(0,s.jsx)("h2",{className:"text-2xl font-bold mb-2 ".concat(h?"text-white":"text-gray-800"),children:"Welcome to TainoAI"}),(0,s.jsx)("p",{className:"mb-6 ".concat(h?"text-gray-300":"text-gray-600"),children:"Your ChatGPT-style AI assistant powered by Mistral 7B"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto",children:["Explain quantum computing","Write a Python function","Latest AI research trends","Create a business plan"].map((e,t)=>(0,s.jsx)(m,{variant:"outline",className:"h-auto p-4 text-left backdrop-blur-sm ".concat(h?"bg-white/10 border-white/20 hover:bg-white/20 text-white":"bg-white/30 border-white/40 hover:bg-white/50 text-gray-800"),onClick:()=>o(e),children:e},t))})]}):e.map(e=>(0,s.jsx)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,s.jsxs)("div",{className:"flex items-start space-x-3 max-w-[80%] ".concat("user"===e.role?"flex-row-reverse space-x-reverse":""),children:[(0,s.jsx)(f,{className:"w-8 h-8 flex-shrink-0",children:(0,s.jsx)(p,{className:"user"===e.role?"bg-blue-600":"bg-gradient-to-r from-purple-600 to-blue-600",children:"user"===e.role?(0,s.jsx)(T.Z,{className:"w-4 h-4"}):(0,s.jsx)(v.Z,{className:"w-4 h-4"})})}),(0,s.jsx)("div",{className:"backdrop-blur-sm rounded-2xl p-4 ".concat("user"===e.role?h?"bg-blue-600/80 text-white":"bg-blue-500/80 text-white":h?"bg-white/10 border border-white/20 text-white":"bg-white/40 border border-white/40 text-gray-800"),children:(0,s.jsx)("p",{className:"whitespace-pre-wrap",children:e.content})})]})},e.id))}),d&&(0,s.jsx)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"flex justify-start",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)(f,{className:"w-8 h-8",children:(0,s.jsx)(p,{className:"bg-gradient-to-r from-purple-600 to-blue-600",children:(0,s.jsx)(v.Z,{className:"w-4 h-4"})})}),(0,s.jsx)("div",{className:"backdrop-blur-sm rounded-2xl p-4 ".concat(h?"bg-white/10 border border-white/20":"bg-white/40 border border-white/40"),children:(0,s.jsxs)("div",{className:"flex space-x-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full animate-bounce ".concat(h?"bg-white":"bg-gray-600"),style:{animationDelay:"0ms"}}),(0,s.jsx)("div",{className:"w-2 h-2 rounded-full animate-bounce ".concat(h?"bg-white":"bg-gray-600"),style:{animationDelay:"150ms"}}),(0,s.jsx)("div",{className:"w-2 h-2 rounded-full animate-bounce ".concat(h?"bg-white":"bg-gray-600"),style:{animationDelay:"300ms"}})]})})]})}),(0,s.jsx)("div",{ref:z})]})})}),(0,s.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"backdrop-blur-md rounded-2xl border p-4 ".concat(h?"bg-white/10 border-white/20":"bg-white/40 border-white/60"),children:[(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)(x,{ref:F,value:r,onChange:e=>o(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),W())},placeholder:"Ask me anything...",className:"flex-1 backdrop-blur-sm border-0 rounded-xl ".concat(h?"bg-white/10 text-white placeholder:text-white/60":"bg-white/30 text-gray-800 placeholder:text-gray-600"),disabled:n}),(0,s.jsx)(m,{onClick:W,disabled:!r.trim()||n,className:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl px-6",children:n?(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}):(0,s.jsx)(A.Z,{className:"w-4 h-4"})})]}),(0,s.jsx)("div",{className:"mt-2 text-xs text-center ".concat(h?"text-gray-400":"text-gray-600"),children:"TainoAI - ChatGPT-style AI Assistant"})]})]})})}}},function(e){e.O(0,[358,971,938,744],function(){return e(e.s=6007)}),_N_E=e.O()}]);