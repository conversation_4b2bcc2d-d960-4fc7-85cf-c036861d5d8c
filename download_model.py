#!/usr/bin/env python3
"""
TainoAI Model Downloader - Full AI Power
Downloads Full Mistral 7B Q4 quantized model (3.9GB) for production deployment
"""

import os
import sys
import requests
import hashlib
import logging
import time
from pathlib import Path
from typing import Optional

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TainoAIModelDownloader:
    def __init__(self):
        self.model_url = "https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.1-GGUF/resolve/main/mistral-7b-instruct-v0.1.Q4_K_M.gguf"
        self.model_dir = Path("models")
        self.model_filename = "mistral-7b-instruct-v0.1.Q4_K_M.gguf"
        self.model_path = self.model_dir / self.model_filename
        self.expected_size = 4_368_439_424  # ~4.1GB (3.9GB actual)
        self.chunk_size = 1024 * 1024  # 1MB chunks for better performance

    def create_directories(self):
        """Create necessary directories"""
        self.model_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"📁 Model directory: {self.model_dir.absolute()}")

    def check_existing_model(self) -> bool:
        """Check if model already exists and is valid"""
        if not self.model_path.exists():
            logger.info("❌ Model not found - will download")
            return False

        file_size = self.model_path.stat().st_size
        size_gb = file_size / (1024**3)
        logger.info(f"📊 Existing model: {file_size:,} bytes ({size_gb:.2f} GB)")

        # Check if file size is reasonable (allow 5% variance)
        if file_size < self.expected_size * 0.95:
            logger.warning("⚠️ Model file appears incomplete - will re-download")
            return False

        # Quick integrity check - read first and last 1KB
        try:
            with open(self.model_path, 'rb') as f:
                header = f.read(1024)
                f.seek(-1024, 2)  # Seek to 1KB from end
                footer = f.read(1024)

                if len(header) < 1024 or len(footer) < 1024:
                    logger.warning("⚠️ Model file appears corrupted - will re-download")
                    return False

        except Exception as e:
            logger.warning(f"⚠️ Cannot verify model integrity: {e} - will re-download")
            return False

        logger.info("✅ Valid model found!")
        return True

    def download_with_resume(self) -> bool:
        """Download model with resume capability and progress tracking"""
        try:
            # Check if partial download exists
            resume_pos = 0
            if self.model_path.exists():
                resume_pos = self.model_path.stat().st_size
                logger.info(f"🔄 Resuming download from {resume_pos:,} bytes")

            # Setup headers for resume
            headers = {}
            if resume_pos > 0:
                headers['Range'] = f'bytes={resume_pos}-'

            logger.info(f"🚀 Downloading from: {self.model_url}")
            response = requests.get(self.model_url, headers=headers, stream=True)

            if response.status_code == 416:  # Range not satisfiable
                logger.info("📁 File already complete")
                return True
            elif response.status_code not in [200, 206]:
                response.raise_for_status()

            # Get total size
            if 'content-range' in response.headers:
                total_size = int(response.headers['content-range'].split('/')[-1])
            else:
                total_size = int(response.headers.get('content-length', 0)) + resume_pos

            logger.info(f"📦 Total size: {total_size:,} bytes ({total_size / (1024**3):.2f} GB)")

            # Download with progress
            downloaded = resume_pos
            last_update = time.time()

            mode = 'ab' if resume_pos > 0 else 'wb'
            with open(self.model_path, mode) as f:
                for chunk in response.iter_content(chunk_size=self.chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)

                        # Update progress every 2 seconds
                        now = time.time()
                        if now - last_update >= 2.0:
                            progress = (downloaded / total_size) * 100 if total_size > 0 else 0
                            speed = len(chunk) / (now - last_update) / (1024**2)  # MB/s
                            logger.info(f"📥 Progress: {progress:.1f}% ({downloaded/(1024**3):.2f}/{total_size/(1024**3):.2f} GB) - {speed:.1f} MB/s")
                            last_update = now

            final_size = self.model_path.stat().st_size
            logger.info(f"✅ Download complete: {final_size:,} bytes ({final_size / (1024**3):.2f} GB)")

            return final_size >= self.expected_size * 0.95

        except Exception as e:
            logger.error(f"❌ Download failed: {e}")
            return False

    def download_model(self, force: bool = False) -> bool:
        """Main method to download and verify model"""
        logger.info("🤖 TainoAI Full Model Downloader")
        logger.info("=" * 60)

        self.create_directories()

        if not force and self.check_existing_model():
            logger.info("✅ Model already available and valid")
            return True

        if force and self.model_path.exists():
            logger.info("🗑️ Removing existing model (force download)")
            self.model_path.unlink()

        logger.info("📥 Downloading Full Mistral 7B model (3.9GB)...")
        if not self.download_with_resume():
            logger.error("❌ Download failed")
            return False

        logger.info("🔍 Verifying model...")
        if not self.verify_model():
            logger.error("❌ Model verification failed")
            return False

        logger.info("🎉 Model download and verification complete!")
        logger.info(f"📍 Model location: {self.model_path.absolute()}")
        return True

    def verify_model(self) -> bool:
        """Verify model integrity"""
        if not self.model_path.exists():
            return False

        file_size = self.model_path.stat().st_size
        logger.info(f"🔍 Verifying model: {file_size:,} bytes")

        # Basic size check
        if file_size < self.expected_size * 0.95:
            logger.error("❌ Model file too small")
            return False

        # Check if file is readable
        try:
            with open(self.model_path, 'rb') as f:
                header = f.read(1024)
                if len(header) < 1024:
                    logger.error("❌ Model file appears corrupted")
                    return False
        except Exception as e:
            logger.error(f"❌ Cannot read model file: {e}")
            return False

        logger.info("✅ Model verification passed")
        return True

def download_model(force: bool = False) -> str:
    """Legacy function for compatibility"""
    downloader = TainoAIModelDownloader()
    success = downloader.download_model(force=force)
    return str(downloader.model_path) if success else None

def setup_environment():
    """Set up environment variables for TainoAI"""
    model_path = Path("models/mistral-7b-instruct-v0.1.Q4_K_M.gguf")

    # Set environment variables
    if model_path.exists():
        os.environ["MODEL_PATH"] = str(model_path.absolute())
        os.environ["TAINOAI_MODE"] = "FULL_AI"
        os.environ["TAINOAI_VERSION"] = "8.0.0"
        logger.info(f"📝 Set MODEL_PATH={os.environ['MODEL_PATH']}")
        logger.info(f"📝 Set TAINOAI_MODE={os.environ['TAINOAI_MODE']}")

    # Set optimized defaults for full AI
    os.environ.setdefault("LLAMA_THREADS", "4")
    os.environ.setdefault("LLAMA_CONTEXT_SIZE", "4096")
    os.environ.setdefault("LLAMA_MAX_TOKENS", "1024")
    logger.info(f"📝 Set LLAMA_THREADS={os.environ['LLAMA_THREADS']}")

def main():
    """Main setup function"""
    logger.info("🚀 TainoAI Full AI Setup - Mistral 7B (3.9GB)")
    logger.info("=" * 60)

    # Check for force flag
    force = "--force" in sys.argv
    if force:
        logger.info("🔄 Force download enabled")

    # Download model
    downloader = TainoAIModelDownloader()
    success = downloader.download_model(force=force)

    if not success:
        logger.error("❌ Failed to download model")
        return False

    # Setup environment
    setup_environment()

    logger.info("✅ TainoAI Full AI Setup Complete!")
    logger.info("🎯 Model ready for Docker deployment")
    logger.info("🚀 Run: docker build -f Dockerfile.llama -t tainoai .")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
