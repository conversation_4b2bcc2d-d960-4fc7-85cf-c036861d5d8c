#!/usr/bin/env python3
"""
TainoAI Model Downloader
Downloads Mistral 7B Q4 quantized model for lightweight deployment
"""

import os
import requests
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def download_model():
    """Download Mistral 7B Q4 model"""
    
    # Model details
    model_url = "https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.1-GGUF/resolve/main/mistral-7b-instruct-v0.1.Q4_K_M.gguf"
    model_dir = Path("models")
    model_path = model_dir / "mistral-7b-instruct-v0.1.Q4_K_M.gguf"
    
    # Create models directory
    model_dir.mkdir(exist_ok=True)
    
    # Check if model already exists
    if model_path.exists():
        logger.info(f"✅ Model already exists at {model_path}")
        logger.info(f"📊 Model size: {model_path.stat().st_size / (1024*1024*1024):.2f} GB")
        return str(model_path)
    
    logger.info("📥 Downloading Mistral 7B Q4 model...")
    logger.info(f"🔗 URL: {model_url}")
    logger.info(f"📁 Destination: {model_path}")
    
    try:
        # Download with progress
        response = requests.get(model_url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(model_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    # Progress indicator
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r📥 Progress: {progress:.1f}% ({downloaded/(1024*1024):.1f}MB/{total_size/(1024*1024):.1f}MB)", end='', flush=True)
        
        print()  # New line after progress
        logger.info(f"✅ Model downloaded successfully!")
        logger.info(f"📊 Final size: {model_path.stat().st_size / (1024*1024*1024):.2f} GB")
        
        return str(model_path)
        
    except Exception as e:
        logger.error(f"❌ Error downloading model: {e}")
        # Clean up partial download
        if model_path.exists():
            model_path.unlink()
        return None

def download_llama_cpp():
    """Download and build llama.cpp"""
    
    llama_dir = Path("llama.cpp")
    llama_binary = llama_dir / "main"
    
    # Check if already built
    if llama_binary.exists():
        logger.info(f"✅ Llama.cpp already built at {llama_binary}")
        return str(llama_binary)
    
    logger.info("🔨 Building llama.cpp...")
    
    try:
        import subprocess
        
        # Clone if needed
        if not llama_dir.exists():
            logger.info("📥 Cloning llama.cpp repository...")
            subprocess.run([
                "git", "clone", "https://github.com/ggerganov/llama.cpp.git", str(llama_dir)
            ], check=True)
        
        # Build
        logger.info("🔨 Compiling llama.cpp...")
        subprocess.run([
            "make", "-C", str(llama_dir), "main"
        ], check=True)
        
        if llama_binary.exists():
            logger.info(f"✅ Llama.cpp built successfully at {llama_binary}")
            return str(llama_binary)
        else:
            logger.error("❌ Llama.cpp binary not found after build")
            return None
            
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Error building llama.cpp: {e}")
        return None
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return None

def setup_environment():
    """Set up environment variables"""
    
    model_path = Path("models/mistral-7b-instruct-v0.1.Q4_K_M.gguf")
    llama_binary = Path("llama.cpp/main")
    
    # Set environment variables
    if model_path.exists():
        os.environ["MODEL_PATH"] = str(model_path.absolute())
        logger.info(f"📝 Set MODEL_PATH={os.environ['MODEL_PATH']}")
    
    if llama_binary.exists():
        os.environ["LLAMA_BINARY"] = str(llama_binary.absolute())
        logger.info(f"📝 Set LLAMA_BINARY={os.environ['LLAMA_BINARY']}")
    
    # Set other defaults
    os.environ.setdefault("LLAMA_THREADS", "2")
    logger.info(f"📝 Set LLAMA_THREADS={os.environ['LLAMA_THREADS']}")

def main():
    """Main setup function"""
    logger.info("🚀 Setting up TainoAI with Llama.cpp and Mistral 7B")
    
    # Download model
    model_path = download_model()
    if not model_path:
        logger.error("❌ Failed to download model")
        return False
    
    # Build llama.cpp (skip on Heroku - will be built in Docker)
    if not os.getenv("DYNO"):  # Not on Heroku
        llama_binary = download_llama_cpp()
        if not llama_binary:
            logger.warning("⚠️ Failed to build llama.cpp locally")
    
    # Setup environment
    setup_environment()
    
    logger.info("✅ Setup complete!")
    logger.info("🎯 You can now run: python main.py")
    
    return True

if __name__ == "__main__":
    main()
