#!/usr/bin/env python3
"""
TainoAI Model Downloader - World-Class AI Models
Downloads LLaMA 2 7B Chat and Mistral 7B models for TainoAI
Organized for Docker deployment and scalability
"""

import os
import sys
import requests
import hashlib
import logging
import time
from pathlib import Path
from typing import Optional, Dict, List

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TainoAIModelDownloader:
    def __init__(self):
        self.models_config = {
            "llama-2-7b-chat": {
                "url": "https://huggingface.co/TheBloke/Llama-2-7B-Chat-GGUF/resolve/main/llama-2-7b-chat.Q4_K_M.gguf",
                "filename": "llama-2-7b-chat.Q4_K_M.gguf",
                "directory": "models/llama-2-7b-chat",
                "expected_size": 4_081_004_832,  # ~3.8GB
                "description": "LLaMA 2 7B <PERSON>t - Meta's conversational AI model"
            },
            "mistral-7b": {
                "url": "https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.1-GGUF/resolve/main/mistral-7b-instruct-v0.1.Q4_K_M.gguf",
                "filename": "mistral-7b-instruct-v0.1.Q4_K_M.gguf",
                "directory": "models/mistral-7b",
                "expected_size": 4_368_439_424,  # ~4.1GB
                "description": "Mistral 7B Instruct - High-performance instruction-following model"
            }
        }
        self.chunk_size = 1024 * 1024  # 1MB chunks for better performance

    def create_directories(self, model_name: str):
        """Create necessary directories for a specific model"""
        model_config = self.models_config[model_name]
        model_dir = Path(model_config["directory"])
        model_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"📁 Created directory: {model_dir.absolute()}")
        return model_dir

    def check_existing_model(self, model_name: str) -> bool:
        """Check if model already exists and is valid"""
        model_config = self.models_config[model_name]
        model_dir = Path(model_config["directory"])
        model_path = model_dir / model_config["filename"]

        if not model_path.exists():
            logger.info(f"❌ {model_name} not found - will download")
            return False

        file_size = model_path.stat().st_size
        size_gb = file_size / (1024**3)
        logger.info(f"📊 Existing {model_name}: {file_size:,} bytes ({size_gb:.2f} GB)")

        # Check if file size is reasonable (allow 5% variance)
        expected_size = model_config["expected_size"]
        if file_size < expected_size * 0.95:
            logger.warning(f"⚠️ {model_name} appears incomplete - will re-download")
            return False

        # Quick integrity check - read first and last 1KB
        try:
            with open(model_path, 'rb') as f:
                header = f.read(1024)
                f.seek(-1024, 2)  # Seek to 1KB from end
                footer = f.read(1024)

                if len(header) < 1024 or len(footer) < 1024:
                    logger.warning(f"⚠️ {model_name} appears corrupted - will re-download")
                    return False

        except Exception as e:
            logger.warning(f"⚠️ Cannot verify {model_name} integrity: {e} - will re-download")
            return False

        logger.info(f"✅ Valid {model_name} found!")
        return True

    def download_with_resume(self, model_name: str) -> bool:
        """Download model with resume capability and progress tracking"""
        model_config = self.models_config[model_name]
        model_dir = Path(model_config["directory"])
        model_path = model_dir / model_config["filename"]
        model_url = model_config["url"]
        expected_size = model_config["expected_size"]

        try:
            # Check if partial download exists
            resume_pos = 0
            if model_path.exists():
                resume_pos = model_path.stat().st_size
                logger.info(f"🔄 Resuming {model_name} download from {resume_pos:,} bytes")

            # Setup headers for resume
            headers = {}
            if resume_pos > 0:
                headers['Range'] = f'bytes={resume_pos}-'

            logger.info(f"🚀 Downloading {model_name} from: {model_url}")
            response = requests.get(model_url, headers=headers, stream=True)

            if response.status_code == 416:  # Range not satisfiable
                logger.info(f"📁 {model_name} already complete")
                return True
            elif response.status_code not in [200, 206]:
                response.raise_for_status()

            # Get total size
            if 'content-range' in response.headers:
                total_size = int(response.headers['content-range'].split('/')[-1])
            else:
                total_size = int(response.headers.get('content-length', 0)) + resume_pos

            logger.info(f"📦 {model_name} total size: {total_size:,} bytes ({total_size / (1024**3):.2f} GB)")

            # Download with progress
            downloaded = resume_pos
            last_update = time.time()

            mode = 'ab' if resume_pos > 0 else 'wb'
            with open(model_path, mode) as f:
                for chunk in response.iter_content(chunk_size=self.chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)

                        # Update progress every 2 seconds
                        now = time.time()
                        if now - last_update >= 2.0:
                            progress = (downloaded / total_size) * 100 if total_size > 0 else 0
                            speed = len(chunk) / (now - last_update) / (1024**2)  # MB/s
                            logger.info(f"📥 {model_name}: {progress:.1f}% ({downloaded/(1024**3):.2f}/{total_size/(1024**3):.2f} GB) - {speed:.1f} MB/s")
                            last_update = now

            final_size = model_path.stat().st_size
            logger.info(f"✅ {model_name} download complete: {final_size:,} bytes ({final_size / (1024**3):.2f} GB)")

            return final_size >= expected_size * 0.95

        except Exception as e:
            logger.error(f"❌ {model_name} download failed: {e}")
            return False

    def download_all_models(self, force: bool = False, models: List[str] = None) -> bool:
        """Download all TainoAI models"""
        logger.info("🤖 TainoAI World-Class Model Downloader")
        logger.info("=" * 70)

        if models is None:
            models = list(self.models_config.keys())

        success_count = 0
        total_models = len(models)

        for model_name in models:
            logger.info(f"\n🔄 Processing {model_name}...")
            logger.info(f"📝 {self.models_config[model_name]['description']}")

            # Create directories
            self.create_directories(model_name)

            # Check existing model
            if not force and self.check_existing_model(model_name):
                logger.info(f"✅ {model_name} already available and valid")
                success_count += 1
                continue

            # Remove existing if force download
            if force:
                model_config = self.models_config[model_name]
                model_dir = Path(model_config["directory"])
                model_path = model_dir / model_config["filename"]
                if model_path.exists():
                    logger.info(f"🗑️ Removing existing {model_name} (force download)")
                    model_path.unlink()

            # Download model
            logger.info(f"📥 Downloading {model_name}...")
            if not self.download_with_resume(model_name):
                logger.error(f"❌ {model_name} download failed")
                continue

            # Verify model
            logger.info(f"🔍 Verifying {model_name}...")
            if not self.verify_model(model_name):
                logger.error(f"❌ {model_name} verification failed")
                continue

            logger.info(f"✅ {model_name} download and verification complete!")
            success_count += 1

        logger.info(f"\n🎉 TainoAI Model Download Summary:")
        logger.info(f"✅ Successfully downloaded: {success_count}/{total_models} models")

        if success_count == total_models:
            logger.info("🚀 All models ready for TainoAI deployment!")
            return True
        else:
            logger.warning(f"⚠️ {total_models - success_count} models failed to download")
            return False

    def verify_model(self, model_name: str) -> bool:
        """Verify model integrity"""
        model_config = self.models_config[model_name]
        model_dir = Path(model_config["directory"])
        model_path = model_dir / model_config["filename"]
        expected_size = model_config["expected_size"]

        if not model_path.exists():
            return False

        file_size = model_path.stat().st_size
        logger.info(f"🔍 Verifying {model_name}: {file_size:,} bytes")

        # Basic size check
        if file_size < expected_size * 0.95:
            logger.error(f"❌ {model_name} file too small")
            return False

        # Check if file is readable
        try:
            with open(model_path, 'rb') as f:
                header = f.read(1024)
                if len(header) < 1024:
                    logger.error(f"❌ {model_name} file appears corrupted")
                    return False
        except Exception as e:
            logger.error(f"❌ Cannot read {model_name} file: {e}")
            return False

        logger.info(f"✅ {model_name} verification passed")
        return True

def setup_environment():
    """Set up environment variables for TainoAI"""
    # Check for available models and set primary model
    llama_path = Path("models/llama-2-7b-chat/llama-2-7b-chat.Q4_K_M.gguf")
    mistral_path = Path("models/mistral-7b/mistral-7b-instruct-v0.1.Q4_K_M.gguf")

    # Set primary model (prefer Mistral, fallback to LLaMA)
    if mistral_path.exists():
        os.environ["PRIMARY_MODEL_PATH"] = str(mistral_path.absolute())
        os.environ["PRIMARY_MODEL_TYPE"] = "mistral-7b"
        logger.info(f"📝 Primary model: Mistral 7B")
    elif llama_path.exists():
        os.environ["PRIMARY_MODEL_PATH"] = str(llama_path.absolute())
        os.environ["PRIMARY_MODEL_TYPE"] = "llama-2-7b-chat"
        logger.info(f"📝 Primary model: LLaMA 2 7B Chat")

    # Set secondary model
    if llama_path.exists() and mistral_path.exists():
        if os.environ.get("PRIMARY_MODEL_TYPE") == "mistral-7b":
            os.environ["SECONDARY_MODEL_PATH"] = str(llama_path.absolute())
            os.environ["SECONDARY_MODEL_TYPE"] = "llama-2-7b-chat"
        else:
            os.environ["SECONDARY_MODEL_PATH"] = str(mistral_path.absolute())
            os.environ["SECONDARY_MODEL_TYPE"] = "mistral-7b"

    # Set TainoAI configuration
    os.environ["TAINOAI_MODE"] = "WORLD_CLASS_AI"
    os.environ["TAINOAI_VERSION"] = "9.0.0"

    # Set optimized defaults
    os.environ.setdefault("LLAMA_THREADS", "4")
    os.environ.setdefault("LLAMA_CONTEXT_SIZE", "4096")
    os.environ.setdefault("LLAMA_MAX_TOKENS", "1024")

def main():
    """Main setup function"""
    logger.info("🚀 TainoAI World-Class AI Setup")
    logger.info("=" * 70)

    # Parse command line arguments
    force = "--force" in sys.argv
    models_to_download = []

    if "--llama-only" in sys.argv:
        models_to_download = ["llama-2-7b-chat"]
    elif "--mistral-only" in sys.argv:
        models_to_download = ["mistral-7b"]
    else:
        models_to_download = None  # Download all

    if force:
        logger.info("🔄 Force download enabled")

    # Download models
    downloader = TainoAIModelDownloader()
    success = downloader.download_all_models(force=force, models=models_to_download)

    if not success:
        logger.error("❌ Failed to download all models")
        return False

    # Setup environment
    setup_environment()

    logger.info("✅ TainoAI World-Class AI Setup Complete!")
    logger.info("🎯 Models ready for Docker deployment")
    logger.info("🚀 Run: docker build -f Dockerfile.tainoai -t tainoai .")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
