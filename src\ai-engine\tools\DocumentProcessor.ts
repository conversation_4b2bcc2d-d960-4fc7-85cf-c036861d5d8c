/**
 * TainoAI Document Processor - Local Document Analysis
 */

import { AITool, DocumentChunk } from '../types';
import fs from 'fs';
import path from 'path';

export class DocumentProcessor implements AITool {
  name = 'document_processor';
  description = 'Process, analyze, and extract information from documents';
  parameters = {
    action: { 
      type: 'string', 
      description: 'Action to perform: analyze, summarize, extract, search',
      enum: ['analyze', 'summarize', 'extract', 'search']
    },
    content: { type: 'string', description: 'Document content or file path' },
    query: { type: 'string', description: 'Search query for document search' },
    format: { type: 'string', description: 'Document format: text, pdf, docx, etc.' },
  };

  async execute(params: {
    action: 'analyze' | 'summarize' | 'extract' | 'search';
    content?: string;
    query?: string;
    format?: string;
  }): Promise<any> {
    const { action, content, query, format } = params;

    console.log(`📄 Document processor: ${action}`);

    try {
      switch (action) {
        case 'analyze':
          return await this.analyzeDocument(content || '', format);
        case 'summarize':
          return await this.summarizeDocument(content || '');
        case 'extract':
          return await this.extractKeyInformation(content || '');
        case 'search':
          return await this.searchInDocument(content || '', query || '');
        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error) {
      console.error('❌ Document processing failed:', error);
      throw error;
    }
  }

  private async analyzeDocument(content: string, format?: string): Promise<any> {
    const analysis = {
      wordCount: this.countWords(content),
      characterCount: content.length,
      paragraphCount: this.countParagraphs(content),
      sentenceCount: this.countSentences(content),
      readingTime: this.estimateReadingTime(content),
      complexity: this.analyzeComplexity(content),
      topics: this.extractTopics(content),
      sentiment: this.analyzeSentiment(content),
      keyPhrases: this.extractKeyPhrases(content),
      structure: this.analyzeStructure(content),
      format: format || 'text',
    };

    return {
      success: true,
      analysis,
      summary: this.generateAnalysisSummary(analysis),
    };
  }

  private async summarizeDocument(content: string): Promise<any> {
    const sentences = this.extractSentences(content);
    const keyPoints = this.extractKeyPoints(content);
    const summary = this.generateSummary(sentences, keyPoints);

    return {
      success: true,
      summary: {
        brief: summary.brief,
        detailed: summary.detailed,
        keyPoints: keyPoints,
        originalLength: content.length,
        summaryLength: summary.brief.length,
        compressionRatio: (summary.brief.length / content.length * 100).toFixed(1) + '%',
      },
    };
  }

  private async extractKeyInformation(content: string): Promise<any> {
    const extraction = {
      entities: this.extractEntities(content),
      dates: this.extractDates(content),
      numbers: this.extractNumbers(content),
      urls: this.extractUrls(content),
      emails: this.extractEmails(content),
      phoneNumbers: this.extractPhoneNumbers(content),
      keywords: this.extractKeywords(content),
      concepts: this.extractConcepts(content),
    };

    return {
      success: true,
      extraction,
      metadata: {
        extractionDate: new Date().toISOString(),
        contentLength: content.length,
        extractedItems: Object.values(extraction).flat().length,
      },
    };
  }

  private async searchInDocument(content: string, query: string): Promise<any> {
    const results = this.performDocumentSearch(content, query);
    
    return {
      success: true,
      query,
      results: results.matches,
      totalMatches: results.totalMatches,
      searchTime: results.searchTime,
      relevanceScores: results.relevanceScores,
    };
  }

  // Analysis helper methods
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  private countParagraphs(text: string): number {
    return text.split(/\n\s*\n/).filter(para => para.trim().length > 0).length;
  }

  private countSentences(text: string): number {
    return text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0).length;
  }

  private estimateReadingTime(text: string): string {
    const wordsPerMinute = 200;
    const words = this.countWords(text);
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }

  private analyzeComplexity(text: string): any {
    const words = text.split(/\s+/);
    const sentences = text.split(/[.!?]+/);
    
    const avgWordsPerSentence = words.length / sentences.length;
    const avgCharsPerWord = text.replace(/\s/g, '').length / words.length;
    
    let complexity = 'Simple';
    if (avgWordsPerSentence > 20 || avgCharsPerWord > 6) {
      complexity = 'Complex';
    } else if (avgWordsPerSentence > 15 || avgCharsPerWord > 5) {
      complexity = 'Moderate';
    }

    return {
      level: complexity,
      avgWordsPerSentence: Math.round(avgWordsPerSentence),
      avgCharsPerWord: Math.round(avgCharsPerWord),
    };
  }

  private extractTopics(text: string): string[] {
    // Simple topic extraction based on word frequency
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3);

    const frequency: Record<string, number> = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });

    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  private analyzeSentiment(text: string): any {
    // Simple sentiment analysis
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'positive', 'success', 'happy', 'love'];
    const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'negative', 'failure', 'sad', 'hate', 'wrong', 'problem'];

    const words = text.toLowerCase().split(/\s+/);
    const positiveCount = words.filter(word => positiveWords.includes(word)).length;
    const negativeCount = words.filter(word => negativeWords.includes(word)).length;

    let sentiment = 'neutral';
    let score = 0;

    if (positiveCount > negativeCount) {
      sentiment = 'positive';
      score = (positiveCount - negativeCount) / words.length;
    } else if (negativeCount > positiveCount) {
      sentiment = 'negative';
      score = (negativeCount - positiveCount) / words.length;
    }

    return {
      sentiment,
      score: Math.round(score * 1000) / 1000,
      positiveWords: positiveCount,
      negativeWords: negativeCount,
    };
  }

  private extractKeyPhrases(text: string): string[] {
    // Extract noun phrases and important terms
    const sentences = text.split(/[.!?]+/);
    const phrases: string[] = [];

    sentences.forEach(sentence => {
      // Look for capitalized words (potential proper nouns)
      const capitalizedWords = sentence.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || [];
      phrases.push(...capitalizedWords);

      // Look for quoted text
      const quotedText = sentence.match(/"([^"]+)"/g) || [];
      phrases.push(...quotedText.map(q => q.replace(/"/g, '')));
    });

    return [...new Set(phrases)].slice(0, 15);
  }

  private analyzeStructure(text: string): any {
    const lines = text.split('\n');
    const structure = {
      hasHeadings: /^#+\s/.test(text) || /^[A-Z][^.!?]*$/.test(lines[0]),
      hasBulletPoints: /^\s*[-*•]\s/.test(text),
      hasNumberedLists: /^\s*\d+\.\s/.test(text),
      hasCodeBlocks: /```/.test(text) || /`[^`]+`/.test(text),
      hasLinks: /https?:\/\//.test(text),
      hasEmails: /@\w+\.\w+/.test(text),
      paragraphCount: this.countParagraphs(text),
      lineCount: lines.length,
    };

    return structure;
  }

  private extractSentences(text: string): string[] {
    return text.split(/[.!?]+/)
      .map(s => s.trim())
      .filter(s => s.length > 10);
  }

  private extractKeyPoints(text: string): string[] {
    const sentences = this.extractSentences(text);
    
    // Score sentences based on various factors
    const scoredSentences = sentences.map(sentence => {
      let score = 0;
      
      // Longer sentences might be more important
      score += Math.min(sentence.length / 100, 1);
      
      // Sentences with numbers or specific terms
      if (/\d+/.test(sentence)) score += 0.5;
      if (/important|key|main|primary|significant/.test(sentence.toLowerCase())) score += 1;
      if (/first|second|third|finally|conclusion/.test(sentence.toLowerCase())) score += 0.5;
      
      return { sentence, score };
    });

    return scoredSentences
      .sort((a, b) => b.score - a.score)
      .slice(0, 5)
      .map(item => item.sentence);
  }

  private generateSummary(sentences: string[], keyPoints: string[]): any {
    const brief = keyPoints.slice(0, 2).join(' ') || sentences.slice(0, 2).join(' ');
    const detailed = keyPoints.join(' ') || sentences.slice(0, 5).join(' ');

    return {
      brief: brief.substring(0, 200) + (brief.length > 200 ? '...' : ''),
      detailed: detailed.substring(0, 500) + (detailed.length > 500 ? '...' : ''),
    };
  }

  private generateAnalysisSummary(analysis: any): string {
    return `Document contains ${analysis.wordCount} words in ${analysis.paragraphCount} paragraphs. ` +
           `Reading time: ${analysis.readingTime}. Complexity: ${analysis.complexity.level}. ` +
           `Sentiment: ${analysis.sentiment.sentiment}. ` +
           `Main topics: ${analysis.topics.slice(0, 3).join(', ')}.`;
  }

  // Extraction helper methods
  private extractEntities(text: string): string[] {
    // Extract potential named entities (capitalized words/phrases)
    const entities = text.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || [];
    return [...new Set(entities)].slice(0, 20);
  }

  private extractDates(text: string): string[] {
    const datePatterns = [
      /\b\d{1,2}\/\d{1,2}\/\d{4}\b/g,
      /\b\d{4}-\d{2}-\d{2}\b/g,
      /\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2},?\s+\d{4}\b/gi,
    ];

    const dates: string[] = [];
    datePatterns.forEach(pattern => {
      const matches = text.match(pattern) || [];
      dates.push(...matches);
    });

    return [...new Set(dates)];
  }

  private extractNumbers(text: string): string[] {
    const numbers = text.match(/\b\d+(?:\.\d+)?(?:%|\$|€|£)?\b/g) || [];
    return [...new Set(numbers)].slice(0, 20);
  }

  private extractUrls(text: string): string[] {
    const urls = text.match(/https?:\/\/[^\s]+/g) || [];
    return [...new Set(urls)];
  }

  private extractEmails(text: string): string[] {
    const emails = text.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g) || [];
    return [...new Set(emails)];
  }

  private extractPhoneNumbers(text: string): string[] {
    const phones = text.match(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g) || [];
    return [...new Set(phones)];
  }

  private extractKeywords(text: string): string[] {
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 4);

    const frequency: Record<string, number> = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });

    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 15)
      .map(([word]) => word);
  }

  private extractConcepts(text: string): string[] {
    // Extract potential concepts (noun phrases, technical terms)
    const concepts = text.match(/\b[a-z]+(?:\s+[a-z]+){1,2}\b/gi) || [];
    return [...new Set(concepts)]
      .filter(concept => concept.length > 5)
      .slice(0, 10);
  }

  private performDocumentSearch(content: string, query: string): any {
    const startTime = Date.now();
    const queryWords = query.toLowerCase().split(/\s+/);
    const sentences = content.split(/[.!?]+/);
    
    const matches: any[] = [];
    const relevanceScores: number[] = [];

    sentences.forEach((sentence, index) => {
      const sentenceLower = sentence.toLowerCase();
      let relevanceScore = 0;
      let matchCount = 0;

      queryWords.forEach(word => {
        if (sentenceLower.includes(word)) {
          matchCount++;
          relevanceScore += word.length / sentence.length;
        }
      });

      if (matchCount > 0) {
        matches.push({
          text: sentence.trim(),
          index,
          matchCount,
          relevanceScore,
        });
        relevanceScores.push(relevanceScore);
      }
    });

    const searchTime = Date.now() - startTime;

    return {
      matches: matches
        .sort((a, b) => b.relevanceScore - a.relevanceScore)
        .slice(0, 10),
      totalMatches: matches.length,
      searchTime,
      relevanceScores,
    };
  }
}

export default DocumentProcessor;
