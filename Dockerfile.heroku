# TainoAI - Heroku Optimized Deployment
# Lightweight build with runtime model download

# Stage 1: Build llama.cpp
FROM ubuntu:22.04 AS llama-builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Clone and build llama.cpp
WORKDIR /build
RUN git clone https://github.com/ggerganov/llama.cpp.git
WORKDIR /build/llama.cpp

# Build with optimizations
RUN make -j$(nproc) main server quantize \
    LLAMA_OPENBLAS=1 \
    LLAMA_NATIVE=1 \
    LLAMA_FAST=1

# Stage 2: Build React Frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./
COPY next.config.js ./
COPY tailwind.config.js ./
COPY postcss.config.js ./

# Install dependencies
RUN npm ci --only=production --no-audit --no-fund

# Copy source code
COPY app/ ./app/
COPY components/ ./components/
COPY lib/ ./lib/

# Build and export static frontend
RUN npm run build && npm run export

# Stage 3: Production Runtime - Heroku Optimized
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy requirements and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy llama.cpp binaries from builder
COPY --from=llama-builder /build/llama.cpp/main ./llama.cpp/main
COPY --from=llama-builder /build/llama.cpp/server ./llama.cpp/server
COPY --from=llama-builder /build/llama.cpp/quantize ./llama.cpp/quantize

# Copy built frontend
COPY --from=frontend-builder /app/out/ ./static/

# Copy backend code
COPY main.py ./
COPY llama_engine.py ./
COPY document_processor.py ./
COPY download_model.py ./

# Create directory structure
RUN mkdir -p models/llama-2-7b-chat models/mistral-7b data/conversations data/documents data/uploads logs && \
    chmod +x ./llama.cpp/*

# Set environment variables for Heroku
ENV PRIMARY_MODEL_PATH=./models/mistral-7b/mistral-7b-instruct-v0.1.Q4_K_M.gguf
ENV SECONDARY_MODEL_PATH=./models/llama-2-7b-chat/llama-2-7b-chat.Q4_K_M.gguf
ENV PRIMARY_MODEL_TYPE=mistral-7b
ENV SECONDARY_MODEL_TYPE=llama-2-7b-chat
ENV LLAMA_BINARY=./llama.cpp/main
ENV LLAMA_SERVER_BINARY=./llama.cpp/server
ENV LLAMA_THREADS=2
ENV LLAMA_CONTEXT_SIZE=2048
ENV LLAMA_MAX_TOKENS=512
ENV PORT=8000
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV TAINOAI_MODE=HEROKU_OPTIMIZED
ENV TAINOAI_VERSION=9.0.0

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=60s --timeout=30s --start-period=120s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Create startup script
RUN echo '#!/bin/bash\n\
echo "🚀 Starting TainoAI Heroku Deployment..."\n\
echo "📥 Downloading models in background..."\n\
python download_model.py --mistral-only &\n\
echo "🌐 Starting web server..."\n\
python main.py' > start.sh && chmod +x start.sh

# Run the application
CMD ["./start.sh"]
