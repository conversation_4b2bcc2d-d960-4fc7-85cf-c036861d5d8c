# TainoAI - Ultra-Lightweight Heroku Deployment
# Starts immediately with enhanced fallback responses

# Stage 1: Build React Frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./
COPY next.config.js ./
COPY tailwind.config.js ./
COPY postcss.config.js ./

# Install dependencies
RUN npm ci --only=production --no-audit --no-fund

# Copy source code
COPY app/ ./app/
COPY components/ ./components/
COPY lib/ ./lib/

# Build and export static frontend
RUN npm run build && npm run export

# Stage 2: Production Runtime - Ultra-Lightweight
FROM python:3.11-slim

WORKDIR /app

# Install minimal system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy requirements and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy built frontend
COPY --from=frontend-builder /app/out/ ./static/

# Copy backend code
COPY main.py ./
COPY llama_engine.py ./
COPY document_processor.py ./

# Create directory structure
RUN mkdir -p data/conversations data/documents data/uploads logs

# Set environment variables for ultra-lightweight mode
ENV TAINOAI_MODE=HEROKU_LITE
ENV TAINOAI_VERSION=9.0.0
ENV PORT=8000
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the application directly
CMD ["python", "main.py"]
