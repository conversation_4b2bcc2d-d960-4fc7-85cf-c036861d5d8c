{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "f0c966e813c748f987761f300a25ec6e", "previewModeSigningKey": "a67dfbf24eb2cb6b24184755e2b02b9613b2b1e476ce0641f201959ecbf3e154", "previewModeEncryptionKey": "809ed816568a867b37ecdc5ab78d9ca7d3a4b82b18b82d13bcd1f8e53433e3c7"}}