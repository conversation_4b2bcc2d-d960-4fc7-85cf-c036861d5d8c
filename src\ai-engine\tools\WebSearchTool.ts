/**
 * TainoAI Web Search Tool - No External APIs
 * Uses web scraping for search capabilities
 */

import { AITool, WebSearchResult } from '../types';
import puppeteer from 'puppeteer';
import * as cheerio from 'cheerio';
import fetch from 'node-fetch';

export class WebSearchTool implements AITool {
  name = 'web_search';
  description = 'Search the web for current information and return relevant results';
  parameters = {
    query: { type: 'string', description: 'Search query' },
    maxResults: { type: 'number', description: 'Maximum number of results (default: 5)' },
    source: { type: 'string', description: 'Search source: duckduckgo, bing, or direct (default: duckduckgo)' },
  };

  async execute(params: { 
    query: string; 
    maxResults?: number; 
    source?: 'duckduckgo' | 'bing' | 'direct' 
  }): Promise<WebSearchResult[]> {
    const { query, maxResults = 5, source = 'duckduckgo' } = params;

    console.log(`🔍 Searching web for: "${query}" using ${source}`);

    try {
      switch (source) {
        case 'duckduckgo':
          return await this.searchDuckDuckGo(query, maxResults);
        case 'bing':
          return await this.searchBing(query, maxResults);
        case 'direct':
          return await this.directWebScrape(query, maxResults);
        default:
          return await this.searchDuckDuckGo(query, maxResults);
      }
    } catch (error) {
      console.error('❌ Web search failed:', error);
      return [];
    }
  }

  private async searchDuckDuckGo(query: string, maxResults: number): Promise<WebSearchResult[]> {
    try {
      // Use DuckDuckGo's instant answer API (no API key required)
      const searchUrl = `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_html=1&skip_disambig=1`;
      
      const response = await fetch(searchUrl);
      const data = await response.json() as any;

      const results: WebSearchResult[] = [];

      // Process instant answer
      if (data.AbstractText) {
        results.push({
          title: data.Heading || 'DuckDuckGo Instant Answer',
          url: data.AbstractURL || 'https://duckduckgo.com',
          snippet: data.AbstractText,
          relevance: 0.9,
        });
      }

      // Process related topics
      if (data.RelatedTopics && Array.isArray(data.RelatedTopics)) {
        for (const topic of data.RelatedTopics.slice(0, maxResults - results.length)) {
          if (topic.Text && topic.FirstURL) {
            results.push({
              title: topic.Text.split(' - ')[0] || 'Related Topic',
              url: topic.FirstURL,
              snippet: topic.Text,
              relevance: 0.7,
            });
          }
        }
      }

      // If we don't have enough results, try web scraping
      if (results.length < maxResults) {
        const scrapedResults = await this.scrapeDuckDuckGoResults(query, maxResults - results.length);
        results.push(...scrapedResults);
      }

      return results.slice(0, maxResults);

    } catch (error) {
      console.error('DuckDuckGo search failed:', error);
      return [];
    }
  }

  private async scrapeDuckDuckGoResults(query: string, maxResults: number): Promise<WebSearchResult[]> {
    let browser;
    try {
      browser = await puppeteer.launch({ 
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      
      const page = await browser.newPage();
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      
      const searchUrl = `https://duckduckgo.com/?q=${encodeURIComponent(query)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      // Wait for results to load
      await page.waitForSelector('[data-result]', { timeout: 10000 });

      const results = await page.evaluate(() => {
        const resultElements = document.querySelectorAll('[data-result]');
        const searchResults: any[] = [];

        resultElements.forEach((element) => {
          const titleElement = element.querySelector('h2 a');
          const snippetElement = element.querySelector('[data-result="snippet"]');
          
          if (titleElement && snippetElement) {
            searchResults.push({
              title: titleElement.textContent?.trim() || '',
              url: titleElement.getAttribute('href') || '',
              snippet: snippetElement.textContent?.trim() || '',
              relevance: 0.8,
            });
          }
        });

        return searchResults;
      });

      return results.slice(0, maxResults);

    } catch (error) {
      console.error('DuckDuckGo scraping failed:', error);
      return [];
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  private async searchBing(query: string, maxResults: number): Promise<WebSearchResult[]> {
    let browser;
    try {
      browser = await puppeteer.launch({ 
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      
      const page = await browser.newPage();
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      
      const searchUrl = `https://www.bing.com/search?q=${encodeURIComponent(query)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      const results = await page.evaluate(() => {
        const resultElements = document.querySelectorAll('.b_algo');
        const searchResults: any[] = [];

        resultElements.forEach((element) => {
          const titleElement = element.querySelector('h2 a');
          const snippetElement = element.querySelector('.b_caption p');
          
          if (titleElement && snippetElement) {
            searchResults.push({
              title: titleElement.textContent?.trim() || '',
              url: titleElement.getAttribute('href') || '',
              snippet: snippetElement.textContent?.trim() || '',
              relevance: 0.8,
            });
          }
        });

        return searchResults;
      });

      return results.slice(0, maxResults);

    } catch (error) {
      console.error('Bing search failed:', error);
      return [];
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  private async directWebScrape(query: string, maxResults: number): Promise<WebSearchResult[]> {
    // For direct scraping, we'll try to find relevant websites based on the query
    const websites = this.getRelevantWebsites(query);
    const results: WebSearchResult[] = [];

    for (const website of websites.slice(0, maxResults)) {
      try {
        const content = await this.scrapeWebsite(website.url);
        if (content && this.isRelevantContent(content, query)) {
          results.push({
            title: website.title,
            url: website.url,
            snippet: this.extractRelevantSnippet(content, query),
            relevance: website.relevance,
          });
        }
      } catch (error) {
        console.error(`Failed to scrape ${website.url}:`, error);
      }
    }

    return results;
  }

  private getRelevantWebsites(query: string): Array<{ title: string; url: string; relevance: number }> {
    const queryLower = query.toLowerCase();
    const websites = [];

    // Technology-related queries
    if (queryLower.includes('programming') || queryLower.includes('code') || queryLower.includes('development')) {
      websites.push(
        { title: 'Stack Overflow', url: 'https://stackoverflow.com', relevance: 0.9 },
        { title: 'GitHub', url: 'https://github.com', relevance: 0.8 },
        { title: 'MDN Web Docs', url: 'https://developer.mozilla.org', relevance: 0.8 }
      );
    }

    // AI/ML related queries
    if (queryLower.includes('ai') || queryLower.includes('machine learning') || queryLower.includes('neural')) {
      websites.push(
        { title: 'Hugging Face', url: 'https://huggingface.co', relevance: 0.9 },
        { title: 'Papers with Code', url: 'https://paperswithcode.com', relevance: 0.8 }
      );
    }

    // News and current events
    if (queryLower.includes('news') || queryLower.includes('current') || queryLower.includes('latest')) {
      websites.push(
        { title: 'Reuters', url: 'https://reuters.com', relevance: 0.8 },
        { title: 'BBC News', url: 'https://bbc.com/news', relevance: 0.8 }
      );
    }

    // General knowledge
    websites.push(
      { title: 'Wikipedia', url: `https://en.wikipedia.org/wiki/${encodeURIComponent(query)}`, relevance: 0.7 }
    );

    return websites;
  }

  private async scrapeWebsite(url: string): Promise<string> {
    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const html = await response.text();
      const $ = cheerio.load(html);

      // Remove script and style elements
      $('script, style, nav, footer, header').remove();

      // Extract main content
      const content = $('main, article, .content, #content, .post, .entry').first().text() ||
                     $('body').text();

      return content.replace(/\s+/g, ' ').trim();

    } catch (error) {
      throw new Error(`Failed to scrape ${url}: ${error}`);
    }
  }

  private isRelevantContent(content: string, query: string): boolean {
    const queryWords = query.toLowerCase().split(' ');
    const contentLower = content.toLowerCase();

    // Check if at least 50% of query words appear in content
    const matchingWords = queryWords.filter(word => 
      word.length > 2 && contentLower.includes(word)
    );

    return matchingWords.length >= Math.ceil(queryWords.length * 0.5);
  }

  private extractRelevantSnippet(content: string, query: string): string {
    const queryWords = query.toLowerCase().split(' ');
    const sentences = content.split(/[.!?]+/);

    // Find sentences that contain query words
    const relevantSentences = sentences.filter(sentence => {
      const sentenceLower = sentence.toLowerCase();
      return queryWords.some(word => word.length > 2 && sentenceLower.includes(word));
    });

    if (relevantSentences.length > 0) {
      return relevantSentences[0].trim().substring(0, 200) + '...';
    }

    // Fallback to first 200 characters
    return content.substring(0, 200) + '...';
  }
}

export default WebSearchTool;
