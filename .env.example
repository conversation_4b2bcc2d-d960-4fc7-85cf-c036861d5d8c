# TainoAI Rebirth v6.0 GENESIS - Environment Variables

# Backend Configuration
PORT=8000
MODEL_PATH=models/tainoai_v6
LEARNING_RATE=1e-5
MAX_SEQUENCE_LENGTH=512
BATCH_SIZE=4

# External Model Configuration
HUGGINGFACE_MODEL=microsoft/DialoGPT-medium
MODEL_WEIGHTS_URL=https://your-storage.com/model_weights.pt

# Database
DATABASE_URL=sqlite:///data/tainoai.db

# Search Configuration
SEARCH_TIMEOUT=10
MAX_SEARCH_RESULTS=5

# Development
DEBUG=true
LOG_LEVEL=info

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=TainoAI Rebirth
NEXT_PUBLIC_VERSION=6.0.0-genesis

# Production (Heroku/Vercel)
# PORT=8000
# NEXT_PUBLIC_API_URL=https://your-backend.herokuapp.com
