"""
TainoAI Document Processor
Handles PDF, Word, Excel, and other document uploads for learning
"""

import os
import json
import logging
from typing import List, Dict, Optional
from datetime import datetime
import hashlib

logger = logging.getLogger(__name__)

class DocumentProcessor:
    def __init__(self):
        self.knowledge_base_path = "data/knowledge_base.json"
        self.documents_path = "data/documents"
        self.supported_formats = {
            '.pdf': self._process_pdf,
            '.docx': self._process_docx,
            '.doc': self._process_docx,
            '.xlsx': self._process_excel,
            '.xls': self._process_excel,
            '.txt': self._process_text,
            '.md': self._process_text,
            '.csv': self._process_csv
        }
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Create necessary directories"""
        os.makedirs("data", exist_ok=True)
        os.makedirs(self.documents_path, exist_ok=True)
    
    async def process_document(self, file_content: bytes, filename: str, user_id: str) -> Dict:
        """Process uploaded document and extract knowledge"""
        try:
            # Get file extension
            file_ext = os.path.splitext(filename.lower())[1]
            
            if file_ext not in self.supported_formats:
                return {
                    "success": False,
                    "error": f"Unsupported file format: {file_ext}",
                    "supported_formats": list(self.supported_formats.keys())
                }
            
            # Generate unique file ID
            file_hash = hashlib.md5(file_content).hexdigest()
            file_id = f"{file_hash}_{filename}"
            
            # Save original file
            file_path = os.path.join(self.documents_path, file_id)
            with open(file_path, 'wb') as f:
                f.write(file_content)
            
            # Extract text content
            processor = self.supported_formats[file_ext]
            extracted_text = await processor(file_path)
            
            if not extracted_text:
                return {
                    "success": False,
                    "error": "Could not extract text from document"
                }
            
            # Create knowledge entry
            knowledge_entry = {
                "id": file_id,
                "filename": filename,
                "user_id": user_id,
                "file_type": file_ext,
                "upload_date": datetime.now().isoformat(),
                "content": extracted_text,
                "word_count": len(extracted_text.split()),
                "file_size": len(file_content),
                "summary": self._generate_summary(extracted_text)
            }
            
            # Add to knowledge base
            await self._add_to_knowledge_base(knowledge_entry)
            
            logger.info(f"✅ Processed document: {filename} ({len(extracted_text)} chars)")
            
            return {
                "success": True,
                "file_id": file_id,
                "filename": filename,
                "word_count": knowledge_entry["word_count"],
                "summary": knowledge_entry["summary"],
                "message": f"Successfully processed {filename}! TainoAI can now use this information to answer your questions."
            }
            
        except Exception as e:
            logger.error(f"❌ Error processing document {filename}: {e}")
            return {
                "success": False,
                "error": f"Error processing document: {str(e)}"
            }
    
    async def _process_pdf(self, file_path: str) -> str:
        """Extract text from PDF"""
        try:
            import PyPDF2
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            return text.strip()
        except Exception as e:
            logger.error(f"PDF processing error: {e}")
            return ""
    
    async def _process_docx(self, file_path: str) -> str:
        """Extract text from Word document"""
        try:
            from docx import Document
            doc = Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text.strip()
        except Exception as e:
            logger.error(f"DOCX processing error: {e}")
            return ""
    
    async def _process_excel(self, file_path: str) -> str:
        """Extract text from Excel file - lightweight version"""
        try:
            from openpyxl import load_workbook
            workbook = load_workbook(file_path, data_only=True)
            text = ""

            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                text += f"Sheet: {sheet_name}\n"

                for row in sheet.iter_rows(values_only=True):
                    row_text = " | ".join([str(cell) if cell is not None else "" for cell in row])
                    if row_text.strip():
                        text += row_text + "\n"
                text += "\n"

            return text.strip()
        except Exception as e:
            logger.error(f"Excel processing error: {e}")
            return ""
    
    async def _process_text(self, file_path: str) -> str:
        """Extract text from text file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        except Exception as e:
            logger.error(f"Text processing error: {e}")
            return ""
    
    async def _process_csv(self, file_path: str) -> str:
        """Extract text from CSV file - lightweight version"""
        try:
            import csv
            text = ""
            with open(file_path, 'r', encoding='utf-8') as file:
                csv_reader = csv.reader(file)
                for row in csv_reader:
                    text += " | ".join(row) + "\n"
            return text.strip()
        except Exception as e:
            logger.error(f"CSV processing error: {e}")
            return ""
    
    def _generate_summary(self, text: str) -> str:
        """Generate a simple summary of the document"""
        words = text.split()
        if len(words) < 50:
            return text[:200] + "..." if len(text) > 200 else text
        
        # Simple extractive summary - first and key sentences
        sentences = text.split('.')
        summary_sentences = []
        
        # Add first sentence
        if sentences:
            summary_sentences.append(sentences[0])
        
        # Add sentences with key terms
        key_terms = ['important', 'key', 'main', 'primary', 'conclusion', 'summary', 'result']
        for sentence in sentences[1:6]:  # Check first few sentences
            if any(term in sentence.lower() for term in key_terms):
                summary_sentences.append(sentence)
                break
        
        summary = '. '.join(summary_sentences).strip()
        return summary[:300] + "..." if len(summary) > 300 else summary
    
    async def _add_to_knowledge_base(self, knowledge_entry: Dict):
        """Add knowledge entry to the knowledge base"""
        try:
            # Load existing knowledge base
            knowledge_base = []
            if os.path.exists(self.knowledge_base_path):
                with open(self.knowledge_base_path, 'r', encoding='utf-8') as f:
                    knowledge_base = json.load(f)
            
            # Add new entry
            knowledge_base.append(knowledge_entry)
            
            # Save updated knowledge base
            with open(self.knowledge_base_path, 'w', encoding='utf-8') as f:
                json.dump(knowledge_base, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"Error updating knowledge base: {e}")
    
    async def search_knowledge_base(self, query: str, user_id: str = None) -> List[Dict]:
        """Search the knowledge base for relevant information"""
        try:
            if not os.path.exists(self.knowledge_base_path):
                return []
            
            with open(self.knowledge_base_path, 'r', encoding='utf-8') as f:
                knowledge_base = json.load(f)
            
            # Simple text search
            query_lower = query.lower()
            relevant_docs = []
            
            for doc in knowledge_base:
                # Filter by user if specified
                if user_id and doc.get('user_id') != user_id:
                    continue
                
                # Check if query terms appear in content
                content_lower = doc['content'].lower()
                if any(term in content_lower for term in query_lower.split()):
                    # Calculate relevance score
                    score = sum(content_lower.count(term) for term in query_lower.split())
                    doc['relevance_score'] = score
                    relevant_docs.append(doc)
            
            # Sort by relevance
            relevant_docs.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            return relevant_docs[:5]  # Return top 5 most relevant
            
        except Exception as e:
            logger.error(f"Error searching knowledge base: {e}")
            return []
    
    async def get_user_documents(self, user_id: str) -> List[Dict]:
        """Get all documents uploaded by a user"""
        try:
            if not os.path.exists(self.knowledge_base_path):
                return []
            
            with open(self.knowledge_base_path, 'r', encoding='utf-8') as f:
                knowledge_base = json.load(f)
            
            user_docs = [
                {
                    "id": doc["id"],
                    "filename": doc["filename"],
                    "upload_date": doc["upload_date"],
                    "word_count": doc["word_count"],
                    "file_size": doc["file_size"],
                    "summary": doc["summary"]
                }
                for doc in knowledge_base
                if doc.get('user_id') == user_id
            ]
            
            return sorted(user_docs, key=lambda x: x['upload_date'], reverse=True)
            
        except Exception as e:
            logger.error(f"Error getting user documents: {e}")
            return []
    
    async def delete_document(self, file_id: str, user_id: str) -> bool:
        """Delete a document from the knowledge base"""
        try:
            if not os.path.exists(self.knowledge_base_path):
                return False
            
            with open(self.knowledge_base_path, 'r', encoding='utf-8') as f:
                knowledge_base = json.load(f)
            
            # Remove document
            original_count = len(knowledge_base)
            knowledge_base = [
                doc for doc in knowledge_base 
                if not (doc['id'] == file_id and doc.get('user_id') == user_id)
            ]
            
            if len(knowledge_base) < original_count:
                # Save updated knowledge base
                with open(self.knowledge_base_path, 'w', encoding='utf-8') as f:
                    json.dump(knowledge_base, f, indent=2, ensure_ascii=False)
                
                # Delete physical file
                file_path = os.path.join(self.documents_path, file_id)
                if os.path.exists(file_path):
                    os.remove(file_path)
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error deleting document: {e}")
            return False

# Global document processor instance
document_processor = DocumentProcessor()
