(()=>{var e={};e.id=931,e.ids=[931],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},3762:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,originalPathname:()=>d,pages:()=>h,routeModule:()=>p,tree:()=>u});var i=r(482),n=r(9108),s=r(2563),o=r.n(s),a=r(8300),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,751)),"C:\\Users\\<USER>\\Documents\\augment-projects\\tainoai\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2917)),"C:\\Users\\<USER>\\Documents\\augment-projects\\tainoai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"]}],h=["C:\\Users\\<USER>\\Documents\\augment-projects\\tainoai\\app\\page.tsx"],d="/page",c={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},1917:(e,t,r)=>{Promise.resolve().then(r.bind(r,4669))},7728:(e,t,r)=>{Promise.resolve().then(r.bind(r,5908))},9437:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,6840,23)),Promise.resolve().then(r.t.bind(r,8771,23)),Promise.resolve().then(r.t.bind(r,3225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,3982,23))},5908:(e,t,r)=>{"use strict";let i;r.r(t),r.d(t,{default:()=>a$});var n,s,o,a,l=r(2295),u=r(3729);function h(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function d(e){let t=[{},{}];return e?.values.forEach((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()}),t}function c(e,t,r,i){if("function"==typeof t){let[n,s]=d(i);t=t(void 0!==r?r:e.custom,n,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[n,s]=d(i);t=t(void 0!==r?r:e.custom,n,s)}return t}function p(e,t,r){let i=e.getProps();return c(i,t,void 0!==r?r:i.custom,e)}function m(e,t){return e?.[t]??e?.default??e}let f=e=>e,g={},y=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],v={value:null,addProjectionMetrics:null};function x(e,t){let r=!1,i=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>r=!0,o=y.reduce((e,r)=>(e[r]=function(e,t){let r=new Set,i=new Set,n=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){o.has(t)&&(h.schedule(t),e()),l++,t(a)}let h={schedule:(e,t=!1,s=!1)=>{let a=s&&n?r:i;return t&&o.add(e),a.has(e)||a.add(e),e},cancel:e=>{i.delete(e),o.delete(e)},process:e=>{if(a=e,n){s=!0;return}n=!0,[r,i]=[i,r],r.forEach(u),t&&v.value&&v.value.frameloop[t].push(l),l=0,r.clear(),n=!1,s&&(s=!1,h.process(e))}};return h}(s,t?r:void 0),e),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:h,update:d,preRender:c,render:p,postRender:m}=o,f=()=>{let s=g.useManualTiming?n.timestamp:performance.now();r=!1,g.useManualTiming||(n.delta=i?1e3/60:Math.max(Math.min(s-n.timestamp,40),1)),n.timestamp=s,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),h.process(n),d.process(n),c.process(n),p.process(n),m.process(n),n.isProcessing=!1,r&&t&&(i=!1,e(f))},x=()=>{r=!0,i=!0,n.isProcessing||e(f)};return{schedule:y.reduce((e,t)=>{let i=o[t];return e[t]=(e,t=!1,n=!1)=>(r||x(),i.schedule(e,t,n)),e},{}),cancel:e=>{for(let t=0;t<y.length;t++)o[y[t]].cancel(e)},state:n,steps:o}}let{schedule:b,cancel:w,state:T,steps:S}=x("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:f,!0),P=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],j=new Set(P),A=new Set(["width","height","top","left","right","bottom",...P]);function k(e,t){-1===e.indexOf(t)&&e.push(t)}function E(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}class C{constructor(){this.subscriptions=[]}add(e){return k(this.subscriptions,e),()=>E(this.subscriptions,e)}notify(e,t,r){let i=this.subscriptions.length;if(i){if(1===i)this.subscriptions[0](e,t,r);else for(let n=0;n<i;n++){let i=this.subscriptions[n];i&&i(e,t,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function M(){i=void 0}let D={now:()=>(void 0===i&&D.set(T.isProcessing||g.useManualTiming?T.timestamp:performance.now()),i),set:e=>{i=e,queueMicrotask(M)}},R=e=>!isNaN(parseFloat(e)),V={current:void 0};class N{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let r=D.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=D.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=R(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new C);let r=this.events[e].add(t);return"change"===e?()=>{r(),b.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-r}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return V.current&&V.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=D.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let r=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),r?1e3/r*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function L(e,t){return new N(e,t)}let F=e=>Array.isArray(e),O=e=>!!(e&&e.getVelocity);function I(e,t){let r=e.getValue("willChange");if(O(r)&&r.add)return r.add(t);if(!r&&g.WillChange){let r=new g.WillChange("auto");e.addValue("willChange",r),r.add(t)}}let B=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),$="data-"+B("framerAppearId"),U=(e,t)=>r=>t(e(r)),z=(...e)=>e.reduce(U),W=(e,t,r)=>r>t?t:r<e?e:r,_=e=>1e3*e,H=e=>e/1e3,Y={layout:0,mainThread:0,waapi:0},X=()=>{},q=()=>{},G=e=>t=>"string"==typeof t&&t.startsWith(e),K=G("--"),Z=G("var(--"),J=e=>!!Z(e)&&Q.test(e.split("/*")[0].trim()),Q=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ee={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},et={...ee,transform:e=>W(0,1,e)},er={...ee,default:1},ei=e=>Math.round(1e5*e)/1e5,en=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,es=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,eo=(e,t)=>r=>!!("string"==typeof r&&es.test(r)&&r.startsWith(e)||t&&null!=r&&Object.prototype.hasOwnProperty.call(r,t)),ea=(e,t,r)=>i=>{if("string"!=typeof i)return i;let[n,s,o,a]=i.match(en);return{[e]:parseFloat(n),[t]:parseFloat(s),[r]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},el=e=>W(0,255,e),eu={...ee,transform:e=>Math.round(el(e))},eh={test:eo("rgb","red"),parse:ea("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:i=1})=>"rgba("+eu.transform(e)+", "+eu.transform(t)+", "+eu.transform(r)+", "+ei(et.transform(i))+")"},ed={test:eo("#"),parse:function(e){let t="",r="",i="",n="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),i=e.substring(5,7),n=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),i=e.substring(3,4),n=e.substring(4,5),t+=t,r+=r,i+=i,n+=n),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(i,16),alpha:n?parseInt(n,16)/255:1}},transform:eh.transform},ec=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),ep=ec("deg"),em=ec("%"),ef=ec("px"),eg=ec("vh"),ey=ec("vw"),ev={...em,parse:e=>em.parse(e)/100,transform:e=>em.transform(100*e)},ex={test:eo("hsl","hue"),parse:ea("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:i=1})=>"hsla("+Math.round(e)+", "+em.transform(ei(t))+", "+em.transform(ei(r))+", "+ei(et.transform(i))+")"},eb={test:e=>eh.test(e)||ed.test(e)||ex.test(e),parse:e=>eh.test(e)?eh.parse(e):ex.test(e)?ex.parse(e):ed.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eh.transform(e):ex.transform(e)},ew=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eT="number",eS="color",eP=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ej(e){let t=e.toString(),r=[],i={color:[],number:[],var:[]},n=[],s=0,o=t.replace(eP,e=>(eb.test(e)?(i.color.push(s),n.push(eS),r.push(eb.parse(e))):e.startsWith("var(")?(i.var.push(s),n.push("var"),r.push(e)):(i.number.push(s),n.push(eT),r.push(parseFloat(e))),++s,"${}")).split("${}");return{values:r,split:o,indexes:i,types:n}}function eA(e){return ej(e).values}function ek(e){let{split:t,types:r}=ej(e),i=t.length;return e=>{let n="";for(let s=0;s<i;s++)if(n+=t[s],void 0!==e[s]){let t=r[s];t===eT?n+=ei(e[s]):t===eS?n+=eb.transform(e[s]):n+=e[s]}return n}}let eE=e=>"number"==typeof e?0:e,eC={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(en)?.length||0)+(e.match(ew)?.length||0)>0},parse:eA,createTransformer:ek,getAnimatableNone:function(e){let t=eA(e);return ek(e)(t.map(eE))}};function eM(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function eD(e,t){return r=>r>0?t:e}let eR=(e,t,r)=>e+(t-e)*r,eV=(e,t,r)=>{let i=e*e,n=r*(t*t-i)+i;return n<0?0:Math.sqrt(n)},eN=[ed,eh,ex],eL=e=>eN.find(t=>t.test(e));function eF(e){let t=eL(e);if(X(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let r=t.parse(e);return t===ex&&(r=function({hue:e,saturation:t,lightness:r,alpha:i}){e/=360,r/=100;let n=0,s=0,o=0;if(t/=100){let i=r<.5?r*(1+t):r+t-r*t,a=2*r-i;n=eM(a,i,e+1/3),s=eM(a,i,e),o=eM(a,i,e-1/3)}else n=s=o=r;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:i}}(r)),r}let eO=(e,t)=>{let r=eF(e),i=eF(t);if(!r||!i)return eD(e,t);let n={...r};return e=>(n.red=eV(r.red,i.red,e),n.green=eV(r.green,i.green,e),n.blue=eV(r.blue,i.blue,e),n.alpha=eR(r.alpha,i.alpha,e),eh.transform(n))},eI=new Set(["none","hidden"]);function eB(e,t){return r=>eR(e,t,r)}function e$(e){return"number"==typeof e?eB:"string"==typeof e?J(e)?eD:eb.test(e)?eO:eW:Array.isArray(e)?eU:"object"==typeof e?eb.test(e)?eO:ez:eD}function eU(e,t){let r=[...e],i=r.length,n=e.map((e,r)=>e$(e)(e,t[r]));return e=>{for(let t=0;t<i;t++)r[t]=n[t](e);return r}}function ez(e,t){let r={...e,...t},i={};for(let n in r)void 0!==e[n]&&void 0!==t[n]&&(i[n]=e$(e[n])(e[n],t[n]));return e=>{for(let t in i)r[t]=i[t](e);return r}}let eW=(e,t)=>{let r=eC.createTransformer(t),i=ej(e),n=ej(t);return i.indexes.var.length===n.indexes.var.length&&i.indexes.color.length===n.indexes.color.length&&i.indexes.number.length>=n.indexes.number.length?eI.has(e)&&!n.values.length||eI.has(t)&&!i.values.length?function(e,t){return eI.has(e)?r=>r<=0?e:t:r=>r>=1?t:e}(e,t):z(eU(function(e,t){let r=[],i={color:0,var:0,number:0};for(let n=0;n<t.values.length;n++){let s=t.types[n],o=e.indexes[s][i[s]],a=e.values[o]??0;r[n]=a,i[s]++}return r}(i,n),n.values),r):(X(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eD(e,t))};function e_(e,t,r){return"number"==typeof e&&"number"==typeof t&&"number"==typeof r?eR(e,t,r):e$(e)(e,t)}let eH=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>b.update(t,e),stop:()=>w(t),now:()=>T.isProcessing?T.timestamp:D.now()}},eY=(e,t,r=10)=>{let i="",n=Math.max(Math.round(t/r),2);for(let t=0;t<n;t++)i+=e(t/(n-1))+", ";return`linear(${i.substring(0,i.length-2)})`};function eX(e){let t=0,r=e.next(t);for(;!r.done&&t<2e4;)t+=50,r=e.next(t);return t>=2e4?1/0:t}function eq(e,t,r){var i,n;let s=Math.max(t-5,0);return i=r-e(s),(n=t-s)?1e3/n*i:0}let eG={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eK(e,t){return e*Math.sqrt(1-t*t)}let eZ=["duration","bounce"],eJ=["stiffness","damping","mass"];function eQ(e,t){return t.some(t=>void 0!==e[t])}function e0(e=eG.visualDuration,t=eG.bounce){let r;let i="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:n,restDelta:s}=i,o=i.keyframes[0],a=i.keyframes[i.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(e){let t={velocity:eG.velocity,stiffness:eG.stiffness,damping:eG.damping,mass:eG.mass,isResolvedFromDuration:!1,...e};if(!eQ(e,eJ)&&eQ(e,eZ)){if(e.visualDuration){let r=2*Math.PI/(1.2*e.visualDuration),i=r*r,n=2*W(.05,1,1-(e.bounce||0))*Math.sqrt(i);t={...t,mass:eG.mass,stiffness:i,damping:n}}else{let r=function({duration:e=eG.duration,bounce:t=eG.bounce,velocity:r=eG.velocity,mass:i=eG.mass}){let n,s;X(e<=_(eG.maxDuration),"Spring duration must be 10 seconds or less");let o=1-t;o=W(eG.minDamping,eG.maxDamping,o),e=W(eG.minDuration,eG.maxDuration,H(e)),o<1?(n=t=>{let i=t*o,n=i*e;return .001-(i-r)/eK(t,o)*Math.exp(-n)},s=t=>{let i=t*o*e,s=Math.pow(o,2)*Math.pow(t,2)*e,a=eK(Math.pow(t,2),o);return(i*r+r-s)*Math.exp(-i)*(-n(t)+.001>0?-1:1)/a}):(n=t=>-.001+Math.exp(-t*e)*((t-r)*e+1),s=t=>e*e*(r-t)*Math.exp(-t*e));let a=function(e,t,r){let i=r;for(let r=1;r<12;r++)i-=e(i)/t(i);return i}(n,s,5/e);if(e=_(e),isNaN(a))return{stiffness:eG.stiffness,damping:eG.damping,duration:e};{let t=Math.pow(a,2)*i;return{stiffness:t,damping:2*o*Math.sqrt(i*t),duration:e}}}(e);(t={...t,...r,mass:eG.mass}).isResolvedFromDuration=!0}}return t}({...i,velocity:-H(i.velocity||0)}),f=p||0,g=h/(2*Math.sqrt(u*d)),y=a-o,v=H(Math.sqrt(u/d)),x=5>Math.abs(y);if(n||(n=x?eG.restSpeed.granular:eG.restSpeed.default),s||(s=x?eG.restDelta.granular:eG.restDelta.default),g<1){let e=eK(v,g);r=t=>a-Math.exp(-g*v*t)*((f+g*v*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)r=e=>a-Math.exp(-v*e)*(y+(f+v*y)*e);else{let e=v*Math.sqrt(g*g-1);r=t=>{let r=Math.exp(-g*v*t),i=Math.min(e*t,300);return a-r*((f+g*v*y)*Math.sinh(i)+e*y*Math.cosh(i))/e}}let b={calculatedDuration:m&&c||null,next:e=>{let t=r(e);if(m)l.done=e>=c;else{let i=0===e?f:0;g<1&&(i=0===e?_(f):eq(r,e,t));let o=Math.abs(i)<=n,u=Math.abs(a-t)<=s;l.done=o&&u}return l.value=l.done?a:t,l},toString:()=>{let e=Math.min(eX(b),2e4),t=eY(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function e1({keyframes:e,velocity:t=0,power:r=.8,timeConstant:i=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c;let p=e[0],m={done:!1,value:p},f=e=>void 0!==a&&e<a||void 0!==l&&e>l,g=e=>void 0===a?l:void 0===l?a:Math.abs(a-e)<Math.abs(l-e)?a:l,y=r*t,v=p+y,x=void 0===o?v:o(v);x!==v&&(y=x-p);let b=e=>-y*Math.exp(-e/i),w=e=>x+b(e),T=e=>{let t=b(e),r=w(e);m.done=Math.abs(t)<=u,m.value=m.done?x:r},S=e=>{f(m.value)&&(d=e,c=e0({keyframes:[m.value,g(m.value)],velocity:eq(w,e,m.value),damping:n,stiffness:s,restDelta:u,restSpeed:h}))};return S(0),{calculatedDuration:null,next:e=>{let t=!1;return(c||void 0!==d||(t=!0,T(e),S(e)),void 0!==d&&e>=d)?c.next(e-d):(t||T(e),m)}}}e0.applyToOptions=e=>{let t=function(e,t=100,r){let i=r({...e,keyframes:[0,t]}),n=Math.min(eX(i),2e4);return{type:"keyframes",ease:e=>i.next(n*e).value/t,duration:H(n)}}(e,100,e0);return e.ease=t.ease,e.duration=_(t.duration),e.type="keyframes",e};let e2=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function e4(e,t,r,i){if(e===t&&r===i)return f;let n=t=>(function(e,t,r,i,n){let s,o;let a=0;do(s=e2(o=t+(r-t)/2,i,n)-e)>0?r=o:t=o;while(Math.abs(s)>1e-7&&++a<12);return o})(t,0,1,e,r);return e=>0===e||1===e?e:e2(n(e),t,i)}let e5=e4(.42,0,1,1),e3=e4(0,0,.58,1),e9=e4(.42,0,.58,1),e6=e=>Array.isArray(e)&&"number"!=typeof e[0],e8=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e7=e=>t=>1-e(1-t),te=e4(.33,1.53,.69,.99),tt=e7(te),tr=e8(tt),ti=e=>(e*=2)<1?.5*tt(e):.5*(2-Math.pow(2,-10*(e-1))),tn=e=>1-Math.sin(Math.acos(e)),ts=e7(tn),to=e8(tn),ta=e=>Array.isArray(e)&&"number"==typeof e[0],tl={linear:f,easeIn:e5,easeInOut:e9,easeOut:e3,circIn:tn,circInOut:to,circOut:ts,backIn:tt,backInOut:tr,backOut:te,anticipate:ti},tu=e=>"string"==typeof e,th=e=>{if(ta(e)){q(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,i,n]=e;return e4(t,r,i,n)}return tu(e)?(q(void 0!==tl[e],`Invalid easing type '${e}'`),tl[e]):e},td=(e,t,r)=>{let i=t-e;return 0===i?1:(r-e)/i};function tc({duration:e=300,keyframes:t,times:r,ease:i="easeInOut"}){let n=e6(i)?i.map(th):th(i),s={done:!1,value:t[0]},o=function(e,t,{clamp:r=!0,ease:i,mixer:n}={}){let s=e.length;if(q(s===t.length,"Both input and output ranges must be the same length"),1===s)return()=>t[0];if(2===s&&t[0]===t[1])return()=>t[1];let o=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,r){let i=[],n=r||g.mix||e_,s=e.length-1;for(let r=0;r<s;r++){let s=n(e[r],e[r+1]);t&&(s=z(Array.isArray(t)?t[r]||f:t,s)),i.push(s)}return i}(t,i,n),l=a.length,u=r=>{if(o&&r<e[0])return t[0];let i=0;if(l>1)for(;i<e.length-2&&!(r<e[i+1]);i++);let n=td(e[i],e[i+1],r);return a[i](n)};return r?t=>u(W(e[0],e[s-1],t)):u}((r&&r.length===t.length?r:function(e){let t=[0];return function(e,t){let r=e[e.length-1];for(let i=1;i<=t;i++){let n=td(0,t,i);e.push(eR(r,1,n))}}(t,e.length-1),t}(t)).map(t=>t*e),t,{ease:Array.isArray(n)?n:t.map(()=>n||e9).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=o(t),s.done=t>=e,s)}}let tp=e=>null!==e;function tm(e,{repeat:t,repeatType:r="loop"},i,n=1){let s=e.filter(tp),o=n<0||t&&"loop"!==r&&t%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}let tf={decay:e1,inertia:e1,tween:tc,keyframes:tc,spring:e0};function tg(e){"string"==typeof e.type&&(e.type=tf[e.type])}class ty{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let tv=e=>e/100;class tx extends ty{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==D.now()&&this.tick(D.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},Y.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tg(e);let{type:t=tc,repeat:r=0,repeatDelay:i=0,repeatType:n,velocity:s=0}=e,{keyframes:o}=e,a=t||tc;a!==tc&&"number"!=typeof o[0]&&(this.mixKeyframes=z(tv,e_(o[0],o[1])),o=[0,100]);let l=a({...e,keyframes:o});"mirror"===n&&(this.mirroredGenerator=a({...e,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=eX(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(r+1)-i,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:r,totalDuration:i,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return r.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-i/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let v=this.currentTime,x=r;if(h){let e=Math.min(this.currentTime,i)/o,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,(t=Math.min(t,h+1))%2&&("reverse"===d?(r=1-r,c&&(r-=c/o)):"mirror"===d&&(x=s)),v=W(0,1,r)*o}let b=y?{done:!1,value:u[0]}:x.next(v);n&&(b.value=n(b.value));let{done:w}=b;y||null===a||(w=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return T&&p!==e1&&(b.value=tm(u,this.options,f,this.speed)),m&&m(b.value),T&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return H(this.calculatedDuration)}get time(){return H(this.currentTime)}set time(e){e=_(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(D.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=H(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eH,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let r=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=r):null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime||(this.startTime=t??r),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(D.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,Y.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tb=e=>180*e/Math.PI,tw=e=>tS(tb(Math.atan2(e[1],e[0]))),tT={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tw,rotateZ:tw,skewX:e=>tb(Math.atan(e[1])),skewY:e=>tb(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},tS=e=>((e%=360)<0&&(e+=360),e),tP=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tj=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tA={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tP,scaleY:tj,scale:e=>(tP(e)+tj(e))/2,rotateX:e=>tS(tb(Math.atan2(e[6],e[5]))),rotateY:e=>tS(tb(Math.atan2(-e[2],e[0]))),rotateZ:tw,rotate:tw,skewX:e=>tb(Math.atan(e[4])),skewY:e=>tb(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tk(e){return e.includes("scale")?1:0}function tE(e,t){let r,i;if(!e||"none"===e)return tk(t);let n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)r=tA,i=n;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=tT,i=t}if(!i)return tk(t);let s=r[t],o=i[1].split(",").map(tM);return"function"==typeof s?s(o):o[s]}let tC=(e,t)=>{let{transform:r="none"}=getComputedStyle(e);return tE(r,t)};function tM(e){return parseFloat(e.trim())}let tD=e=>e===ee||e===ef,tR=new Set(["x","y","z"]),tV=P.filter(e=>!tR.has(e)),tN={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tE(t,"x"),y:(e,{transform:t})=>tE(t,"y")};tN.translateX=tN.x,tN.translateY=tN.y;let tL=new Set,tF=!1,tO=!1,tI=!1;function tB(){if(tO){let e=Array.from(tL).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),r=new Map;t.forEach(e=>{let t=function(e){let t=[];return tV.forEach(r=>{let i=e.getValue(r);void 0!==i&&(t.push([r,i.get()]),i.set(r.startsWith("scale")?1:0))}),t}(e);t.length&&(r.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=r.get(e);t&&t.forEach(([t,r])=>{e.getValue(t)?.set(r)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tO=!1,tF=!1,tL.forEach(e=>e.complete(tI)),tL.clear()}function t$(){tL.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tO=!0)})}class tU{constructor(e,t,r,i,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=r,this.motionValue=i,this.element=n,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(tL.add(this),tF||(tF=!0,b.read(t$),b.resolveKeyframes(tB))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:r,motionValue:i}=this;if(null===e[0]){let n=i?.get(),s=e[e.length-1];if(void 0!==n)e[0]=n;else if(r&&t){let i=r.readValue(t,s);null!=i&&(e[0]=i)}void 0===e[0]&&(e[0]=s),i&&void 0===n&&i.set(e[0])}!function(e){for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tL.delete(this)}cancel(){"scheduled"===this.state&&(tL.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tz=e=>e.startsWith("--");function tW(e){let t;return()=>(void 0===t&&(t=e()),t)}let t_=tW(()=>void 0!==window.ScrollTimeline),tH={},tY=function(e,t){let r=tW(e);return()=>tH[t]??r()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tX=([e,t,r,i])=>`cubic-bezier(${e}, ${t}, ${r}, ${i})`,tq={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tX([0,.65,.55,1]),circOut:tX([.55,0,1,.45]),backIn:tX([.31,.01,.66,-.59]),backOut:tX([.33,1.53,.69,.99])};function tG(e){return"function"==typeof e&&"applyToOptions"in e}class tK extends ty{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:r,keyframes:i,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=e;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=e,q("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tG(e)&&tY()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,r,{delay:i=0,duration:n=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[t]:r};l&&(h.offset=l);let d=function e(t,r){if(t)return"function"==typeof t?tY()?eY(t,r):"ease-out":ta(t)?tX(t):Array.isArray(t)?t.map(t=>e(t,r)||tq.easeOut):tq[t]}(a,n);Array.isArray(d)&&(h.easing=d),v.value&&Y.waapi++;let c={delay:i,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(c.pseudoElement=u);let p=e.animate(h,c);return v.value&&p.finished.finally(()=>{Y.waapi--}),p}(t,r,i,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let e=tm(i,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,r){tz(t)?e.style.setProperty(t,r):e.style[t]=r}(t,r,e),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return H(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return H(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=_(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&t_())?(this.animation.timeline=e,f):t(this)}}let tZ={anticipate:ti,backInOut:tr,circInOut:to};class tJ extends tK{constructor(e){(function(e){"string"==typeof e.ease&&e.ease in tZ&&(e.ease=tZ[e.ease])})(e),tg(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:r,onComplete:i,element:n,...s}=this.options;if(!t)return;if(void 0!==e){t.set(e);return}let o=new tx({...s,autoplay:!1}),a=_(this.finishedTime??this.time);t.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let tQ=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eC.test(e)||"0"===e)&&!e.startsWith("url("));function t0(e){return"object"==typeof e&&null!==e}function t1(e){return t0(e)&&"offsetHeight"in e}let t2=new Set(["opacity","clipPath","filter","transform"]),t4=tW(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class t5 extends ty{constructor({autoplay:e=!0,delay:t=0,type:r="keyframes",repeat:i=0,repeatDelay:n=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=D.now();let d={autoplay:e,delay:t,type:r,repeat:i,repeatDelay:n,repeatType:s,name:a,motionValue:l,element:u,...h},c=u?.KeyframeResolver||tU;this.keyframeResolver=new c(o,(e,t,r)=>this.onKeyframesResolved(e,t,d,!r),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,r,i){this.keyframeResolver=void 0;let{name:n,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:u}=r;this.resolvedAt=D.now(),!function(e,t,r,i){let n=e[0];if(null===n)return!1;if("display"===t||"visibility"===t)return!0;let s=e[e.length-1],o=tQ(n,t),a=tQ(s,t);return X(o===a,`You are trying to animate ${t} from "${n}" to "${s}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(e){let t=e[0];if(1===e.length)return!0;for(let r=0;r<e.length;r++)if(e[r]!==t)return!0}(e)||("spring"===r||tG(r))&&i)}(e,n,s,o)&&((g.instantAnimations||!a)&&u?.(tm(e,r,t)),e[0]=e[e.length-1],r.duration=0,r.repeat=0);let h={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...r,keyframes:e},d=!l&&function(e){let{motionValue:t,name:r,repeatDelay:i,repeatType:n,damping:s,type:o}=e;if(!t1(t?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=t.owner.getProps();return t4()&&r&&t2.has(r)&&("transform"!==r||!l)&&!a&&!i&&"mirror"!==n&&0!==s&&"inertia"!==o}(h)?new tJ({...h,element:h.motionValue.owner.current}):new tx(h);d.finished.then(()=>this.notifyFinished()).catch(f),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tI=!0,t$(),tB(),tI=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t3=e=>null!==e,t9={type:"spring",stiffness:500,damping:25,restSpeed:10},t6=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t8={type:"keyframes",duration:.8},t7={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},re=(e,{keyframes:t})=>t.length>2?t8:j.has(e)?e.startsWith("scale")?t6(t[1]):t9:t7,rt=(e,t,r,i={},n,s)=>o=>{let a=m(i,e)||{},l=a.delay||i.delay||0,{elapsed:u=0}=i;u-=_(l);let h={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:e=>{t.set(e),a.onUpdate&&a.onUpdate(e)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:s?void 0:n};!function({when:e,delay:t,delayChildren:r,staggerChildren:i,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(h,re(e,h)),h.duration&&(h.duration=_(h.duration)),h.repeatDelay&&(h.repeatDelay=_(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0!==h.delay||(d=!0)),(g.instantAnimations||g.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),h.allowFlatten=!a.type&&!a.ease,d&&!s&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:r="loop"},i){let n=e.filter(t3),s=t&&"loop"!==r&&t%2==1?0:n.length-1;return s&&void 0!==i?i:n[s]}(h.keyframes,a);if(void 0!==e){b.update(()=>{h.onUpdate(e),h.onComplete()});return}}return a.isSync?new tx(h):new t5(h)};function rr(e,t,{delay:r=0,transitionOverride:i,type:n}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:o,...a}=t;i&&(s=i);let l=[],u=n&&e.animationState&&e.animationState.getState()[n];for(let t in a){let i=e.getValue(t,e.latestValues[t]??null),n=a[t];if(void 0===n||u&&function({protectedKeys:e,needsAnimating:t},r){let i=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,i}(u,t))continue;let o={delay:r,...m(s||{},t)},h=i.get();if(void 0!==h&&!i.isAnimating&&!Array.isArray(n)&&n===h&&!o.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let r=e.props[$];if(r){let e=window.MotionHandoffAnimation(r,t,b);null!==e&&(o.startTime=e,d=!0)}}I(e,t),i.start(rt(t,i,n,e.shouldReduceMotion&&A.has(t)?{type:!1}:o,e,d));let c=i.animation;c&&l.push(c)}return o&&Promise.all(l).then(()=>{b.update(()=>{o&&function(e,t){let{transitionEnd:r={},transition:i={},...n}=p(e,t)||{};for(let t in n={...n,...r}){var s;let r=F(s=n[t])?s[s.length-1]||0:s;e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,L(r))}}(e,o)})}),l}function ri(e,t,r={}){let i=p(e,t,"exit"===r.type?e.presenceContext?.custom:void 0),{transition:n=e.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(n=r.transitionOverride);let s=i?()=>Promise.all(rr(e,i,r)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(i=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=n;return function(e,t,r=0,i=0,n=1,s){let o=[],a=(e.variantChildren.size-1)*i,l=1===n?(e=0)=>e*i:(e=0)=>a-e*i;return Array.from(e.variantChildren).sort(rn).forEach((e,i)=>{e.notify("AnimationStart",t),o.push(ri(e,t,{...s,delay:r+l(i)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(o)}(e,t,s+i,o,a,r)}:()=>Promise.resolve(),{when:a}=n;if(!a)return Promise.all([s(),o(r.delay)]);{let[e,t]="beforeChildren"===a?[s,o]:[o,s];return e().then(()=>t())}}function rn(e,t){return e.sortNodePosition(t)}function rs(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let i=0;i<r;i++)if(t[i]!==e[i])return!1;return!0}function ro(e){return"string"==typeof e||Array.isArray(e)}let ra=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],rl=["initial",...ra],ru=rl.length,rh=[...ra].reverse(),rd=ra.length;function rc(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rp(){return{animate:rc(!0),whileInView:rc(),whileHover:rc(),whileTap:rc(),whileDrag:rc(),whileFocus:rc(),exit:rc()}}class rm{constructor(e){this.isMounted=!1,this.node=e}update(){}}class rf extends rm{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let i;if(e.notify("AnimationStart",t),Array.isArray(t))i=Promise.all(t.map(t=>ri(e,t,r)));else if("string"==typeof t)i=ri(e,t,r);else{let n="function"==typeof t?p(e,t,r.custom):t;i=Promise.all(rr(e,n,r))}return i.then(()=>{e.notify("AnimationComplete",t)})})(e,t,r))),r=rp(),i=!0,n=t=>(r,i)=>{let n=p(e,i,"exit"===t?e.presenceContext?.custom:void 0);if(n){let{transition:e,transitionEnd:t,...i}=n;r={...r,...i,...t}}return r};function s(s){let{props:o}=e,a=function e(t){if(!t)return;if(!t.isControllingVariants){let r=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(r.initial=t.props.initial),r}let r={};for(let e=0;e<ru;e++){let i=rl[e],n=t.props[i];(ro(n)||!1===n)&&(r[i]=n)}return r}(e.parent)||{},l=[],u=new Set,d={},c=1/0;for(let t=0;t<rd;t++){var m;let p=rh[t],f=r[p],g=void 0!==o[p]?o[p]:a[p],y=ro(g),v=p===s?f.isActive:null;!1===v&&(c=t);let x=g===a[p]&&g!==o[p]&&y;if(x&&i&&e.manuallyAnimateOnMount&&(x=!1),f.protectedKeys={...d},!f.isActive&&null===v||!g&&!f.prevProp||h(g)||"boolean"==typeof g)continue;let b=(m=f.prevProp,"string"==typeof g?g!==m:!!Array.isArray(g)&&!rs(g,m)),w=b||p===s&&f.isActive&&!x&&y||t>c&&y,T=!1,S=Array.isArray(g)?g:[g],P=S.reduce(n(p),{});!1===v&&(P={});let{prevResolvedValues:j={}}=f,A={...j,...P},k=t=>{w=!0,u.has(t)&&(T=!0,u.delete(t)),f.needsAnimating[t]=!0;let r=e.getValue(t);r&&(r.liveStyle=!1)};for(let e in A){let t=P[e],r=j[e];if(!d.hasOwnProperty(e))(F(t)&&F(r)?rs(t,r):t===r)?void 0!==t&&u.has(e)?k(e):f.protectedKeys[e]=!0:null!=t?k(e):u.add(e)}f.prevProp=g,f.prevResolvedValues=P,f.isActive&&(d={...d,...P}),i&&e.blockInitialAnimation&&(w=!1);let E=!(x&&b)||T;w&&E&&l.push(...S.map(e=>({animation:e,options:{type:p}})))}if(u.size){let t={};if("boolean"!=typeof o.initial){let r=p(e,Array.isArray(o.initial)?o.initial[0]:o.initial);r&&r.transition&&(t.transition=r.transition)}u.forEach(r=>{let i=e.getBaseTarget(r),n=e.getValue(r);n&&(n.liveStyle=!0),t[r]=i??null}),l.push({animation:t})}let f=!!l.length;return i&&(!1===o.initial||o.initial===o.animate)&&!e.manuallyAnimateOnMount&&(f=!1),i=!1,f?t(l):Promise.resolve()}return{animateChanges:s,setActive:function(t,i){if(r[t].isActive===i)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,i)),r[t].isActive=i;let n=s(t);for(let e in r)r[e].protectedKeys={};return n},setAnimateFunction:function(r){t=r(e)},getState:()=>r,reset:()=>{r=rp(),i=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();h(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let rg=0;class ry extends rm{constructor(){super(...arguments),this.id=rg++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;let i=this.node.animationState.setActive("exit",!e);t&&!e&&i.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let rv={x:!1,y:!1};function rx(e,t,r,i={passive:!0}){return e.addEventListener(t,r,i),()=>e.removeEventListener(t,r)}let rb=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function rw(e){return{point:{x:e.pageX,y:e.pageY}}}let rT=e=>t=>rb(t)&&e(t,rw(t));function rS(e,t,r,i){return rx(e,t,rT(r),i)}function rP({top:e,left:t,right:r,bottom:i}){return{x:{min:t,max:r},y:{min:e,max:i}}}function rj(e){return e.max-e.min}function rA(e,t,r,i=.5){e.origin=i,e.originPoint=eR(t.min,t.max,e.origin),e.scale=rj(r)/rj(t),e.translate=eR(r.min,r.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function rk(e,t,r,i){rA(e.x,t.x,r.x,i?i.originX:void 0),rA(e.y,t.y,r.y,i?i.originY:void 0)}function rE(e,t,r){e.min=r.min+t.min,e.max=e.min+rj(t)}function rC(e,t,r){e.min=t.min-r.min,e.max=e.min+rj(t)}function rM(e,t,r){rC(e.x,t.x,r.x),rC(e.y,t.y,r.y)}let rD=()=>({translate:0,scale:1,origin:0,originPoint:0}),rR=()=>({x:rD(),y:rD()}),rV=()=>({min:0,max:0}),rN=()=>({x:rV(),y:rV()});function rL(e){return[e("x"),e("y")]}function rF(e){return void 0===e||1===e}function rO({scale:e,scaleX:t,scaleY:r}){return!rF(e)||!rF(t)||!rF(r)}function rI(e){return rO(e)||rB(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function rB(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function r$(e,t,r,i,n){return void 0!==n&&(e=i+n*(e-i)),i+r*(e-i)+t}function rU(e,t=0,r=1,i,n){e.min=r$(e.min,t,r,i,n),e.max=r$(e.max,t,r,i,n)}function rz(e,{x:t,y:r}){rU(e.x,t.translate,t.scale,t.originPoint),rU(e.y,r.translate,r.scale,r.originPoint)}function rW(e,t){e.min=e.min+t,e.max=e.max+t}function r_(e,t,r,i,n=.5){let s=eR(e.min,e.max,n);rU(e,t,r,s,i)}function rH(e,t){r_(e.x,t.x,t.scaleX,t.scale,t.originX),r_(e.y,t.y,t.scaleY,t.scale,t.originY)}function rY(e,t){return rP(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),i=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:i.y,right:i.x}}(e.getBoundingClientRect(),t))}let rX=({current:e})=>e?e.ownerDocument.defaultView:null;function rq(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let rG=(e,t)=>Math.abs(e-t);class rK{constructor(e,t,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rQ(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){return Math.sqrt(rG(e.x,t.x)**2+rG(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;let{point:i}=e,{timestamp:n}=T;this.history.push({...i,timestamp:n});let{onStart:s,onMove:o}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rZ(t,this.transformPagePoint),b.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:i,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=rQ("pointercancel"===e.type?this.lastMoveEventInfo:rZ(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,s),i&&i(e,s)},!rb(e))return;this.dragSnapToOrigin=n,this.handlers=t,this.transformPagePoint=r,this.contextWindow=i||window;let s=rZ(rw(e),this.transformPagePoint),{point:o}=s,{timestamp:a}=T;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=t;l&&l(e,rQ(s,this.history)),this.removeListeners=z(rS(this.contextWindow,"pointermove",this.handlePointerMove),rS(this.contextWindow,"pointerup",this.handlePointerUp),rS(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),w(this.updatePoint)}}function rZ(e,t){return t?{point:t(e.point)}:e}function rJ(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rQ({point:e},t){return{point:e,delta:rJ(e,r0(t)),offset:rJ(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,i=null,n=r0(e);for(;r>=0&&(i=e[r],!(n.timestamp-i.timestamp>_(.1)));)r--;if(!i)return{x:0,y:0};let s=H(n.timestamp-i.timestamp);if(0===s)return{x:0,y:0};let o={x:(n.x-i.x)/s,y:(n.y-i.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(t,0)}}function r0(e){return e[e.length-1]}function r1(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function r2(e,t){let r=t.min-e.min,i=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,i]=[i,r]),{min:r,max:i}}function r4(e,t,r){return{min:r5(e,t),max:r5(e,r)}}function r5(e,t){return"number"==typeof e?e:e[t]||0}let r3=new WeakMap;class r9{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=rN(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new rK(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(rw(e).point)},onStart:(e,t)=>{let{drag:r,dragPropagation:i,onDragStart:n}=this.getProps();if(r&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===r||"y"===r?rv[r]?null:(rv[r]=!0,()=>{rv[r]=!1}):rv.x||rv.y?null:(rv.x=rv.y=!0,()=>{rv.x=rv.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rL(e=>{let t=this.getAxisMotionValue(e).get()||0;if(em.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let i=r.layout.layoutBox[e];if(i){let e=rj(i);t=parseFloat(t)/100*e}}}this.originPoint[e]=t}),n&&b.postRender(()=>n(e,t)),I(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:r,dragDirectionLock:i,onDirectionLock:n,onDrag:s}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:o}=t;if(i&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",t.point,o),this.updateAxis("y",t.point,o),this.visualElement.render(),s&&s(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>rL(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:rX(this.visualElement)})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:i}=t;this.startAnimation(i);let{onDragEnd:n}=this.getProps();n&&b.postRender(()=>n(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:i}=this.getProps();if(!r||!r6(e,i,this.currentDirection))return;let n=this.getAxisMotionValue(e),s=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(s=function(e,{min:t,max:r},i){return void 0!==t&&e<t?e=i?eR(t,e,i.min):Math.max(e,t):void 0!==r&&e>r&&(e=i?eR(r,e,i.max):Math.min(e,r)),e}(s,this.constraints[e],this.elastic[e])),n.set(s)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;e&&rq(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&r?this.constraints=function(e,{top:t,left:r,bottom:i,right:n}){return{x:r1(e.x,r,n),y:r1(e.y,t,i)}}(r.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:r4(e,"left","right"),y:r4(e,"top","bottom")}}(t),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&rL(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!rq(t))return!1;let i=t.current;q(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(e,t,r){let i=rY(e,r),{scroll:n}=t;return n&&(rW(i.x,n.offset.x),rW(i.y,n.offset.y)),i}(i,n.root,this.visualElement.getTransformPagePoint()),o={x:r2((e=n.layout.layoutBox).x,s.x),y:r2(e.y,s.y)};if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(o));this.hasMutatedConstraints=!!e,e&&(o=rP(e))}return o}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:i,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(rL(o=>{if(!r6(o,t,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[o]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return I(this.visualElement,e),r.start(rt(e,r,0,t,this.visualElement,!1))}stopAnimation(){rL(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){rL(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){rL(t=>{let{drag:r}=this.getProps();if(!r6(t,r,this.currentDirection))return;let{projection:i}=this.visualElement,n=this.getAxisMotionValue(t);if(i&&i.layout){let{min:r,max:s}=i.layout.layoutBox[t];n.set(e[t]-eR(r,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!rq(t)||!r||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};rL(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let r=t.get();i[e]=function(e,t){let r=.5,i=rj(e),n=rj(t);return n>i?r=td(t.min,t.max-i,e.min):i>n&&(r=td(e.min,e.max-n,t.min)),W(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),rL(t=>{if(!r6(t,e,null))return;let r=this.getAxisMotionValue(t),{min:n,max:s}=this.constraints[t];r.set(eR(n,s,i[t]))})}addListeners(){if(!this.visualElement.current)return;r3.set(this.visualElement,this);let e=rS(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();rq(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,i=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),b.read(t);let n=rx(window,"resize",()=>this.scalePositionWithinConstraints()),s=r.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(rL(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{n(),e(),i(),s&&s()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:i,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function r6(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class r8 extends rm{constructor(e){super(e),this.removeGroupControls=f,this.removeListeners=f,this.controls=new r9(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||f}unmount(){this.removeGroupControls(),this.removeListeners()}}let r7=e=>(t,r)=>{e&&b.postRender(()=>e(t,r))};class ie extends rm{constructor(){super(...arguments),this.removePointerDownListener=f}onPointerDown(e){this.session=new rK(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rX(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:r7(e),onStart:r7(t),onMove:r,onEnd:(e,t)=>{delete this.session,i&&b.postRender(()=>i(e,t))}}}mount(){this.removePointerDownListener=rS(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let{schedule:it,cancel:ir}=x(queueMicrotask,!1),ii=(0,u.createContext)(null);function is(e=!0){let t=(0,u.useContext)(ii);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:i,register:n}=t,s=(0,u.useId)();(0,u.useEffect)(()=>{if(e)return n(s)},[e]);let o=(0,u.useCallback)(()=>e&&i&&i(s),[s,i,e]);return!r&&i?[!1,o]:[!0]}let io=(0,u.createContext)({}),ia=(0,u.createContext)({}),il={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function iu(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ih={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!ef.test(e))return e;e=parseFloat(e)}let r=iu(e,t.target.x),i=iu(e,t.target.y);return`${r}% ${i}%`}},id={};class ic extends u.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:i}=this.props,{projection:n}=e;(function(e){for(let t in e)id[t]=e[t],K(t)&&(id[t].isCSSVariable=!0)})(im),n&&(t.group&&t.group.add(n),r&&r.register&&i&&r.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),il.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:i,isPresent:n}=this.props,{projection:s}=r;return s&&(s.isPresent=n,i||e.layoutDependency!==t||void 0===t||e.isPresent!==n?s.willUpdate():this.safeToRemove(),e.isPresent===n||(n?s.promote():s.relegate()||b.postRender(()=>{let e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),it.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function ip(e){let[t,r]=is(),i=(0,u.useContext)(io);return(0,l.jsx)(ic,{...e,layoutGroup:i,switchLayoutGroup:(0,u.useContext)(ia),isPresent:t,safeToRemove:r})}let im={borderRadius:{...ih,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ih,borderTopRightRadius:ih,borderBottomLeftRadius:ih,borderBottomRightRadius:ih,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let i=eC.parse(e);if(i.length>5)return e;let n=eC.createTransformer(e),s="number"!=typeof i[0]?1:0,o=r.x.scale*t.x,a=r.y.scale*t.y;i[0+s]/=o,i[1+s]/=a;let l=eR(o,a,.5);return"number"==typeof i[2+s]&&(i[2+s]/=l),"number"==typeof i[3+s]&&(i[3+s]/=l),n(i)}}};function ig(e){return t0(e)&&"ownerSVGElement"in e}let iy=(e,t)=>e.depth-t.depth;class iv{constructor(){this.children=[],this.isDirty=!1}add(e){k(this.children,e),this.isDirty=!0}remove(e){E(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(iy),this.isDirty=!1,this.children.forEach(e)}}function ix(e){return O(e)?e.get():e}let ib=["TopLeft","TopRight","BottomLeft","BottomRight"],iw=ib.length,iT=e=>"string"==typeof e?parseFloat(e):e,iS=e=>"number"==typeof e||ef.test(e);function iP(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let ij=ik(0,.5,ts),iA=ik(.5,.95,f);function ik(e,t,r){return i=>i<e?0:i>t?1:r(td(e,t,i))}function iE(e,t){e.min=t.min,e.max=t.max}function iC(e,t){iE(e.x,t.x),iE(e.y,t.y)}function iM(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function iD(e,t,r,i,n){return e-=t,e=i+1/r*(e-i),void 0!==n&&(e=i+1/n*(e-i)),e}function iR(e,t,[r,i,n],s,o){!function(e,t=0,r=1,i=.5,n,s=e,o=e){if(em.test(t)&&(t=parseFloat(t),t=eR(o.min,o.max,t/100)-o.min),"number"!=typeof t)return;let a=eR(s.min,s.max,i);e===s&&(a-=t),e.min=iD(e.min,t,r,a,n),e.max=iD(e.max,t,r,a,n)}(e,t[r],t[i],t[n],t.scale,s,o)}let iV=["x","scaleX","originX"],iN=["y","scaleY","originY"];function iL(e,t,r,i){iR(e.x,t,iV,r?r.x:void 0,i?i.x:void 0),iR(e.y,t,iN,r?r.y:void 0,i?i.y:void 0)}function iF(e){return 0===e.translate&&1===e.scale}function iO(e){return iF(e.x)&&iF(e.y)}function iI(e,t){return e.min===t.min&&e.max===t.max}function iB(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function i$(e,t){return iB(e.x,t.x)&&iB(e.y,t.y)}function iU(e){return rj(e.x)/rj(e.y)}function iz(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class iW{constructor(){this.members=[]}add(e){k(this.members,e),e.scheduleRender()}remove(e){if(E(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:i}=e.options;!1===i&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let i_={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},iH=["","X","Y","Z"],iY={visibility:"hidden"},iX=0;function iq(e,t,r,i){let{latestValues:n}=t;n[e]&&(r[e]=n[e],t.setStaticValue(e,0),i&&(i[e]=0))}function iG({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:i,resetTransform:n}){return class{constructor(e={},r=t?.()){this.id=iX++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,v.value&&(i_.nodes=i_.calculatedTargetDeltas=i_.calculatedProjections=0),this.nodes.forEach(iJ),this.nodes.forEach(i3),this.nodes.forEach(i9),this.nodes.forEach(iQ),v.addProjectionMetrics&&v.addProjectionMetrics(i_)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new iv)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new C),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=ig(t)&&!(ig(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:r,layout:i,visualElement:n}=this.options;if(n&&!n.current&&n.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(i||r)&&(this.isLayoutDirty=!0),e){let r;let i=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=D.now(),i=({timestamp:n})=>{let s=n-r;s>=t&&(w(i),e(s-t))};return b.setup(i,!0),()=>w(i)}(i,250),il.hasAnimatedSinceResize&&(il.hasAnimatedSinceResize=!1,this.nodes.forEach(i5))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&n&&(r||i)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:r,layout:i})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||nr,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=n.getProps(),l=!this.targetLayout||!i$(this.targetLayout,i),u=!t&&r;if(this.options.layoutRoot||this.resumeFrom||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...m(s,"layout"),onPlay:o,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,u)}else t||i5(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),w(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(i6),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:r}=t.options;if(!r)return;let i=r.props[$];if(window.MotionHasOptimisedAnimation(i,"transform")){let{layout:e,layoutId:r}=t.options;window.MotionCancelOptimisedAnimation(i,"transform",b,!(e||r))}let{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&e(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(i1);return}this.isUpdating||this.nodes.forEach(i2),this.isUpdating=!1,this.nodes.forEach(i4),this.nodes.forEach(iK),this.nodes.forEach(iZ),this.clearAllSnapshots();let e=D.now();T.delta=W(0,1e3/60,e-T.timestamp),T.timestamp=e,T.isProcessing=!0,S.update.process(T),S.preRender.process(T),S.render.process(T),T.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,it.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(i0),this.sharedNodes.forEach(i8)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,b.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){b.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||rj(this.snapshot.measuredBox.x)||rj(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=rN(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=i(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!n)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!iO(this.projectionDelta),r=this.getTransformTemplate(),i=r?r(this.latestValues,""):void 0,s=i!==this.prevTransformTemplateValue;e&&this.instance&&(t||rI(this.latestValues)||s)&&(n(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),i=this.removeElementScroll(r);return e&&(i=this.removeTransform(i)),ns((t=i).x),ns(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return rN();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(na))){let{scroll:e}=this.root;e&&(rW(t.x,e.offset.x),rW(t.y,e.offset.y))}return t}removeElementScroll(e){let t=rN();if(iC(t,e),this.scroll?.wasRoot)return t;for(let r=0;r<this.path.length;r++){let i=this.path[r],{scroll:n,options:s}=i;i!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&iC(t,e),rW(t.x,n.offset.x),rW(t.y,n.offset.y))}return t}applyTransform(e,t=!1){let r=rN();iC(r,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];!t&&i.options.layoutScroll&&i.scroll&&i!==i.root&&rH(r,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),rI(i.latestValues)&&rH(r,i.latestValues)}return rI(this.latestValues)&&rH(r,this.latestValues),r}removeTransform(e){let t=rN();iC(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!rI(r.latestValues))continue;rO(r.latestValues)&&r.updateSnapshot();let i=rN();iC(i,r.measurePageBox()),iL(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,i)}return rI(this.latestValues)&&iL(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==T.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let r=!!this.resumingFrom||this!==t;if(!(e||r&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:i,layoutId:n}=this.options;if(this.layout&&(i||n)){if(this.resolvedRelativeTargetAt=T.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rN(),this.relativeTargetOrigin=rN(),rM(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),iC(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=rN(),this.targetWithTransforms=rN()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,rE(s.x,o.x,a.x),rE(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iC(this.target,this.layout.layoutBox),rz(this.target,this.targetDelta)):iC(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rN(),this.relativeTargetOrigin=rN(),rM(this.relativeTargetOrigin,this.target,e.target),iC(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}v.value&&i_.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||rO(this.parent.latestValues)||rB(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,r=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(r=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===T.timestamp&&(r=!1),r)return;let{layout:i,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||n))return;iC(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;(function(e,t,r,i=!1){let n,s;let o=r.length;if(o){t.x=t.y=1;for(let a=0;a<o;a++){s=(n=r[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(i&&n.options.layoutScroll&&n.scroll&&n!==n.root&&rH(e,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,rz(e,s)),i&&rI(n.latestValues)&&rH(e,n.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}})(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=rN());let{target:a}=e;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(iM(this.prevProjectionDelta.x,this.projectionDelta.x),iM(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rk(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&iz(this.projectionDelta.x,this.prevProjectionDelta.x)&&iz(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),v.value&&i_.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rR(),this.projectionDelta=rR(),this.projectionDeltaWithTransform=rR()}setAnimationOrigin(e,t=!1){let r;let i=this.snapshot,n=i?i.latestValues:{},s={...this.latestValues},o=rR();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=rN(),l=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nt));this.animationProgress=0,this.mixTargetDelta=t=>{let i=t/1e3;if(i7(o.x,e.x,i),i7(o.y,e.y,i),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m;rM(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,ne(p.x,m.x,a.x,i),ne(p.y,m.y,a.y,i),r&&(u=this.relativeTarget,c=r,iI(u.x,c.x)&&iI(u.y,c.y))&&(this.isProjectionDirty=!1),r||(r=rN()),iC(r,this.relativeTarget)}l&&(this.animationValues=s,function(e,t,r,i,n,s){n?(e.opacity=eR(0,r.opacity??1,ij(i)),e.opacityExit=eR(t.opacity??1,0,iA(i))):s&&(e.opacity=eR(t.opacity??1,r.opacity??1,i));for(let n=0;n<iw;n++){let s=`border${ib[n]}Radius`,o=iP(t,s),a=iP(r,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||iS(o)===iS(a)?(e[s]=Math.max(eR(iT(o),iT(a),i),0),(em.test(a)||em.test(o))&&(e[s]+="%")):e[s]=a)}(t.rotate||r.rotate)&&(e.rotate=eR(t.rotate||0,r.rotate||0,i))}(s,n,this.latestValues,i,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(w(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=b.update(()=>{il.hasAnimatedSinceResize=!0,Y.layout++,this.motionValue||(this.motionValue=L(0)),this.currentAnimation=function(e,t,r){let i=O(e)?e:L(e);return i.start(rt("",i,t,r)),i.animation}(this.motionValue,[0,1e3],{...e,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{Y.layout--},onComplete:()=>{Y.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:i,latestValues:n}=e;if(t&&r&&i){if(this!==e&&this.layout&&i&&no(this.options.animationType,this.layout.layoutBox,i.layoutBox)){r=this.target||rN();let t=rj(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let i=rj(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+i}iC(t,r),rH(t,n),rk(this.projectionDeltaWithTransform,this.layoutCorrected,t,n)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new iW),this.sharedNodes.get(e).add(t);let r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let i=this.getStack();i&&i.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(t=!0),!t)return;let i={};r.z&&iq("z",e,i,this.animationValues);for(let t=0;t<iH.length;t++)iq(`rotate${iH[t]}`,e,i,this.animationValues),iq(`skew${iH[t]}`,e,i,this.animationValues);for(let t in e.render(),i)e.setStaticValue(t,i[t]),this.animationValues&&(this.animationValues[t]=i[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return iY;let t={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=ix(e?.pointerEvents)||"",t.transform=r?r(this.latestValues,""):"none",t;let i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=ix(e?.pointerEvents)||""),this.hasProjected&&!rI(this.latestValues)&&(t.transform=r?r({},""):"none",this.hasProjected=!1),t}let n=i.animationValues||i.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,r){let i="",n=e.x.translate/t.x,s=e.y.translate/t.y,o=r?.z||0;if((n||s||o)&&(i=`translate3d(${n}px, ${s}px, ${o}px) `),(1!==t.x||1!==t.y)&&(i+=`scale(${1/t.x}, ${1/t.y}) `),r){let{transformPerspective:e,rotate:t,rotateX:n,rotateY:s,skewX:o,skewY:a}=r;e&&(i=`perspective(${e}px) ${i}`),t&&(i+=`rotate(${t}deg) `),n&&(i+=`rotateX(${n}deg) `),s&&(i+=`rotateY(${s}deg) `),o&&(i+=`skewX(${o}deg) `),a&&(i+=`skewY(${a}deg) `)}let a=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==a||1!==l)&&(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),r&&(t.transform=r(n,t.transform));let{x:s,y:o}=this.projectionDelta;for(let e in t.transformOrigin=`${100*s.origin}% ${100*o.origin}% 0`,i.animationValues?t.opacity=i===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:t.opacity=i===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,id){if(void 0===n[e])continue;let{correct:r,applyTo:s,isCSSVariable:o}=id[e],a="none"===t.transform?n[e]:r(n[e],i);if(s){let e=s.length;for(let r=0;r<e;r++)t[s[r]]=a}else o?this.options.visualElement.renderState.vars[e]=a:t[e]=a}return this.options.layoutId&&(t.pointerEvents=i===this?ix(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(i1),this.root.sharedNodes.clear()}}}function iK(e){e.updateLayout()}function iZ(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:r,measuredBox:i}=e.layout,{animationType:n}=e.options,s=t.source!==e.layout.source;"size"===n?rL(e=>{let i=s?t.measuredBox[e]:t.layoutBox[e],n=rj(i);i.min=r[e].min,i.max=i.min+n}):no(n,t.layoutBox,r)&&rL(i=>{let n=s?t.measuredBox[i]:t.layoutBox[i],o=rj(r[i]);n.max=n.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[i].max=e.relativeTarget[i].min+o)});let o=rR();rk(o,r,t.layoutBox);let a=rR();s?rk(a,e.applyTransform(i,!0),t.measuredBox):rk(a,r,t.layoutBox);let l=!iO(o),u=!1;if(!e.resumeFrom){let i=e.getClosestProjectingParent();if(i&&!i.resumeFrom){let{snapshot:n,layout:s}=i;if(n&&s){let o=rN();rM(o,t.layoutBox,n.layoutBox);let a=rN();rM(a,r,s.layoutBox),i$(o,a)||(u=!0),i.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=o,e.relativeParent=i)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:t,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function iJ(e){v.value&&i_.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function iQ(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function i0(e){e.clearSnapshot()}function i1(e){e.clearMeasurements()}function i2(e){e.isLayoutDirty=!1}function i4(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function i5(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function i3(e){e.resolveTargetDelta()}function i9(e){e.calcProjection()}function i6(e){e.resetSkewAndRotation()}function i8(e){e.removeLeadSnapshot()}function i7(e,t,r){e.translate=eR(t.translate,0,r),e.scale=eR(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function ne(e,t,r,i){e.min=eR(t.min,r.min,i),e.max=eR(t.max,r.max,i)}function nt(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let nr={duration:.45,ease:[.4,0,.1,1]},ni=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),nn=ni("applewebkit/")&&!ni("chrome/")?Math.round:f;function ns(e){e.min=nn(e.min),e.max=nn(e.max)}function no(e,t,r){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(iU(t)-iU(r)))}function na(e){return e!==e.root&&e.scroll?.wasRoot}let nl=iG({attachResizeListener:(e,t)=>rx(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nu={current:void 0},nh=iG({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!nu.current){let e=new nl({});e.mount(window),e.setOptions({layoutScroll:!0}),nu.current=e}return nu.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function nd(e,t){let r=function(e,t,r){if(e instanceof EventTarget)return[e];if("string"==typeof e){let r=document;t&&(r=t.current);let i=(void 0)??r.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}(e),i=new AbortController;return[r,{passive:!0,...t,signal:i.signal},()=>i.abort()]}function nc(e){return!("touch"===e.pointerType||rv.x||rv.y)}function np(e,t,r){let{props:i}=e;e.animationState&&i.whileHover&&e.animationState.setActive("whileHover","Start"===r);let n=i["onHover"+r];n&&b.postRender(()=>n(t,rw(t)))}class nm extends rm{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[i,n,s]=nd(e,r),o=e=>{if(!nc(e))return;let{target:r}=e,i=t(r,e);if("function"!=typeof i||!r)return;let s=e=>{nc(e)&&(i(e),r.removeEventListener("pointerleave",s))};r.addEventListener("pointerleave",s,n)};return i.forEach(e=>{e.addEventListener("pointerenter",o,n)}),s}(e,(e,t)=>(np(this.node,t,"Start"),e=>np(this.node,e,"End"))))}unmount(){}}class nf extends rm{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=z(rx(this.node.current,"focus",()=>this.onFocus()),rx(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let ng=(e,t)=>!!t&&(e===t||ng(e,t.parentElement)),ny=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),nv=new WeakSet;function nx(e){return t=>{"Enter"===t.key&&e(t)}}function nb(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let nw=(e,t)=>{let r=e.currentTarget;if(!r)return;let i=nx(()=>{if(nv.has(r))return;nb(r,"down");let e=nx(()=>{nb(r,"up")});r.addEventListener("keyup",e,t),r.addEventListener("blur",()=>nb(r,"cancel"),t)});r.addEventListener("keydown",i,t),r.addEventListener("blur",()=>r.removeEventListener("keydown",i),t)};function nT(e){return rb(e)&&!(rv.x||rv.y)}function nS(e,t,r){let{props:i}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&i.whileTap&&e.animationState.setActive("whileTap","Start"===r);let n=i["onTap"+("End"===r?"":r)];n&&b.postRender(()=>n(t,rw(t)))}class nP extends rm{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[i,n,s]=nd(e,r),o=e=>{let i=e.currentTarget;if(!nT(e))return;nv.add(i);let s=t(i,e),o=(e,t)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),nv.has(i)&&nv.delete(i),nT(e)&&"function"==typeof s&&s(e,{success:t})},a=e=>{o(e,i===window||i===document||r.useGlobalTarget||ng(i,e.target))},l=e=>{o(e,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return i.forEach(e=>{(r.useGlobalTarget?window:e).addEventListener("pointerdown",o,n),t1(e)&&(e.addEventListener("focus",e=>nw(e,n)),ny.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),s}(e,(e,t)=>(nS(this.node,t,"Start"),(e,{success:t})=>nS(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nj=new WeakMap,nA=new WeakMap,nk=e=>{let t=nj.get(e.target);t&&t(e)},nE=e=>{e.forEach(nk)},nC={some:0,all:1};class nM extends rm{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:i="some",once:n}=e,s={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof i?i:nC[i]};return function(e,t,r){let i=function({root:e,...t}){let r=e||document;nA.has(r)||nA.set(r,{});let i=nA.get(r),n=JSON.stringify(t);return i[n]||(i[n]=new IntersectionObserver(nE,{root:e,...t})),i[n]}(t);return nj.set(e,r),i.observe(e),()=>{nj.delete(e),i.unobserve(e)}}(this.node.current,s,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,n&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:i}=this.node.getProps(),s=t?r:i;s&&s(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}let nD=(0,u.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),nR=(0,u.createContext)({});function nV(e){return h(e.animate)||rl.some(t=>ro(e[t]))}function nN(e){return!!(nV(e)||e.variants)}function nL(e){return Array.isArray(e)?e.join(" "):e}let nF={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nO={};for(let e in nF)nO[e]={isEnabled:t=>nF[e].some(e=>!!t[e])};let nI=Symbol.for("motionComponentSymbol"),nB=u.useEffect;function n$(e,{layout:t,layoutId:r}){return j.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!id[e]||"opacity"===e)}let nU=(e,t)=>t&&"number"==typeof e?t.transform(e):e,nz={...ee,transform:Math.round},nW={borderWidth:ef,borderTopWidth:ef,borderRightWidth:ef,borderBottomWidth:ef,borderLeftWidth:ef,borderRadius:ef,radius:ef,borderTopLeftRadius:ef,borderTopRightRadius:ef,borderBottomRightRadius:ef,borderBottomLeftRadius:ef,width:ef,maxWidth:ef,height:ef,maxHeight:ef,top:ef,right:ef,bottom:ef,left:ef,padding:ef,paddingTop:ef,paddingRight:ef,paddingBottom:ef,paddingLeft:ef,margin:ef,marginTop:ef,marginRight:ef,marginBottom:ef,marginLeft:ef,backgroundPositionX:ef,backgroundPositionY:ef,rotate:ep,rotateX:ep,rotateY:ep,rotateZ:ep,scale:er,scaleX:er,scaleY:er,scaleZ:er,skew:ep,skewX:ep,skewY:ep,distance:ef,translateX:ef,translateY:ef,translateZ:ef,x:ef,y:ef,z:ef,perspective:ef,transformPerspective:ef,opacity:et,originX:ev,originY:ev,originZ:ef,zIndex:nz,fillOpacity:et,strokeOpacity:et,numOctaves:nz},n_={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nH=P.length;function nY(e,t,r){let{style:i,vars:n,transformOrigin:s}=e,o=!1,a=!1;for(let e in t){let r=t[e];if(j.has(e)){o=!0;continue}if(K(e)){n[e]=r;continue}{let t=nU(r,nW[e]);e.startsWith("origin")?(a=!0,s[e]=t):i[e]=t}}if(!t.transform&&(o||r?i.transform=function(e,t,r){let i="",n=!0;for(let s=0;s<nH;s++){let o=P[s],a=e[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===(o.startsWith("scale")?1:0):0===parseFloat(a))||r){let e=nU(a,nW[o]);if(!l){n=!1;let t=n_[o]||o;i+=`${t}(${e}) `}r&&(t[o]=e)}}return i=i.trim(),r?i=r(t,n?"":i):n&&(i="none"),i}(t,e.transform,r):i.transform&&(i.transform="none")),a){let{originX:e="50%",originY:t="50%",originZ:r=0}=s;i.transformOrigin=`${e} ${t} ${r}`}}let nX=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nq(e,t,r){for(let i in t)O(t[i])||n$(i,r)||(e[i]=t[i])}let nG={offset:"stroke-dashoffset",array:"stroke-dasharray"},nK={offset:"strokeDashoffset",array:"strokeDasharray"};function nZ(e,{attrX:t,attrY:r,attrScale:i,pathLength:n,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(nY(e,a,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:c}=e;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==r&&(d.y=r),void 0!==i&&(d.scale=i),void 0!==n&&function(e,t,r=1,i=0,n=!0){e.pathLength=1;let s=n?nG:nK;e[s.offset]=ef.transform(-i);let o=ef.transform(t),a=ef.transform(r);e[s.array]=`${o} ${a}`}(d,n,s,o,!1)}let nJ=()=>({...nX(),attrs:{}}),nQ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),n0=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function n1(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||n0.has(e)}let n2=e=>!n1(e);try{!function(e){e&&(n2=t=>t.startsWith("on")?!n1(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let n4=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function n5(e){if("string"!=typeof e||e.includes("-"));else if(n4.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}function n3(e){let t=(0,u.useRef)(null);return null===t.current&&(t.current=e()),t.current}let n9=e=>(t,r)=>{let i=(0,u.useContext)(nR),n=(0,u.useContext)(ii),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},r,i,n){return{latestValues:function(e,t,r,i){let n={},s=i(e,{});for(let e in s)n[e]=ix(s[e]);let{initial:o,animate:a}=e,l=nV(e),u=nN(e);t&&u&&!l&&!1!==e.inherit&&(void 0===o&&(o=t.initial),void 0===a&&(a=t.animate));let d=!!r&&!1===r.initial,p=(d=d||!1===o)?a:o;if(p&&"boolean"!=typeof p&&!h(p)){let t=Array.isArray(p)?p:[p];for(let r=0;r<t.length;r++){let i=c(e,t[r]);if(i){let{transitionEnd:e,transition:t,...r}=i;for(let e in r){let t=r[e];if(Array.isArray(t)){let e=d?t.length-1:0;t=t[e]}null!==t&&(n[e]=t)}for(let t in e)n[t]=e[t]}}}return n}(r,i,n,e),renderState:t()}})(e,t,i,n);return r?s():n3(s)};function n6(e,t,r){let{style:i}=e,n={};for(let s in i)(O(i[s])||t.style&&O(t.style[s])||n$(s,e)||r?.getValue(s)?.liveStyle!==void 0)&&(n[s]=i[s]);return n}let n8={useVisualState:n9({scrapeMotionValuesFromProps:n6,createRenderState:nX})};function n7(e,t,r){let i=n6(e,t,r);for(let r in e)(O(e[r])||O(t[r]))&&(i[-1!==P.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return i}let se={useVisualState:n9({scrapeMotionValuesFromProps:n7,createRenderState:nJ})},st=e=>t=>t.test(e),sr=[ee,ef,em,ep,ey,eg,{test:e=>"auto"===e,parse:e=>e}],si=e=>sr.find(st(e)),sn=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),ss=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,so=e=>/^0[^.\s]+$/u.test(e),sa=new Set(["brightness","contrast","saturate","opacity"]);function sl(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[i]=r.match(en)||[];if(!i)return e;let n=r.replace(i,""),s=sa.has(t)?1:0;return i!==r&&(s*=100),t+"("+s+n+")"}let su=/\b([a-z-]*)\(.*?\)/gu,sh={...eC,getAnimatableNone:e=>{let t=e.match(su);return t?t.map(sl).join(" "):e}},sd={...nW,color:eb,backgroundColor:eb,outlineColor:eb,fill:eb,stroke:eb,borderColor:eb,borderTopColor:eb,borderRightColor:eb,borderBottomColor:eb,borderLeftColor:eb,filter:sh,WebkitFilter:sh},sc=e=>sd[e];function sp(e,t){let r=sc(e);return r!==sh&&(r=eC),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let sm=new Set(["auto","none","0"]);class sf extends tU{constructor(e,t,r,i,n){super(e,t,r,i,n,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:r}=this;if(!t||!t.current)return;super.readKeyframes();for(let r=0;r<e.length;r++){let i=e[r];if("string"==typeof i&&J(i=i.trim())){let n=function e(t,r,i=1){q(i<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[n,s]=function(e){let t=ss.exec(e);if(!t)return[,];let[,r,i,n]=t;return[`--${r??i}`,n]}(t);if(!n)return;let o=window.getComputedStyle(r).getPropertyValue(n);if(o){let e=o.trim();return sn(e)?parseFloat(e):e}return J(s)?e(s,r,i+1):s}(i,t.current);void 0!==n&&(e[r]=n),r===e.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!A.has(r)||2!==e.length)return;let[i,n]=e,s=si(i),o=si(n);if(s!==o){if(tD(s)&&tD(o))for(let t=0;t<e.length;t++){let r=e[t];"string"==typeof r&&(e[t]=parseFloat(r))}else tN[r]&&(this.needsMeasurement=!0)}}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,r=[];for(let t=0;t<e.length;t++){var i;(null===e[t]||("number"==typeof(i=e[t])?0===i:null===i||"none"===i||"0"===i||so(i)))&&r.push(t)}r.length&&function(e,t,r){let i,n=0;for(;n<e.length&&!i;){let t=e[n];"string"==typeof t&&!sm.has(t)&&ej(t).values.length&&(i=e[n]),n++}if(i&&r)for(let n of t)e[n]=sp(r,i)}(e,r,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:r}=this;if(!e||!e.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tN[r](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let i=t[t.length-1];void 0!==i&&e.getValue(r,i).jump(i,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:r}=this;if(!e||!e.current)return;let i=e.getValue(t);i&&i.jump(this.measuredOrigin,!1);let n=r.length-1,s=r[n];r[n]=tN[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,r])=>{e.getValue(t).set(r)}),this.resolveNoneKeyframes()}}let sg=[...sr,eb,eC],sy=e=>sg.find(st(e)),sv={current:null},sx={current:!1},sb=new WeakMap,sw=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sT{scrapeMotionValuesFromProps(e,t,r){return{}}constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:i,blockInitialAnimation:n,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tU,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=D.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,b.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=nV(t),this.isVariantNode=nN(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in h){let t=h[e];void 0!==a[e]&&O(t)&&t.set(a[e],!1)}}mount(e){this.current=e,sb.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),sx.current||(sx.current=!0),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sv.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),w(this.notifyUpdate),w(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let r;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let i=j.has(e);i&&this.onBindTransform&&this.onBindTransform();let n=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&b.preRender(this.notifyUpdate),i&&this.projection&&(this.projection.isTransformDirty=!0)}),s=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{n(),s(),r&&r(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in nO){let t=nO[e];if(!t)continue;let{isEnabled:r,Feature:i}=t;if(!this.features[e]&&i&&r(this.props)&&(this.features[e]=new i(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):rN()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<sw.length;t++){let r=sw[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let i=e["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=function(e,t,r){for(let i in t){let n=t[i],s=r[i];if(O(n))e.addValue(i,n);else if(O(s))e.addValue(i,L(n,{owner:e}));else if(s!==n){if(e.hasValue(i)){let t=e.getValue(i);!0===t.liveStyle?t.jump(n):t.hasAnimated||t.set(n)}else{let t=e.getStaticValue(i);e.addValue(i,L(void 0!==t?t:n,{owner:e}))}}}for(let i in r)void 0===t[i]&&e.removeValue(i);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let r=this.values.get(e);t!==r&&(r&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=L(null===t?void 0:t,{owner:this}),this.addValue(e,r)),r}readValue(e,t){let r=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=r&&("string"==typeof r&&(sn(r)||so(r))?r=parseFloat(r):!sy(r)&&eC.test(t)&&(r=sp(e,t)),this.setBaseTarget(e,O(r)?r.get():r)),O(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t;let{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let i=c(this.props,r,this.presenceContext?.custom);i&&(t=i[e])}if(r&&void 0!==t)return t;let i=this.getBaseTargetFromProps(this.props,e);return void 0===i||O(i)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new C),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class sS extends sT{constructor(){super(...arguments),this.KeyframeResolver=sf}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;O(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function sP(e,{style:t,vars:r},i,n){for(let s in Object.assign(e.style,t,n&&n.getProjectionStyles(i)),r)e.style.setProperty(s,r[s])}class sj extends sS{constructor(){super(...arguments),this.type="html",this.renderInstance=sP}readValueFromInstance(e,t){if(j.has(t))return this.projection?.isProjecting?tk(t):tC(e,t);{let r=window.getComputedStyle(e),i=(K(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof i?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:t}){return rY(e,t)}build(e,t,r){nY(e,t,r.transformTemplate)}scrapeMotionValuesFromProps(e,t,r){return n6(e,t,r)}}let sA=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sk extends sS{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=rN}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(j.has(t)){let e=sc(t);return e&&e.default||0}return t=sA.has(t)?t:B(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,r){return n7(e,t,r)}build(e,t,r){nZ(e,t,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(e,t,r,i){!function(e,t,r,i){for(let r in sP(e,t,void 0,i),t.attrs)e.setAttribute(sA.has(r)?r:B(r),t.attrs[r])}(e,t,0,i)}mount(e){this.isSVGTag=nQ(e.tagName),super.mount(e)}}let sE=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(r,i)=>"create"===i?e:(t.has(i)||t.set(i,e(i)),t.get(i))})}((n={animation:{Feature:rf},exit:{Feature:ry},inView:{Feature:nM},tap:{Feature:nP},focus:{Feature:nf},hover:{Feature:nm},pan:{Feature:ie},drag:{Feature:r8,ProjectionNode:nh,MeasureLayout:ip},layout:{ProjectionNode:nh,MeasureLayout:ip}},s=(e,t)=>n5(e)?new sk(t):new sj(t,{allowProjection:e!==u.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:i,Component:n}){function s(e,t){var s;let o;let a={...(0,u.useContext)(nD),...e,layoutId:function({layoutId:e}){let t=(0,u.useContext)(io).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:h}=a,d=function(e){let{initial:t,animate:r}=function(e,t){if(nV(e)){let{initial:t,animate:r}=e;return{initial:!1===t||ro(t)?t:void 0,animate:ro(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,u.useContext)(nR));return(0,u.useMemo)(()=>({initial:t,animate:r}),[nL(t),nL(r)])}(e),c=i(e,h);return(0,l.jsxs)(nR.Provider,{value:d,children:[o&&d.visualElement?(0,l.jsx)(o,{visualElement:d.visualElement,...a}):null,r(n,e,(s=d.visualElement,(0,u.useCallback)(e=>{e&&c.onMount&&c.onMount(e),s&&(e?s.mount(e):s.unmount()),t&&("function"==typeof t?t(e):rq(t)&&(t.current=e))},[s])),c,h,d.visualElement)]})}e&&function(e){for(let t in e)nO[t]={...nO[t],...e[t]}}(e),s.displayName=`motion.${"string"==typeof n?n:`create(${n.displayName??n.name??""})`}`;let o=(0,u.forwardRef)(s);return o[nI]=n,o}({...n5(e)?se:n8,preloadedFeatures:n,useRender:function(e=!1){return(t,r,i,{latestValues:n},s)=>{let o=(n5(t)?function(e,t,r,i){let n=(0,u.useMemo)(()=>{let r=nJ();return nZ(r,t,nQ(i),e.transformTemplate,e.style),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};nq(t,e.style,e),n.style={...t,...n.style}}return n}:function(e,t){let r={},i=function(e,t){let r=e.style||{},i={};return nq(i,r,e),Object.assign(i,function({transformTemplate:e},t){return(0,u.useMemo)(()=>{let r=nX();return nY(r,t,e),Object.assign({},r.vars,r.style)},[t])}(e,t)),i}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r})(r,n,s,t),a=function(e,t,r){let i={};for(let n in e)("values"!==n||"object"!=typeof e.values)&&(n2(n)||!0===r&&n1(n)||!t&&!n1(n)||e.draggable&&n.startsWith("onDrag"))&&(i[n]=e[n]);return i}(r,"string"==typeof t,e),l=t!==u.Fragment?{...a,...o,ref:i}:{},{children:h}=r,d=(0,u.useMemo)(()=>O(h)?h.get():h,[h]);return(0,u.createElement)(t,{...l,children:d})}}(t),createVisualElement:s,Component:e})}));class sC extends u.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=t1(e)&&e.offsetWidth||0,i=this.props.sizeRef.current;i.height=t.offsetHeight||0,i.width=t.offsetWidth||0,i.top=t.offsetTop,i.left=t.offsetLeft,i.right=r-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function sM({children:e,isPresent:t,anchorX:r}){let i=(0,u.useId)(),n=(0,u.useRef)(null),s=(0,u.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:o}=(0,u.useContext)(nD);return(0,u.useInsertionEffect)(()=>{let{width:e,height:a,top:l,left:u,right:h}=s.current;if(t||!n.current||!e||!a)return;let d="left"===r?`left: ${u}`:`right: ${h}`;n.current.dataset.motionPopId=i;let c=document.createElement("style");return o&&(c.nonce=o),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${a}px !important;
            ${d}px !important;
            top: ${l}px !important;
          }
        `),()=>{document.head.contains(c)&&document.head.removeChild(c)}},[t]),(0,l.jsx)(sC,{isPresent:t,childRef:n,sizeRef:s,children:u.cloneElement(e,{ref:n})})}let sD=({children:e,initial:t,isPresent:r,onExitComplete:i,custom:n,presenceAffectsLayout:s,mode:o,anchorX:a})=>{let h=n3(sR),d=(0,u.useId)(),c=!0,p=(0,u.useMemo)(()=>(c=!1,{id:d,initial:t,isPresent:r,custom:n,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;i&&i()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[r,h,i]);return s&&c&&(p={...p}),(0,u.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[r]),u.useEffect(()=>{r||h.size||!i||i()},[r]),"popLayout"===o&&(e=(0,l.jsx)(sM,{isPresent:r,anchorX:a,children:e})),(0,l.jsx)(ii.Provider,{value:p,children:e})};function sR(){return new Map}let sV=e=>e.key||"";function sN(e){let t=[];return u.Children.forEach(e,e=>{(0,u.isValidElement)(e)&&t.push(e)}),t}let sL=({children:e,custom:t,initial:r=!0,onExitComplete:i,presenceAffectsLayout:n=!0,mode:s="sync",propagate:o=!1,anchorX:a="left"})=>{let[h,d]=is(o),c=(0,u.useMemo)(()=>sN(e),[e]),p=o&&!h?[]:c.map(sV),m=(0,u.useRef)(!0),f=(0,u.useRef)(c),g=n3(()=>new Map),[y,v]=(0,u.useState)(c),[x,b]=(0,u.useState)(c);nB(()=>{m.current=!1,f.current=c;for(let e=0;e<x.length;e++){let t=sV(x[e]);p.includes(t)?g.delete(t):!0!==g.get(t)&&g.set(t,!1)}},[x,p.length,p.join("-")]);let w=[];if(c!==y){let e=[...c];for(let t=0;t<x.length;t++){let r=x[t],i=sV(r);p.includes(i)||(e.splice(t,0,r),w.push(r))}return"wait"===s&&w.length&&(e=w),b(sN(e)),v(c),null}let{forceRender:T}=(0,u.useContext)(io);return(0,l.jsx)(l.Fragment,{children:x.map(e=>{let u=sV(e),y=(!o||!!h)&&(c===x||p.includes(u));return(0,l.jsx)(sD,{isPresent:y,initial:(!m.current||!!r)&&void 0,custom:t,presenceAffectsLayout:n,mode:s,onExitComplete:y?void 0:()=>{if(!g.has(u))return;g.set(u,!0);let e=!0;g.forEach(t=>{t||(e=!1)}),e&&(T?.(),b(f.current),o&&d?.(),i&&i())},anchorX:a,children:e},u)})})};function sF(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function sO(...e){return t=>{let r=!1,i=e.map(e=>{let i=sF(e,t);return r||"function"!=typeof i||(r=!0),i});if(r)return()=>{for(let t=0;t<i.length;t++){let r=i[t];"function"==typeof r?r():sF(e[t],null)}}}}function sI(...e){return u.useCallback(sO(...e),e)}function sB(e){let t=function(e){let t=u.forwardRef((e,t)=>{let{children:r,...i}=e;if(u.isValidElement(r)){let e,n;let s=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,o=function(e,t){let r={...t};for(let i in t){let n=e[i],s=t[i];/^on[A-Z]/.test(i)?n&&s?r[i]=(...e)=>{let t=s(...e);return n(...e),t}:n&&(r[i]=n):"style"===i?r[i]={...n,...s}:"className"===i&&(r[i]=[n,s].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==u.Fragment&&(o.ref=t?sO(t,s):s),u.cloneElement(r,o)}return u.Children.count(r)>1?u.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=u.forwardRef((e,r)=>{let{children:i,...n}=e,s=u.Children.toArray(i),o=s.find(sz);if(o){let e=o.props.children,i=s.map(t=>t!==o?t:u.Children.count(e)>1?u.Children.only(null):u.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...n,ref:r,children:u.isValidElement(e)?u.cloneElement(e,void 0,i):null})}return(0,l.jsx)(t,{...n,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}var s$=sB("Slot"),sU=Symbol("radix.slottable");function sz(e){return u.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===sU}function sW(){for(var e,t,r=0,i="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,i,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t){if(Array.isArray(t)){var s=t.length;for(r=0;r<s;r++)t[r]&&(i=e(t[r]))&&(n&&(n+=" "),n+=i)}else for(i in t)t[i]&&(n&&(n+=" "),n+=i)}return n}(e))&&(i&&(i+=" "),i+=t);return i}let s_=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,sH=e=>{let t=sG(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:i}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),sY(r,t)||sq(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&i[e]?[...n,...i[e]]:n}}},sY=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],i=t.nextPart.get(r),n=i?sY(e.slice(1),i):void 0;if(n)return n;if(0===t.validators.length)return;let s=e.join("-");return t.validators.find(({validator:e})=>e(s))?.classGroupId},sX=/^\[(.+)\]$/,sq=e=>{if(sX.test(e)){let t=sX.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},sG=e=>{let{theme:t,prefix:r}=e,i={nextPart:new Map,validators:[]};return sQ(Object.entries(e.classGroups),r).forEach(([e,r])=>{sK(r,i,e,t)}),i},sK=(e,t,r,i)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:sZ(t,e)).classGroupId=r;return}if("function"==typeof e){if(sJ(e)){sK(e(i),t,r,i);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,n])=>{sK(n,sZ(t,e),r,i)})})},sZ=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},sJ=e=>e.isThemeGetter,sQ=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,s0=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,i=new Map,n=(n,s)=>{r.set(n,s),++t>e&&(t=0,i=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=i.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}},s1=e=>{let{separator:t,experimentalParseClassName:r}=e,i=1===t.length,n=t[0],s=t.length,o=e=>{let r;let o=[],a=0,l=0;for(let u=0;u<e.length;u++){let h=e[u];if(0===a){if(h===n&&(i||e.slice(u,u+s)===t)){o.push(e.slice(l,u)),l=u+s;continue}if("/"===h){r=u;continue}}"["===h?a++:"]"===h&&a--}let u=0===o.length?e:e.substring(l),h=u.startsWith("!"),d=h?u.substring(1):u;return{modifiers:o,hasImportantModifier:h,baseClassName:d,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};return r?e=>r({className:e,parseClassName:o}):o},s2=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},s4=e=>({cache:s0(e.cacheSize),parseClassName:s1(e),...sH(e)}),s5=/\s+/,s3=(e,t)=>{let{parseClassName:r,getClassGroupId:i,getConflictingClassGroupIds:n}=t,s=[],o=e.trim().split(s5),a="";for(let e=o.length-1;e>=0;e-=1){let t=o[e],{modifiers:l,hasImportantModifier:u,baseClassName:h,maybePostfixModifierPosition:d}=r(t),c=!!d,p=i(c?h.substring(0,d):h);if(!p){if(!c||!(p=i(h))){a=t+(a.length>0?" "+a:a);continue}c=!1}let m=s2(l).join(":"),f=u?m+"!":m,g=f+p;if(s.includes(g))continue;s.push(g);let y=n(p,c);for(let e=0;e<y.length;++e){let t=y[e];s.push(f+t)}a=t+(a.length>0?" "+a:a)}return a};function s9(){let e,t,r=0,i="";for(;r<arguments.length;)(e=arguments[r++])&&(t=s6(e))&&(i&&(i+=" "),i+=t);return i}let s6=e=>{let t;if("string"==typeof e)return e;let r="";for(let i=0;i<e.length;i++)e[i]&&(t=s6(e[i]))&&(r&&(r+=" "),r+=t);return r},s8=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},s7=/^\[(?:([a-z-]+):)?(.+)\]$/i,oe=/^\d+\/\d+$/,ot=new Set(["px","full","screen"]),or=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,oi=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,on=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,os=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,oo=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,oa=e=>ou(e)||ot.has(e)||oe.test(e),ol=e=>oT(e,"length",oS),ou=e=>!!e&&!Number.isNaN(Number(e)),oh=e=>oT(e,"number",ou),od=e=>!!e&&Number.isInteger(Number(e)),oc=e=>e.endsWith("%")&&ou(e.slice(0,-1)),op=e=>s7.test(e),om=e=>or.test(e),of=new Set(["length","size","percentage"]),og=e=>oT(e,of,oP),oy=e=>oT(e,"position",oP),ov=new Set(["image","url"]),ox=e=>oT(e,ov,oA),ob=e=>oT(e,"",oj),ow=()=>!0,oT=(e,t,r)=>{let i=s7.exec(e);return!!i&&(i[1]?"string"==typeof t?i[1]===t:t.has(i[1]):r(i[2]))},oS=e=>oi.test(e)&&!on.test(e),oP=()=>!1,oj=e=>os.test(e),oA=e=>oo.test(e);Symbol.toStringTag;let ok=function(e){let t,r,i;let n=function(o){return r=(t=s4([].reduce((e,t)=>t(e),e()))).cache.get,i=t.cache.set,n=s,s(o)};function s(e){let n=r(e);if(n)return n;let s=s3(e,t);return i(e,s),s}return function(){return n(s9.apply(null,arguments))}}(()=>{let e=s8("colors"),t=s8("spacing"),r=s8("blur"),i=s8("brightness"),n=s8("borderColor"),s=s8("borderRadius"),o=s8("borderSpacing"),a=s8("borderWidth"),l=s8("contrast"),u=s8("grayscale"),h=s8("hueRotate"),d=s8("invert"),c=s8("gap"),p=s8("gradientColorStops"),m=s8("gradientColorStopPositions"),f=s8("inset"),g=s8("margin"),y=s8("opacity"),v=s8("padding"),x=s8("saturate"),b=s8("scale"),w=s8("sepia"),T=s8("skew"),S=s8("space"),P=s8("translate"),j=()=>["auto","contain","none"],A=()=>["auto","hidden","clip","visible","scroll"],k=()=>["auto",op,t],E=()=>[op,t],C=()=>["",oa,ol],M=()=>["auto",ou,op],D=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],R=()=>["solid","dashed","dotted","double","none"],V=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],N=()=>["start","end","center","between","around","evenly","stretch"],L=()=>["","0",op],F=()=>["auto","avoid","all","avoid-page","page","left","right","column"],O=()=>[ou,op];return{cacheSize:500,separator:":",theme:{colors:[ow],spacing:[oa,ol],blur:["none","",om,op],brightness:O(),borderColor:[e],borderRadius:["none","","full",om,op],borderSpacing:E(),borderWidth:C(),contrast:O(),grayscale:L(),hueRotate:O(),invert:L(),gap:E(),gradientColorStops:[e],gradientColorStopPositions:[oc,ol],inset:k(),margin:k(),opacity:O(),padding:E(),saturate:O(),scale:O(),sepia:L(),skew:O(),space:E(),translate:E()},classGroups:{aspect:[{aspect:["auto","square","video",op]}],container:["container"],columns:[{columns:[om]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...D(),op]}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",od,op]}],basis:[{basis:k()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",op]}],grow:[{grow:L()}],shrink:[{shrink:L()}],order:[{order:["first","last","none",od,op]}],"grid-cols":[{"grid-cols":[ow]}],"col-start-end":[{col:["auto",{span:["full",od,op]},op]}],"col-start":[{"col-start":M()}],"col-end":[{"col-end":M()}],"grid-rows":[{"grid-rows":[ow]}],"row-start-end":[{row:["auto",{span:[od,op]},op]}],"row-start":[{"row-start":M()}],"row-end":[{"row-end":M()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",op]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",op]}],gap:[{gap:[c]}],"gap-x":[{"gap-x":[c]}],"gap-y":[{"gap-y":[c]}],"justify-content":[{justify:["normal",...N()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...N(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...N(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[S]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[S]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",op,t]}],"min-w":[{"min-w":[op,t,"min","max","fit"]}],"max-w":[{"max-w":[op,t,"none","full","min","max","fit","prose",{screen:[om]},om]}],h:[{h:[op,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[op,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[op,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[op,t,"auto","min","max","fit"]}],"font-size":[{text:["base",om,ol]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",oh]}],"font-family":[{font:[ow]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",op]}],"line-clamp":[{"line-clamp":["none",ou,oh]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",oa,op]}],"list-image":[{"list-image":["none",op]}],"list-style-type":[{list:["none","disc","decimal",op]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...R(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",oa,ol]}],"underline-offset":[{"underline-offset":["auto",oa,op]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:E()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",op]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",op]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...D(),oy]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",og]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},ox]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[...R(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:R()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...R()]}],"outline-offset":[{"outline-offset":[oa,op]}],"outline-w":[{outline:[oa,ol]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:C()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[oa,ol]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",om,ob]}],"shadow-color":[{shadow:[ow]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":[...V(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":V()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[i]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",om,op]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[h]}],invert:[{invert:[d]}],saturate:[{saturate:[x]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[i]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[h]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[x]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",op]}],duration:[{duration:O()}],ease:[{ease:["linear","in","out","in-out",op]}],delay:[{delay:O()}],animate:[{animate:["none","spin","ping","pulse","bounce",op]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[b]}],"scale-x":[{"scale-x":[b]}],"scale-y":[{"scale-y":[b]}],rotate:[{rotate:[od,op]}],"translate-x":[{"translate-x":[P]}],"translate-y":[{"translate-y":[P]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",op]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",op]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":E()}],"scroll-mx":[{"scroll-mx":E()}],"scroll-my":[{"scroll-my":E()}],"scroll-ms":[{"scroll-ms":E()}],"scroll-me":[{"scroll-me":E()}],"scroll-mt":[{"scroll-mt":E()}],"scroll-mr":[{"scroll-mr":E()}],"scroll-mb":[{"scroll-mb":E()}],"scroll-ml":[{"scroll-ml":E()}],"scroll-p":[{"scroll-p":E()}],"scroll-px":[{"scroll-px":E()}],"scroll-py":[{"scroll-py":E()}],"scroll-ps":[{"scroll-ps":E()}],"scroll-pe":[{"scroll-pe":E()}],"scroll-pt":[{"scroll-pt":E()}],"scroll-pr":[{"scroll-pr":E()}],"scroll-pb":[{"scroll-pb":E()}],"scroll-pl":[{"scroll-pl":E()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",op]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[oa,ol,oh]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function oE(...e){return ok(sW(e))}let oC=(o="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",a={variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}},e=>{var t;if((null==a?void 0:a.variants)==null)return sW(o,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:r,defaultVariants:i}=a,n=Object.keys(r).map(t=>{let n=null==e?void 0:e[t],s=null==i?void 0:i[t];if(null===n)return null;let o=s_(n)||s_(s);return r[t][o]}),s=e&&Object.entries(e).reduce((e,t)=>{let[r,i]=t;return void 0===i||(e[r]=i),e},{});return sW(o,n,null==a?void 0:null===(t=a.compoundVariants)||void 0===t?void 0:t.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...s}[t]):({...i,...s})[t]===r})?[...e,r,n]:e},[]),null==e?void 0:e.class,null==e?void 0:e.className)}),oM=u.forwardRef(({className:e,variant:t,size:r,asChild:i=!1,...n},s)=>{let o=i?s$:"button";return l.jsx(o,{className:oE(oC({variant:t,size:r,className:e})),ref:s,...n})});oM.displayName="Button";let oD=u.forwardRef(({className:e,type:t,...r},i)=>l.jsx("input",{type:t,className:oE("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:i,...r}));function oR(e,t=[]){let r=[],i=()=>{let t=r.map(e=>u.createContext(e));return function(r){let i=r?.[e]||t;return u.useMemo(()=>({[`__scope${e}`]:{...r,[e]:i}}),[r,i])}};return i.scopeName=e,[function(t,i){let n=u.createContext(i),s=r.length;r=[...r,i];let o=t=>{let{scope:r,children:i,...o}=t,a=r?.[e]?.[s]||n,h=u.useMemo(()=>o,Object.values(o));return(0,l.jsx)(a.Provider,{value:h,children:i})};return o.displayName=t+"Provider",[o,function(r,o){let a=o?.[e]?.[s]||n,l=u.useContext(a);if(l)return l;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=r.reduce((t,{useScope:r,scopeName:i})=>{let n=r(e)[`__scope${i}`];return{...t,...n}},{});return u.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}(i,...t)]}function oV(e){let t=u.useRef(e);return u.useEffect(()=>{t.current=e}),u.useMemo(()=>(...e)=>t.current?.(...e),[])}oD.displayName="Input";var oN=globalThis?.document?u.useLayoutEffect:()=>{};r(1202);var oL=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=sB(`Primitive.${t}`),i=u.forwardRef((e,i)=>{let{asChild:n,...s}=e,o=n?r:t;return(0,l.jsx)(o,{...s,ref:i})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),oF=r(8145);function oO(){return()=>{}}var oI="Avatar",[oB,o$]=oR(oI),[oU,oz]=oB(oI),oW=u.forwardRef((e,t)=>{let{__scopeAvatar:r,...i}=e,[n,s]=u.useState("idle");return(0,l.jsx)(oU,{scope:r,imageLoadingStatus:n,onImageLoadingStatusChange:s,children:(0,l.jsx)(oL.span,{...i,ref:t})})});oW.displayName=oI;var o_="AvatarImage",oH=u.forwardRef((e,t)=>{let{__scopeAvatar:r,src:i,onLoadingStatusChange:n=()=>{},...s}=e,o=oz(o_,r),a=function(e,{referrerPolicy:t,crossOrigin:r}){let i=(0,oF.useSyncExternalStore)(oO,()=>!0,()=>!1),n=u.useRef(null),s=i?(n.current||(n.current=new window.Image),n.current):null,[o,a]=u.useState(()=>oq(s,e));return oN(()=>{a(oq(s,e))},[s,e]),oN(()=>{let e=e=>()=>{a(e)};if(!s)return;let i=e("loaded"),n=e("error");return s.addEventListener("load",i),s.addEventListener("error",n),t&&(s.referrerPolicy=t),"string"==typeof r&&(s.crossOrigin=r),()=>{s.removeEventListener("load",i),s.removeEventListener("error",n)}},[s,r,t]),o}(i,s),h=oV(e=>{n(e),o.onImageLoadingStatusChange(e)});return oN(()=>{"idle"!==a&&h(a)},[a,h]),"loaded"===a?(0,l.jsx)(oL.img,{...s,ref:t,src:i}):null});oH.displayName=o_;var oY="AvatarFallback",oX=u.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:i,...n}=e,s=oz(oY,r),[o,a]=u.useState(void 0===i);return u.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>a(!0),i);return()=>window.clearTimeout(e)}},[i]),o&&"loaded"!==s.imageLoadingStatus?(0,l.jsx)(oL.span,{...n,ref:t}):null});function oq(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}oX.displayName=oY;let oG=u.forwardRef(({className:e,...t},r)=>l.jsx(oW,{ref:r,className:oE("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));oG.displayName=oW.displayName,u.forwardRef(({className:e,...t},r)=>l.jsx(oH,{ref:r,className:oE("aspect-square h-full w-full",e),...t})).displayName=oH.displayName;let oK=u.forwardRef(({className:e,...t},r)=>l.jsx(oX,{ref:r,className:oE("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));oK.displayName=oX.displayName;var oZ=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[i,n]=u.useState(),s=u.useRef(null),o=u.useRef(e),a=u.useRef("none"),[l,h]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},u.useReducer((e,t)=>r[e][t]??e,t));return u.useEffect(()=>{let e=oJ(s.current);a.current="mounted"===l?e:"none"},[l]),oN(()=>{let t=s.current,r=o.current;if(r!==e){let i=a.current,n=oJ(t);e?h("MOUNT"):"none"===n||t?.display==="none"?h("UNMOUNT"):r&&i!==n?h("ANIMATION_OUT"):h("UNMOUNT"),o.current=e}},[e,h]),oN(()=>{if(i){let e;let t=i.ownerDocument.defaultView??window,r=r=>{let n=oJ(s.current).includes(r.animationName);if(r.target===i&&n&&(h("ANIMATION_END"),!o.current)){let r=i.style.animationFillMode;i.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=r)})}},n=e=>{e.target===i&&(a.current=oJ(s.current))};return i.addEventListener("animationstart",n),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{t.clearTimeout(e),i.removeEventListener("animationstart",n),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}h("ANIMATION_END")},[i,h]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:u.useCallback(e=>{s.current=e?getComputedStyle(e):null,n(e)},[])}}(t),n="function"==typeof r?r({present:i.isPresent}):u.Children.only(r),s=sI(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n));return"function"==typeof r||i.isPresent?u.cloneElement(n,{ref:s}):null};function oJ(e){return e?.animationName||"none"}oZ.displayName="Presence";var oQ=u.createContext(void 0);function o0(e,t,{checkForDefaultPrevented:r=!0}={}){return function(i){if(e?.(i),!1===r||!i.defaultPrevented)return t?.(i)}}var o1="ScrollArea",[o2,o4]=oR(o1),[o5,o3]=o2(o1),o9=u.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:i="hover",dir:n,scrollHideDelay:s=600,...o}=e,[a,h]=u.useState(null),[d,c]=u.useState(null),[p,m]=u.useState(null),[f,g]=u.useState(null),[y,v]=u.useState(null),[x,b]=u.useState(0),[w,T]=u.useState(0),[S,P]=u.useState(!1),[j,A]=u.useState(!1),k=sI(t,e=>h(e)),E=function(e){let t=u.useContext(oQ);return e||t||"ltr"}(n);return(0,l.jsx)(o5,{scope:r,type:i,dir:E,scrollHideDelay:s,scrollArea:a,viewport:d,onViewportChange:c,content:p,onContentChange:m,scrollbarX:f,onScrollbarXChange:g,scrollbarXEnabled:S,onScrollbarXEnabledChange:P,scrollbarY:y,onScrollbarYChange:v,scrollbarYEnabled:j,onScrollbarYEnabledChange:A,onCornerWidthChange:b,onCornerHeightChange:T,children:(0,l.jsx)(oL.div,{dir:E,...o,ref:k,style:{position:"relative","--radix-scroll-area-corner-width":x+"px","--radix-scroll-area-corner-height":w+"px",...e.style}})})});o9.displayName=o1;var o6="ScrollAreaViewport",o8=u.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:i,nonce:n,...s}=e,o=o3(o6,r),a=sI(t,u.useRef(null),o.onViewportChange);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,l.jsx)(oL.div,{"data-radix-scroll-area-viewport":"",...s,ref:a,style:{overflowX:o.scrollbarXEnabled?"scroll":"hidden",overflowY:o.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,l.jsx)("div",{ref:o.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});o8.displayName=o6;var o7="ScrollAreaScrollbar",ae=u.forwardRef((e,t)=>{let{forceMount:r,...i}=e,n=o3(o7,e.__scopeScrollArea),{onScrollbarXEnabledChange:s,onScrollbarYEnabledChange:o}=n,a="horizontal"===e.orientation;return u.useEffect(()=>(a?s(!0):o(!0),()=>{a?s(!1):o(!1)}),[a,s,o]),"hover"===n.type?(0,l.jsx)(at,{...i,ref:t,forceMount:r}):"scroll"===n.type?(0,l.jsx)(ar,{...i,ref:t,forceMount:r}):"auto"===n.type?(0,l.jsx)(ai,{...i,ref:t,forceMount:r}):"always"===n.type?(0,l.jsx)(an,{...i,ref:t}):null});ae.displayName=o7;var at=u.forwardRef((e,t)=>{let{forceMount:r,...i}=e,n=o3(o7,e.__scopeScrollArea),[s,o]=u.useState(!1);return u.useEffect(()=>{let e=n.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),o(!0)},i=()=>{t=window.setTimeout(()=>o(!1),n.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",i),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",i)}}},[n.scrollArea,n.scrollHideDelay]),(0,l.jsx)(oZ,{present:r||s,children:(0,l.jsx)(ai,{"data-state":s?"visible":"hidden",...i,ref:t})})}),ar=u.forwardRef((e,t)=>{var r;let{forceMount:i,...n}=e,s=o3(o7,e.__scopeScrollArea),o="horizontal"===e.orientation,a=aT(()=>d("SCROLL_END"),100),[h,d]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},u.useReducer((e,t)=>r[e][t]??e,"hidden"));return u.useEffect(()=>{if("idle"===h){let e=window.setTimeout(()=>d("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[h,s.scrollHideDelay,d]),u.useEffect(()=>{let e=s.viewport,t=o?"scrollLeft":"scrollTop";if(e){let r=e[t],i=()=>{let i=e[t];r!==i&&(d("SCROLL"),a()),r=i};return e.addEventListener("scroll",i),()=>e.removeEventListener("scroll",i)}},[s.viewport,o,d,a]),(0,l.jsx)(oZ,{present:i||"hidden"!==h,children:(0,l.jsx)(an,{"data-state":"hidden"===h?"hidden":"visible",...n,ref:t,onPointerEnter:o0(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:o0(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),ai=u.forwardRef((e,t)=>{let r=o3(o7,e.__scopeScrollArea),{forceMount:i,...n}=e,[s,o]=u.useState(!1),a="horizontal"===e.orientation,h=aT(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;o(a?e:t)}},10);return aS(r.viewport,h),aS(r.content,h),(0,l.jsx)(oZ,{present:i||s,children:(0,l.jsx)(an,{"data-state":s?"visible":"hidden",...n,ref:t})})}),an=u.forwardRef((e,t)=>{let{orientation:r="vertical",...i}=e,n=o3(o7,e.__scopeScrollArea),s=u.useRef(null),o=u.useRef(0),[a,h]=u.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=ay(a.viewport,a.content),c={...i,sizes:a,onSizesChange:h,hasThumb:!!(d>0&&d<1),onThumbChange:e=>s.current=e,onThumbPointerUp:()=>o.current=0,onThumbPointerDown:e=>o.current=e};function p(e,t){return function(e,t,r,i="ltr"){let n=av(r),s=t||n/2,o=r.scrollbar.paddingStart+s,a=r.scrollbar.size-r.scrollbar.paddingEnd-(n-s),l=r.content-r.viewport;return ab([o,a],"ltr"===i?[0,l]:[-1*l,0])(e)}(e,o.current,a,t)}return"horizontal"===r?(0,l.jsx)(as,{...c,ref:t,onThumbPositionChange:()=>{if(n.viewport&&s.current){let e=ax(n.viewport.scrollLeft,a,n.dir);s.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{n.viewport&&(n.viewport.scrollLeft=e)},onDragScroll:e=>{n.viewport&&(n.viewport.scrollLeft=p(e,n.dir))}}):"vertical"===r?(0,l.jsx)(ao,{...c,ref:t,onThumbPositionChange:()=>{if(n.viewport&&s.current){let e=ax(n.viewport.scrollTop,a);s.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{n.viewport&&(n.viewport.scrollTop=e)},onDragScroll:e=>{n.viewport&&(n.viewport.scrollTop=p(e))}}):null}),as=u.forwardRef((e,t)=>{let{sizes:r,onSizesChange:i,...n}=e,s=o3(o7,e.__scopeScrollArea),[o,a]=u.useState(),h=u.useRef(null),d=sI(t,h,s.onScrollbarXChange);return u.useEffect(()=>{h.current&&a(getComputedStyle(h.current))},[h]),(0,l.jsx)(au,{"data-orientation":"horizontal",...n,ref:d,sizes:r,style:{bottom:0,left:"rtl"===s.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===s.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":av(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(s.viewport){let i=s.viewport.scrollLeft+t.deltaX;e.onWheelScroll(i),function(e,t){return e>0&&e<t}(i,r)&&t.preventDefault()}},onResize:()=>{h.current&&s.viewport&&o&&i({content:s.viewport.scrollWidth,viewport:s.viewport.offsetWidth,scrollbar:{size:h.current.clientWidth,paddingStart:ag(o.paddingLeft),paddingEnd:ag(o.paddingRight)}})}})}),ao=u.forwardRef((e,t)=>{let{sizes:r,onSizesChange:i,...n}=e,s=o3(o7,e.__scopeScrollArea),[o,a]=u.useState(),h=u.useRef(null),d=sI(t,h,s.onScrollbarYChange);return u.useEffect(()=>{h.current&&a(getComputedStyle(h.current))},[h]),(0,l.jsx)(au,{"data-orientation":"vertical",...n,ref:d,sizes:r,style:{top:0,right:"ltr"===s.dir?0:void 0,left:"rtl"===s.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":av(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(s.viewport){let i=s.viewport.scrollTop+t.deltaY;e.onWheelScroll(i),function(e,t){return e>0&&e<t}(i,r)&&t.preventDefault()}},onResize:()=>{h.current&&s.viewport&&o&&i({content:s.viewport.scrollHeight,viewport:s.viewport.offsetHeight,scrollbar:{size:h.current.clientHeight,paddingStart:ag(o.paddingTop),paddingEnd:ag(o.paddingBottom)}})}})}),[aa,al]=o2(o7),au=u.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:i,hasThumb:n,onThumbChange:s,onThumbPointerUp:o,onThumbPointerDown:a,onThumbPositionChange:h,onDragScroll:d,onWheelScroll:c,onResize:p,...m}=e,f=o3(o7,r),[g,y]=u.useState(null),v=sI(t,e=>y(e)),x=u.useRef(null),b=u.useRef(""),w=f.viewport,T=i.content-i.viewport,S=oV(c),P=oV(h),j=aT(p,10);function A(e){x.current&&d({x:e.clientX-x.current.left,y:e.clientY-x.current.top})}return u.useEffect(()=>{let e=e=>{let t=e.target;g?.contains(t)&&S(e,T)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[w,g,T,S]),u.useEffect(P,[i,P]),aS(g,j),aS(f.content,j),(0,l.jsx)(aa,{scope:r,scrollbar:g,hasThumb:n,onThumbChange:oV(s),onThumbPointerUp:oV(o),onThumbPositionChange:P,onThumbPointerDown:oV(a),children:(0,l.jsx)(oL.div,{...m,ref:v,style:{position:"absolute",...m.style},onPointerDown:o0(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),x.current=g.getBoundingClientRect(),b.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",f.viewport&&(f.viewport.style.scrollBehavior="auto"),A(e))}),onPointerMove:o0(e.onPointerMove,A),onPointerUp:o0(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=b.current,f.viewport&&(f.viewport.style.scrollBehavior=""),x.current=null})})})}),ah="ScrollAreaThumb",ad=u.forwardRef((e,t)=>{let{forceMount:r,...i}=e,n=al(ah,e.__scopeScrollArea);return(0,l.jsx)(oZ,{present:r||n.hasThumb,children:(0,l.jsx)(ac,{ref:t,...i})})}),ac=u.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:i,...n}=e,s=o3(ah,r),o=al(ah,r),{onThumbPositionChange:a}=o,h=sI(t,e=>o.onThumbChange(e)),d=u.useRef(void 0),c=aT(()=>{d.current&&(d.current(),d.current=void 0)},100);return u.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{if(c(),!d.current){let t=aw(e,a);d.current=t,a()}};return a(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,c,a]),(0,l.jsx)(oL.div,{"data-state":o.hasThumb?"visible":"hidden",...n,ref:h,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:o0(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,i=e.clientY-t.top;o.onThumbPointerDown({x:r,y:i})}),onPointerUp:o0(e.onPointerUp,o.onThumbPointerUp)})});ad.displayName=ah;var ap="ScrollAreaCorner",am=u.forwardRef((e,t)=>{let r=o3(ap,e.__scopeScrollArea),i=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&i?(0,l.jsx)(af,{...e,ref:t}):null});am.displayName=ap;var af=u.forwardRef((e,t)=>{let{__scopeScrollArea:r,...i}=e,n=o3(ap,r),[s,o]=u.useState(0),[a,h]=u.useState(0),d=!!(s&&a);return aS(n.scrollbarX,()=>{let e=n.scrollbarX?.offsetHeight||0;n.onCornerHeightChange(e),h(e)}),aS(n.scrollbarY,()=>{let e=n.scrollbarY?.offsetWidth||0;n.onCornerWidthChange(e),o(e)}),d?(0,l.jsx)(oL.div,{...i,ref:t,style:{width:s,height:a,position:"absolute",right:"ltr"===n.dir?0:void 0,left:"rtl"===n.dir?0:void 0,bottom:0,...e.style}}):null});function ag(e){return e?parseInt(e,10):0}function ay(e,t){let r=e/t;return isNaN(r)?0:r}function av(e){let t=ay(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function ax(e,t,r="ltr"){let i=av(t),n=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,s=t.scrollbar.size-n,o=t.content-t.viewport,a=function(e,[t,r]){return Math.min(r,Math.max(t,e))}(e,"ltr"===r?[0,o]:[-1*o,0]);return ab([0,o],[0,s-i])(a)}function ab(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let i=(t[1]-t[0])/(e[1]-e[0]);return t[0]+i*(r-e[0])}}var aw=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},i=0;return function n(){let s={left:e.scrollLeft,top:e.scrollTop},o=r.left!==s.left,a=r.top!==s.top;(o||a)&&t(),r=s,i=window.requestAnimationFrame(n)}(),()=>window.cancelAnimationFrame(i)};function aT(e,t){let r=oV(e),i=u.useRef(0);return u.useEffect(()=>()=>window.clearTimeout(i.current),[]),u.useCallback(()=>{window.clearTimeout(i.current),i.current=window.setTimeout(r,t)},[r,t])}function aS(e,t){let r=oV(t);oN(()=>{let t=0;if(e){let i=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return i.observe(e),()=>{window.cancelAnimationFrame(t),i.unobserve(e)}}},[e,r])}let aP=u.forwardRef(({className:e,children:t,...r},i)=>(0,l.jsxs)(o9,{ref:i,className:oE("relative overflow-hidden",e),...r,children:[l.jsx(o8,{className:"h-full w-full rounded-[inherit]",children:t}),l.jsx(aj,{}),l.jsx(am,{})]}));aP.displayName=o9.displayName;let aj=u.forwardRef(({className:e,orientation:t="vertical",...r},i)=>l.jsx(ae,{ref:i,orientation:t,className:oE("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...r,children:l.jsx(ad,{className:"relative flex-1 rounded-full bg-border"})}));aj.displayName=ae.displayName;/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var aA={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let ak=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),aE=(e,t)=>{let r=(0,u.forwardRef)(({color:r="currentColor",size:i=24,strokeWidth:n=2,absoluteStrokeWidth:s,className:o="",children:a,...l},h)=>(0,u.createElement)("svg",{ref:h,...aA,width:i,height:i,stroke:r,strokeWidth:s?24*Number(n)/Number(i):n,className:["lucide",`lucide-${ak(e)}`,o].join(" "),...l},[...t.map(([e,t])=>(0,u.createElement)(e,t)),...Array.isArray(a)?a:[a]]));return r.displayName=`${e}`,r},aC=aE("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]),aM=aE("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),aD=aE("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),aR=aE("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),aV=aE("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),aN=aE("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),aL=aE("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),aF=aE("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]),aO=aE("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),aI=aE("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);var aB=r(4669);function a$(){let[e,t]=(0,u.useState)([]),[r,i]=(0,u.useState)(""),[n,s]=(0,u.useState)(!1),[o,a]=(0,u.useState)(!1),[h,d]=(0,u.useState)(!0),[c,p]=(0,u.useState)(!1),[m,f]=(0,u.useState)([]),[g,y]=(0,u.useState)(!1),v=(0,u.useRef)(null),x=(0,u.useRef)(null),b=(0,u.useRef)(null),w=()=>{v.current?.scrollIntoView({behavior:"smooth"})};(0,u.useEffect)(()=>{w()},[e]);let T=async()=>{if(!r.trim()||n)return;let e={id:Date.now().toString(),content:r.trim(),role:"user",timestamp:new Date};t(t=>[...t,e]),i(""),s(!0),a(!0);try{let r=await fetch("/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:e.content,conversation_id:"default",user_id:"user",temperature:.8,max_tokens:512})});if(!r.ok)throw Error(`HTTP error! status: ${r.status}`);let i=await r.json();setTimeout(()=>{a(!1);let e={id:(Date.now()+1).toString(),content:i.response,role:"assistant",timestamp:new Date};t(t=>[...t,e])},1e3)}catch(e){console.error("Error:",e),a(!1),aB.toast.error("Failed to send message. Please try again.")}finally{s(!1)}},S=async e=>{let t=e.target.files?.[0];if(!t)return;y(!0);let r=new FormData;r.append("file",t),r.append("user_id","default");try{let e=await fetch("/upload",{method:"POST",body:r}),t=await e.json();t.success?(aB.toast.success(`Document uploaded successfully! ${t.message}`),P()):aB.toast.error(t.error||"Upload failed")}catch(e){console.error("Upload error:",e),aB.toast.error("Failed to upload document")}finally{y(!1),b.current&&(b.current.value="")}},P=async()=>{try{let e=await fetch("/documents/default"),t=await e.json();f(t.documents||[])}catch(e){console.error("Error loading documents:",e)}},j=async e=>{try{let t=`/documents/${e}?user_id=default`,r=await fetch(t,{method:"DELETE"});(await r.json()).success?(aB.toast.success("Document deleted successfully"),P()):aB.toast.error("Failed to delete document")}catch(e){console.error("Delete error:",e),aB.toast.error("Failed to delete document")}};return(0,u.useEffect)(()=>{P()},[]),l.jsx("div",{className:`min-h-screen transition-all duration-500 ${h?"bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900":"bg-gradient-to-br from-purple-100 via-blue-100 to-indigo-100"}`,children:(0,l.jsxs)("div",{className:"container mx-auto max-w-4xl h-screen flex flex-col p-4",children:[l.jsx(sE.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:`backdrop-blur-md rounded-2xl border p-6 mb-4 ${h?"bg-white/10 border-white/20 text-white":"bg-white/40 border-white/60 text-gray-800"}`,children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[l.jsx(oG,{className:"w-12 h-12 border-2 border-purple-400",children:l.jsx(oK,{className:"bg-gradient-to-r from-purple-600 to-blue-600",children:l.jsx(aC,{className:"w-6 h-6 text-white"})})}),(0,l.jsxs)("div",{children:[l.jsx("h1",{className:"text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent",children:"TainoAI"}),l.jsx("p",{className:`text-sm ${h?"text-gray-300":"text-gray-600"}`,children:"ChatGPT-style AI Assistant"})]})]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[l.jsx(oM,{variant:"ghost",size:"sm",onClick:()=>p(!c),className:`${h?"hover:bg-white/10":"hover:bg-black/10"}`,children:l.jsx(aM,{className:"w-4 h-4"})}),l.jsx(oM,{variant:"ghost",size:"sm",onClick:()=>{d(!h),aB.toast.success(`Switched to ${h?"light":"dark"} theme`)},className:`${h?"hover:bg-white/10":"hover:bg-black/10"}`,children:h?l.jsx(aD,{className:"w-4 h-4"}):l.jsx(aR,{className:"w-4 h-4"})}),l.jsx(oM,{variant:"ghost",size:"sm",onClick:()=>{t([]),aB.toast.success("Conversation reset!")},className:`${h?"hover:bg-white/10":"hover:bg-black/10"}`,children:l.jsx(aV,{className:"w-4 h-4"})})]})]})}),c&&(0,l.jsxs)(sE.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:`backdrop-blur-md rounded-2xl border p-6 mb-4 ${h?"bg-white/10 border-white/20 text-white":"bg-white/40 border-white/60 text-gray-800"}`,children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[l.jsx("h3",{className:"text-lg font-semibold",children:"\uD83D\uDCDA Document Learning"}),l.jsx("span",{className:"text-sm opacity-70",children:"Upload documents to teach TainoAI"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[l.jsx("div",{children:(0,l.jsxs)("div",{className:`border-2 border-dashed rounded-xl p-6 text-center ${h?"border-white/30":"border-gray-300"}`,children:[l.jsx(aM,{className:"w-8 h-8 mx-auto mb-2 opacity-60"}),(0,l.jsxs)("p",{className:"mb-4",children:[l.jsx("strong",{children:"Supported formats:"}),l.jsx("br",{}),"PDF, Word, Excel, Text, CSV"]}),l.jsx("input",{ref:b,type:"file",onChange:S,accept:".pdf,.docx,.doc,.xlsx,.xls,.txt,.md,.csv",className:"hidden"}),l.jsx(oM,{onClick:()=>b.current?.click(),disabled:g,className:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white",children:g?"Uploading...":"Choose File"})]})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("h4",{className:"font-medium mb-3",children:["\uD83D\uDCC4 Your Documents (",m.length,")"]}),l.jsx("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:0===m.length?l.jsx("p",{className:"text-sm opacity-60",children:"No documents uploaded yet"}):m.map(e=>(0,l.jsxs)("div",{className:`flex items-center justify-between p-3 rounded-lg ${h?"bg-white/10":"bg-white/30"}`,children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[l.jsx(aN,{className:"w-4 h-4"}),(0,l.jsxs)("div",{children:[l.jsx("p",{className:"text-sm font-medium",children:e.filename}),(0,l.jsxs)("p",{className:"text-xs opacity-60",children:[e.word_count," words"]})]})]}),l.jsx(oM,{variant:"ghost",size:"sm",onClick:()=>j(e.id),className:"text-red-400 hover:text-red-300",children:l.jsx(aL,{className:"w-4 h-4"})})]},e.id))})]})]})]}),l.jsx("div",{className:`flex-1 backdrop-blur-md rounded-2xl border p-4 mb-4 overflow-hidden ${h?"bg-white/5 border-white/10":"bg-white/30 border-white/40"}`,children:l.jsx(aP,{className:"h-full",children:(0,l.jsxs)("div",{className:"space-y-4 pr-4",children:[l.jsx(sL,{children:0===e.length?(0,l.jsxs)(sE.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center py-12",children:[l.jsx("div",{className:"w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-center",children:l.jsx(aF,{className:"w-10 h-10 text-white"})}),l.jsx("h2",{className:`text-2xl font-bold mb-2 ${h?"text-white":"text-gray-800"}`,children:"Welcome to TainoAI"}),l.jsx("p",{className:`mb-6 ${h?"text-gray-300":"text-gray-600"}`,children:"Your ChatGPT-style AI assistant powered by Mistral 7B"}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto",children:["Explain quantum computing","Write a Python function","Latest AI research trends","Create a business plan"].map((e,t)=>l.jsx(oM,{variant:"outline",className:`h-auto p-4 text-left backdrop-blur-sm ${h?"bg-white/10 border-white/20 hover:bg-white/20 text-white":"bg-white/30 border-white/40 hover:bg-white/50 text-gray-800"}`,onClick:()=>i(e),children:e},t))})]}):e.map(e=>l.jsx(sE.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`flex ${"user"===e.role?"justify-end":"justify-start"}`,children:(0,l.jsxs)("div",{className:`flex items-start space-x-3 max-w-[80%] ${"user"===e.role?"flex-row-reverse space-x-reverse":""}`,children:[l.jsx(oG,{className:"w-8 h-8 flex-shrink-0",children:l.jsx(oK,{className:"user"===e.role?"bg-blue-600":"bg-gradient-to-r from-purple-600 to-blue-600",children:"user"===e.role?l.jsx(aO,{className:"w-4 h-4"}):l.jsx(aC,{className:"w-4 h-4"})})}),l.jsx("div",{className:`backdrop-blur-sm rounded-2xl p-4 ${"user"===e.role?h?"bg-blue-600/80 text-white":"bg-blue-500/80 text-white":h?"bg-white/10 border border-white/20 text-white":"bg-white/40 border border-white/40 text-gray-800"}`,children:l.jsx("p",{className:"whitespace-pre-wrap",children:e.content})})]})},e.id))}),o&&l.jsx(sE.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"flex justify-start",children:(0,l.jsxs)("div",{className:"flex items-start space-x-3",children:[l.jsx(oG,{className:"w-8 h-8",children:l.jsx(oK,{className:"bg-gradient-to-r from-purple-600 to-blue-600",children:l.jsx(aC,{className:"w-4 h-4"})})}),l.jsx("div",{className:`backdrop-blur-sm rounded-2xl p-4 ${h?"bg-white/10 border border-white/20":"bg-white/40 border border-white/40"}`,children:(0,l.jsxs)("div",{className:"flex space-x-1",children:[l.jsx("div",{className:`w-2 h-2 rounded-full animate-bounce ${h?"bg-white":"bg-gray-600"}`,style:{animationDelay:"0ms"}}),l.jsx("div",{className:`w-2 h-2 rounded-full animate-bounce ${h?"bg-white":"bg-gray-600"}`,style:{animationDelay:"150ms"}}),l.jsx("div",{className:`w-2 h-2 rounded-full animate-bounce ${h?"bg-white":"bg-gray-600"}`,style:{animationDelay:"300ms"}})]})})]})}),l.jsx("div",{ref:v})]})})}),(0,l.jsxs)(sE.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:`backdrop-blur-md rounded-2xl border p-4 ${h?"bg-white/10 border-white/20":"bg-white/40 border-white/60"}`,children:[(0,l.jsxs)("div",{className:"flex space-x-4",children:[l.jsx(oD,{ref:x,value:r,onChange:e=>i(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),T())},placeholder:"Ask me anything...",className:`flex-1 backdrop-blur-sm border-0 rounded-xl ${h?"bg-white/10 text-white placeholder:text-white/60":"bg-white/30 text-gray-800 placeholder:text-gray-600"}`,disabled:n}),l.jsx(oM,{onClick:T,disabled:!r.trim()||n,className:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl px-6",children:n?l.jsx("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}):l.jsx(aI,{className:"w-4 h-4"})})]}),l.jsx("div",{className:`mt-2 text-xs text-center ${h?"text-gray-400":"text-gray-600"}`,children:"TainoAI - ChatGPT-style AI Assistant"})]})]})})}},2295:(e,t,r)=>{"use strict";e.exports=r(6372).vendored["react-ssr"].ReactJsxRuntime},80:(e,t,r)=>{"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var i=r(3729);"function"==typeof Object.is&&Object.is,i.useState,i.useEffect,i.useLayoutEffect,i.useDebugValue,t.useSyncExternalStore=void 0!==i.useSyncExternalStore?i.useSyncExternalStore:function(e,t){return t()}},8145:(e,t,r)=>{"use strict";e.exports=r(80)},2917:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>a});var i=r(5036),n=r(265),s=r.n(n);r(7272);var o=r(9636);let a={title:"TainoAI Rebirth v6.0 GENESIS",description:"Independent AI Assistant - Built from scratch with Next.js, FastAPI, and PyTorch",keywords:["AI","Assistant","TainoAI","Independent","PyTorch","FastAPI","Next.js"],authors:[{name:"TainoAI Team"}],viewport:"width=device-width, initial-scale=1",themeColor:"#9c6bff",openGraph:{title:"TainoAI Rebirth v6.0 GENESIS",description:"Independent AI Assistant - Built from scratch",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"TainoAI Rebirth v6.0 GENESIS",description:"Independent AI Assistant - Built from scratch"},robots:{index:!0,follow:!0}};function l({children:e}){return(0,i.jsxs)("html",{lang:"en",className:"dark",children:[(0,i.jsxs)("head",{children:[i.jsx("link",{rel:"icon",href:"/favicon.ico"}),i.jsx("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),i.jsx("meta",{name:"theme-color",content:"#9c6bff"})]}),(0,i.jsxs)("body",{className:`${s().className} antialiased`,children:[i.jsx("div",{className:"min-h-screen animated-gradient",children:e}),i.jsx(o.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"rgba(255, 255, 255, 0.1)",backdropFilter:"blur(10px)",border:"1px solid rgba(255, 255, 255, 0.2)",color:"#fff"}}})]})]})}},751:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>s,__esModule:()=>n,default:()=>o});let i=(0,r(6843).createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\tainoai\app\page.tsx`),{__esModule:n,$$typeof:s}=i,o=i.default},7272:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[816],()=>r(3762));module.exports=i})();