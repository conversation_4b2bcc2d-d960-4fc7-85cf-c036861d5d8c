/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Capp%5Cpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Capp%5Cpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVTS1RPUCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUN0YWlub2FpJTVDYXBwJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFpbm9haS8/YjU5YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERFU0tUT1BcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcdGFpbm9haVxcXFxhcHBcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVTS1RPUCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUN0YWlub2FpJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMmFwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVTS1RPUCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUN0YWlub2FpJTVDYXBwJTVDZ2xvYmFscy5jc3MmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNERVNLVE9QJTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1Q3RhaW5vYWklNUNub2RlX21vZHVsZXMlNUNyZWFjdC1ob3QtdG9hc3QlNUNkaXN0JTVDaW5kZXgubWpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3RhaW5vYWkvP2RkZGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxERVNLVE9QXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXHRhaW5vYWlcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TainoAI)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(ssr)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(ssr)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Moon,RotateCcw,Send,Sparkles,Sun,Trash2,Upload,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Moon,RotateCcw,Send,Sparkles,Sun,Trash2,Upload,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Moon,RotateCcw,Send,Sparkles,Sun,Trash2,Upload,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Moon,RotateCcw,Send,Sparkles,Sun,Trash2,Upload,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Moon,RotateCcw,Send,Sparkles,Sun,Trash2,Upload,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Moon,RotateCcw,Send,Sparkles,Sun,Trash2,Upload,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Moon,RotateCcw,Send,Sparkles,Sun,Trash2,Upload,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Moon,RotateCcw,Send,Sparkles,Sun,Trash2,Upload,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Moon,RotateCcw,Send,Sparkles,Sun,Trash2,Upload,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Moon,RotateCcw,Send,Sparkles,Sun,Trash2,Upload,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,FileText,Moon,RotateCcw,Send,Sparkles,Sun,Trash2,Upload,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _src_ai_engine_client_TainoAIClient__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/src/ai-engine/client/TainoAIClient */ \"(ssr)/./src/ai-engine/client/TainoAIClient.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction TainoAI() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDark, setIsDark] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showUpload, setShowUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [aiClient, setAiClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [engineStatus, setEngineStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        primary: \"initializing\",\n        secondary: \"initializing\",\n        claude: \"not configured\"\n    });\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    // Initialize TainoAI Engine connection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAI = async ()=>{\n            try {\n                // Check API health first\n                const healthCheck = await fetch(`${\"http://localhost:8000\"}/health`);\n                const health = await healthCheck.json();\n                if (health.status === \"healthy\") {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"API connection established!\");\n                    const client = new _src_ai_engine_client_TainoAIClient__WEBPACK_IMPORTED_MODULE_7__.TainoAIClient({\n                        serverUrl: \"ws://localhost:8001\" || 0,\n                        autoReconnect: true\n                    });\n                    // Set up event handlers\n                    client.on(\"welcome\", (data)=>{\n                        console.log(\"\\uD83C\\uDF89 TainoAI Engine connected:\", data);\n                        setEngineStatus({\n                            primary: data.models?.primary || \"not available\",\n                            secondary: data.models?.secondary || \"not available\",\n                            claude: data.models?.claude || \"not configured\"\n                        });\n                        setIsConnected(true);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"TainoAI Engine connected!\");\n                    });\n                    client.on(\"chat_response\", (data)=>{\n                        setIsTyping(false);\n                        setIsLoading(false);\n                        const assistantMessage = {\n                            id: (Date.now() + 1).toString(),\n                            content: data.response,\n                            role: \"assistant\",\n                            timestamp: new Date()\n                        };\n                        setMessages((prev)=>[\n                                ...prev,\n                                assistantMessage\n                            ]);\n                    });\n                    client.on(\"typing\", (data)=>{\n                        setIsTyping(data.isTyping);\n                    });\n                    client.on(\"error\", (error)=>{\n                        console.error(\"TainoAI Error:\", error);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.message || \"Connection error\");\n                        setIsTyping(false);\n                        setIsLoading(false);\n                    });\n                    await client.connect();\n                    setAiClient(client);\n                }\n            } catch (error) {\n                console.error(\"Failed to connect to TainoAI Engine:\", error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to connect to AI Engine\");\n            }\n        };\n        initializeAI();\n    }, []);\n    const sendMessage = async ()=>{\n        if (!input.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            content: input.trim(),\n            role: \"user\",\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput(\"\");\n        setIsLoading(true);\n        try {\n            if (aiClient && isConnected) {\n                // Use custom TainoAI Engine\n                await aiClient.sendMessage(userMessage.content, {\n                    temperature: 0.8,\n                    maxTokens: 512,\n                    useTools: true,\n                    useKnowledge: true\n                });\n            } else {\n                // Fallback to original API\n                const apiUrl =  false ? 0 : \"http://localhost:8003/chat\";\n                const response = await fetch(apiUrl, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        message: userMessage.content,\n                        conversation_id: \"default\",\n                        user_id: \"user\",\n                        temperature: 0.8,\n                        max_tokens: 512\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(`HTTP error! status: ${response.status}`);\n                }\n                const data = await response.json();\n                setTimeout(()=>{\n                    setIsTyping(false);\n                    const assistantMessage = {\n                        id: (Date.now() + 1).toString(),\n                        content: data.response,\n                        role: \"assistant\",\n                        timestamp: new Date()\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            assistantMessage\n                        ]);\n                }, 1000);\n            }\n        } catch (error) {\n            console.error(\"Error:\", error);\n            setIsTyping(false);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to send message. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    const resetConversation = ()=>{\n        setMessages([]);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Conversation reset!\");\n    };\n    const toggleTheme = ()=>{\n        setIsDark(!isDark);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success(`Switched to ${isDark ? \"light\" : \"dark\"} theme`);\n    };\n    const handleFileUpload = async (event)=>{\n        const file = event.target.files?.[0];\n        if (!file) return;\n        setIsUploading(true);\n        try {\n            // Read file content\n            const content = await readFileContent(file);\n            if (aiClient && isConnected) {\n                // Use custom TainoAI Engine\n                await aiClient.uploadDocument(content, file.name, {\n                    fileType: file.type,\n                    fileSize: file.size,\n                    uploadedAt: new Date().toISOString()\n                });\n            } else {\n                // Fallback to original API\n                const formData = new FormData();\n                formData.append(\"file\", file);\n                formData.append(\"user_id\", \"default\");\n                const apiUrl =  false ? 0 : \"http://localhost:8003/upload\";\n                const response = await fetch(apiUrl, {\n                    method: \"POST\",\n                    body: formData\n                });\n                const result = await response.json();\n                if (result.success) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success(`Document uploaded successfully! ${result.message}`);\n                    loadDocuments();\n                } else {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(result.error || \"Upload failed\");\n                }\n            }\n        } catch (error) {\n            console.error(\"Upload error:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to upload document\");\n        } finally{\n            setIsUploading(false);\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n        }\n    };\n    const readFileContent = (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                const content = e.target?.result;\n                resolve(content);\n            };\n            reader.onerror = ()=>reject(new Error(\"Failed to read file\"));\n            reader.readAsText(file);\n        });\n    };\n    const loadDocuments = async ()=>{\n        try {\n            const apiUrl =  false ? 0 : \"http://localhost:8003/documents/default\";\n            const response = await fetch(apiUrl);\n            const data = await response.json();\n            setDocuments(data.documents || []);\n        } catch (error) {\n            console.error(\"Error loading documents:\", error);\n        }\n    };\n    const deleteDocument = async (fileId)=>{\n        try {\n            const apiUrl =  false ? 0 : `http://localhost:8003/documents/${fileId}?user_id=default`;\n            const response = await fetch(apiUrl, {\n                method: \"DELETE\"\n            });\n            const result = await response.json();\n            if (result.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Document deleted successfully\");\n                loadDocuments();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to delete document\");\n            }\n        } catch (error) {\n            console.error(\"Delete error:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to delete document\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDocuments();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-h-screen transition-all duration-500 ${isDark ? \"bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900\" : \"bg-gradient-to-br from-purple-100 via-blue-100 to-indigo-100\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto max-w-4xl h-screen flex flex-col p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: `backdrop-blur-md rounded-2xl border p-6 mb-4 ${isDark ? \"bg-white/10 border-white/20 text-white\" : \"bg-white/40 border-white/60 text-gray-800\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                        className: \"w-12 h-12 border-2 border-purple-400\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                            className: \"bg-gradient-to-r from-purple-600 to-blue-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\",\n                                                children: \"TainoAI v1.0.11\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: `text-sm ${isDark ? \"text-gray-300\" : \"text-gray-600\"}`,\n                                                        children: \"Multi-Model AI Engine\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-3 h-3 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-400\",\n                                                                    children: \"LIVE\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                engineStatus.claude === \"available\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-xs text-purple-400\",\n                                                                    children: \"+ Claude\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-yellow-400\",\n                                                                children: \"Initializing...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowUpload(!showUpload),\n                                        className: `${isDark ? \"hover:bg-white/10\" : \"hover:bg-black/10\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: toggleTheme,\n                                        className: `${isDark ? \"hover:bg-white/10\" : \"hover:bg-black/10\"}`,\n                                        children: isDark ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 27\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 57\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: resetConversation,\n                                        className: `${isDark ? \"hover:bg-white/10\" : \"hover:bg-black/10\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this),\n                showUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: \"auto\"\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    className: `backdrop-blur-md rounded-2xl border p-6 mb-4 ${isDark ? \"bg-white/10 border-white/20 text-white\" : \"bg-white/40 border-white/60 text-gray-800\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"\\uD83D\\uDCDA Document Learning\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm opacity-70\",\n                                    children: \"Upload documents to teach TainoAI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `border-2 border-dashed rounded-xl p-6 text-center ${isDark ? \"border-white/30\" : \"border-gray-300\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-8 h-8 mx-auto mb-2 opacity-60\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Supported formats:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 56\n                                                    }, this),\n                                                    \"PDF, Word, Excel, Text, CSV\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: fileInputRef,\n                                                type: \"file\",\n                                                onChange: handleFileUpload,\n                                                accept: \".pdf,.docx,.doc,.xlsx,.xls,.txt,.md,.csv\",\n                                                className: \"hidden\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: ()=>fileInputRef.current?.click(),\n                                                disabled: isUploading,\n                                                className: \"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white\",\n                                                children: isUploading ? \"Uploading...\" : \"Choose File\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium mb-3\",\n                                            children: [\n                                                \"\\uD83D\\uDCC4 Your Documents (\",\n                                                documents.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 max-h-40 overflow-y-auto\",\n                                            children: documents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm opacity-60\",\n                                                children: \"No documents uploaded yet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 21\n                                            }, this) : documents.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `flex items-center justify-between p-3 rounded-lg ${isDark ? \"bg-white/10\" : \"bg-white/30\"}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: doc.filename\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 439,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs opacity-60\",\n                                                                            children: [\n                                                                                doc.word_count,\n                                                                                \" words\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 440,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>deleteDocument(doc.id),\n                                                            className: \"text-red-400 hover:text-red-300\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, doc.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex-1 backdrop-blur-md rounded-2xl border p-4 mb-4 overflow-hidden ${isDark ? \"bg-white/5 border-white/10\" : \"bg-white/30 border-white/40\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                        className: \"h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4 pr-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.AnimatePresence, {\n                                    children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-10 h-10 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: `text-2xl font-bold mb-2 ${isDark ? \"text-white\" : \"text-gray-800\"}`,\n                                                children: \"Welcome to TainoAI v1.0.11\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: `mb-6 ${isDark ? \"text-gray-300\" : \"text-gray-600\"}`,\n                                                children: isConnected ? `🚀 Multi-Model AI Engine with LLaMA/Mistral${engineStatus.claude === \"available\" ? \" + Claude\" : \"\"} + Tools` : \"⚡ Fully Independent AI System + Cloud Fallback\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto\",\n                                                children: [\n                                                    \"Explain quantum computing\",\n                                                    \"Write a Python function\",\n                                                    \"Latest AI research trends\",\n                                                    \"Create a business plan\"\n                                                ].map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        className: `h-auto p-4 text-left backdrop-blur-sm ${isDark ? \"bg-white/10 border-white/20 hover:bg-white/20 text-white\" : \"bg-white/30 border-white/40 hover:bg-white/50 text-gray-800\"}`,\n                                                        onClick: ()=>setInput(suggestion),\n                                                        children: suggestion\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 19\n                                    }, this) : messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                y: -20\n                                            },\n                                            className: `flex ${message.role === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex items-start space-x-3 max-w-[80%] ${message.role === \"user\" ? \"flex-row-reverse space-x-reverse\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                        className: \"w-8 h-8 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                            className: message.role === \"user\" ? \"bg-blue-600\" : \"bg-gradient-to-r from-purple-600 to-blue-600\",\n                                                            children: message.role === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 56\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 87\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `backdrop-blur-sm rounded-2xl p-4 ${message.role === \"user\" ? isDark ? \"bg-blue-600/80 text-white\" : \"bg-blue-500/80 text-white\" : isDark ? \"bg-white/10 border border-white/20 text-white\" : \"bg-white/40 border border-white/40 text-gray-800\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"whitespace-pre-wrap\",\n                                                            children: message.content\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, message.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this),\n                                isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"flex justify-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                className: \"w-8 h-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                    className: \"bg-gradient-to-r from-purple-600 to-blue-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `backdrop-blur-sm rounded-2xl p-4 ${isDark ? \"bg-white/10 border border-white/20\" : \"bg-white/40 border border-white/40\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-2 h-2 rounded-full animate-bounce ${isDark ? \"bg-white\" : \"bg-gray-600\"}`,\n                                                            style: {\n                                                                animationDelay: \"0ms\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-2 h-2 rounded-full animate-bounce ${isDark ? \"bg-white\" : \"bg-gray-600\"}`,\n                                                            style: {\n                                                                animationDelay: \"150ms\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-2 h-2 rounded-full animate-bounce ${isDark ? \"bg-white\" : \"bg-gray-600\"}`,\n                                                            style: {\n                                                                animationDelay: \"300ms\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: `backdrop-blur-md rounded-2xl border p-4 ${isDark ? \"bg-white/10 border-white/20\" : \"bg-white/40 border-white/60\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    ref: inputRef,\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    onKeyPress: handleKeyPress,\n                                    placeholder: \"Ask me anything...\",\n                                    className: `flex-1 backdrop-blur-sm border-0 rounded-xl ${isDark ? \"bg-white/10 text-white placeholder:text-white/60\" : \"bg-white/30 text-gray-800 placeholder:text-gray-600\"}`,\n                                    disabled: isLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: sendMessage,\n                                    disabled: !input.trim() || isLoading,\n                                    className: \"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl px-6\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `mt-2 text-xs text-center ${isDark ? \"text-gray-400\" : \"text-gray-600\"}`,\n                            children: [\n                                \"TainoAI v1.0.11 - \",\n                                isConnected ? \"Custom AI Engine LIVE\" : \"Independent AI System\",\n                                isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_FileText_Moon_RotateCcw_Send_Sparkles_Sun_Trash2_Upload_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-3 h-3 text-green-400 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-400\",\n                                            children: \"LIVE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                            lineNumber: 624,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n                    lineNumber: 589,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n            lineNumber: 302,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\page.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/avatar.tsx":
/*!**********************************!*\
  !*** ./components/ui/avatar.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarImage,AvatarFallback auto */ \n\n\n\nconst Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nAvatar.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\nconst AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"aspect-square h-full w-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nAvatarImage.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image.displayName;\nconst AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-full w-full items-center justify-center rounded-full bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nAvatarFallback.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/avatar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFpbm9haS8uL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4P2RhNzkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIElucHV0UHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBJbnB1dFByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/scroll-area.tsx":
/*!***************************************!*\
  !*** ./components/ui/scroll-area.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollBar: () => (/* binding */ ScrollBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-scroll-area */ \"(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ScrollArea,ScrollBar auto */ \n\n\n\nconst ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative overflow-hidden\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                className: \"h-full w-full rounded-[inherit]\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 17,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollBar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 20,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Corner, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nScrollArea.displayName = _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\nconst ScrollBar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = \"vertical\", ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar, {\n        ref: ref,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex touch-none select-none transition-colors\", orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent p-[1px]\", orientation === \"horizontal\" && \"h-2.5 flex-col border-t border-t-transparent p-[1px]\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaThumb, {\n            className: \"relative flex-1 rounded-full bg-border\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\components\\\\ui\\\\scroll-area.tsx\",\n            lineNumber: 43,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 30,\n        columnNumber: 3\n    }, undefined));\nScrollBar.displayName = _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3Njcm9sbC1hcmVhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEI7QUFDb0M7QUFFbEM7QUFFaEMsTUFBTUcsMkJBQWFILDZDQUFnQixDQUdqQyxDQUFDLEVBQUVLLFNBQVMsRUFBRUMsUUFBUSxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQ3BDLDhEQUFDUCw2REFBd0I7UUFDdkJPLEtBQUtBO1FBQ0xILFdBQVdILDhDQUFFQSxDQUFDLDRCQUE0Qkc7UUFDekMsR0FBR0UsS0FBSzs7MEJBRVQsOERBQUNOLGlFQUE0QjtnQkFBQ0ksV0FBVTswQkFDckNDOzs7Ozs7MEJBRUgsOERBQUNLOzs7OzswQkFDRCw4REFBQ1YsK0RBQTBCOzs7Ozs7Ozs7OztBQUcvQkUsV0FBV1UsV0FBVyxHQUFHWiw2REFBd0IsQ0FBQ1ksV0FBVztBQUU3RCxNQUFNRiwwQkFBWVgsNkNBQWdCLENBR2hDLENBQUMsRUFBRUssU0FBUyxFQUFFUyxjQUFjLFVBQVUsRUFBRSxHQUFHUCxPQUFPLEVBQUVDLG9CQUNwRCw4REFBQ1AsNEVBQXVDO1FBQ3RDTyxLQUFLQTtRQUNMTSxhQUFhQTtRQUNiVCxXQUFXSCw4Q0FBRUEsQ0FDWCxpREFDQVksZ0JBQWdCLGNBQ2Qsc0RBQ0ZBLGdCQUFnQixnQkFDZCx3REFDRlQ7UUFFRCxHQUFHRSxLQUFLO2tCQUVULDRFQUFDTix3RUFBbUM7WUFBQ0ksV0FBVTs7Ozs7Ozs7Ozs7QUFHbkRNLFVBQVVFLFdBQVcsR0FBR1osNEVBQXVDLENBQUNZLFdBQVc7QUFFM0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YWlub2FpLy4vY29tcG9uZW50cy91aS9zY3JvbGwtYXJlYS50c3g/ODllZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0ICogYXMgU2Nyb2xsQXJlYVByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXNjcm9sbC1hcmVhXCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBTY3JvbGxBcmVhID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgU2Nyb2xsQXJlYVByaW1pdGl2ZS5Sb290PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBTY3JvbGxBcmVhUHJpbWl0aXZlLlJvb3Q+XG4+KCh7IGNsYXNzTmFtZSwgY2hpbGRyZW4sIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8U2Nyb2xsQXJlYVByaW1pdGl2ZS5Sb290XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcInJlbGF0aXZlIG92ZXJmbG93LWhpZGRlblwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgPlxuICAgIDxTY3JvbGxBcmVhUHJpbWl0aXZlLlZpZXdwb3J0IGNsYXNzTmFtZT1cImgtZnVsbCB3LWZ1bGwgcm91bmRlZC1baW5oZXJpdF1cIj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1Njcm9sbEFyZWFQcmltaXRpdmUuVmlld3BvcnQ+XG4gICAgPFNjcm9sbEJhciAvPlxuICAgIDxTY3JvbGxBcmVhUHJpbWl0aXZlLkNvcm5lciAvPlxuICA8L1Njcm9sbEFyZWFQcmltaXRpdmUuUm9vdD5cbikpXG5TY3JvbGxBcmVhLmRpc3BsYXlOYW1lID0gU2Nyb2xsQXJlYVByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXG5cbmNvbnN0IFNjcm9sbEJhciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFNjcm9sbEFyZWFQcmltaXRpdmUuU2Nyb2xsQXJlYVNjcm9sbGJhcj4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgU2Nyb2xsQXJlYVByaW1pdGl2ZS5TY3JvbGxBcmVhU2Nyb2xsYmFyPlxuPigoeyBjbGFzc05hbWUsIG9yaWVudGF0aW9uID0gXCJ2ZXJ0aWNhbFwiLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPFNjcm9sbEFyZWFQcmltaXRpdmUuU2Nyb2xsQXJlYVNjcm9sbGJhclxuICAgIHJlZj17cmVmfVxuICAgIG9yaWVudGF0aW9uPXtvcmllbnRhdGlvbn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJmbGV4IHRvdWNoLW5vbmUgc2VsZWN0LW5vbmUgdHJhbnNpdGlvbi1jb2xvcnNcIixcbiAgICAgIG9yaWVudGF0aW9uID09PSBcInZlcnRpY2FsXCIgJiZcbiAgICAgICAgXCJoLWZ1bGwgdy0yLjUgYm9yZGVyLWwgYm9yZGVyLWwtdHJhbnNwYXJlbnQgcC1bMXB4XVwiLFxuICAgICAgb3JpZW50YXRpb24gPT09IFwiaG9yaXpvbnRhbFwiICYmXG4gICAgICAgIFwiaC0yLjUgZmxleC1jb2wgYm9yZGVyLXQgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcC1bMXB4XVwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gID5cbiAgICA8U2Nyb2xsQXJlYVByaW1pdGl2ZS5TY3JvbGxBcmVhVGh1bWIgY2xhc3NOYW1lPVwicmVsYXRpdmUgZmxleC0xIHJvdW5kZWQtZnVsbCBiZy1ib3JkZXJcIiAvPlxuICA8L1Njcm9sbEFyZWFQcmltaXRpdmUuU2Nyb2xsQXJlYVNjcm9sbGJhcj5cbikpXG5TY3JvbGxCYXIuZGlzcGxheU5hbWUgPSBTY3JvbGxBcmVhUHJpbWl0aXZlLlNjcm9sbEFyZWFTY3JvbGxiYXIuZGlzcGxheU5hbWVcblxuZXhwb3J0IHsgU2Nyb2xsQXJlYSwgU2Nyb2xsQmFyIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNjcm9sbEFyZWFQcmltaXRpdmUiLCJjbiIsIlNjcm9sbEFyZWEiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwiY2hpbGRyZW4iLCJwcm9wcyIsInJlZiIsIlJvb3QiLCJWaWV3cG9ydCIsIlNjcm9sbEJhciIsIkNvcm5lciIsImRpc3BsYXlOYW1lIiwib3JpZW50YXRpb24iLCJTY3JvbGxBcmVhU2Nyb2xsYmFyIiwiU2Nyb2xsQXJlYVRodW1iIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/scroll-area.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatTimestamp: () => (/* binding */ formatTimestamp),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   sleep: () => (/* binding */ sleep)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatTimestamp(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        hour: \"2-digit\",\n        minute: \"2-digit\",\n        hour12: true\n    }).format(date);\n}\nfunction generateId() {\n    return Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\nasync function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCO0FBRU8sU0FBU0MsZ0JBQWdCQyxJQUFVO0lBQ3hDLE9BQU8sSUFBSUMsS0FBS0MsY0FBYyxDQUFDLFNBQVM7UUFDdENDLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxRQUFRO0lBQ1YsR0FBR0MsTUFBTSxDQUFDTjtBQUNaO0FBRU8sU0FBU087SUFDZCxPQUFPQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxTQUFTLENBQUMsS0FBS0MsS0FBS0MsR0FBRyxHQUFHSCxRQUFRLENBQUM7QUFDdkU7QUFFTyxlQUFlSSxNQUFNQyxFQUFVO0lBQ3BDLE9BQU8sSUFBSUMsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBU0Y7QUFDcEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YWlub2FpLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRUaW1lc3RhbXAoZGF0ZTogRGF0ZSk6IHN0cmluZyB7XG4gIHJldHVybiBuZXcgSW50bC5EYXRlVGltZUZvcm1hdCgnZW4tVVMnLCB7XG4gICAgaG91cjogJzItZGlnaXQnLFxuICAgIG1pbnV0ZTogJzItZGlnaXQnLFxuICAgIGhvdXIxMjogdHJ1ZSxcbiAgfSkuZm9ybWF0KGRhdGUpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUlkKCk6IHN0cmluZyB7XG4gIHJldHVybiBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMikgKyBEYXRlLm5vdygpLnRvU3RyaW5nKDM2KVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc2xlZXAobXM6IG51bWJlcik6IFByb21pc2U8dm9pZD4ge1xuICByZXR1cm4gbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIG1zKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIiwiZm9ybWF0VGltZXN0YW1wIiwiZGF0ZSIsIkludGwiLCJEYXRlVGltZUZvcm1hdCIsImhvdXIiLCJtaW51dGUiLCJob3VyMTIiLCJmb3JtYXQiLCJnZW5lcmF0ZUlkIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyaW5nIiwiRGF0ZSIsIm5vdyIsInNsZWVwIiwibXMiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/ai-engine/client/TainoAIClient.ts":
/*!***********************************************!*\
  !*** ./src/ai-engine/client/TainoAIClient.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TainoAIClient: () => (/* binding */ TainoAIClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useTainoAI: () => (/* binding */ useTainoAI)\n/* harmony export */ });\n/**\n * TainoAI Client - Frontend Integration for Custom AI Engine\n */ class TainoAIClient {\n    constructor(options = {}){\n        this.ws = null;\n        this.reconnectAttempts = 0;\n        this.isConnected = false;\n        this.messageHandlers = new Map();\n        this.serverUrl = options.serverUrl || \"ws://localhost:8001\";\n        this.autoReconnect = options.autoReconnect !== false;\n        this.reconnectInterval = options.reconnectInterval || 3000;\n        this.maxReconnectAttempts = options.maxReconnectAttempts || 5;\n        this.conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    async connect() {\n        return new Promise((resolve, reject)=>{\n            try {\n                this.ws = new WebSocket(this.serverUrl);\n                this.ws.onopen = ()=>{\n                    console.log(\"\\uD83D\\uDD0C Connected to TainoAI Engine\");\n                    this.isConnected = true;\n                    this.reconnectAttempts = 0;\n                    resolve();\n                };\n                this.ws.onmessage = (event)=>{\n                    try {\n                        const message = JSON.parse(event.data);\n                        this.handleMessage(message);\n                    } catch (error) {\n                        console.error(\"❌ Failed to parse message:\", error);\n                    }\n                };\n                this.ws.onclose = ()=>{\n                    console.log(\"\\uD83D\\uDD0C Disconnected from TainoAI Engine\");\n                    this.isConnected = false;\n                    if (this.autoReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {\n                        this.reconnectAttempts++;\n                        console.log(`🔄 Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n                        setTimeout(()=>this.connect(), this.reconnectInterval);\n                    }\n                };\n                this.ws.onerror = (error)=>{\n                    console.error(\"❌ WebSocket error:\", error);\n                    reject(error);\n                };\n            } catch (error) {\n                reject(error);\n            }\n        });\n    }\n    handleMessage(message) {\n        const { type, data } = message;\n        // Call registered handlers\n        const handler = this.messageHandlers.get(type);\n        if (handler) {\n            handler(data);\n        }\n        // Default handling for common message types\n        switch(type){\n            case \"welcome\":\n                console.log(\"\\uD83C\\uDF89 TainoAI Engine:\", data.message);\n                break;\n            case \"error\":\n                console.error(\"❌ TainoAI Error:\", data.error);\n                break;\n            case \"typing\":\n                break;\n        }\n    }\n    on(eventType, handler) {\n        this.messageHandlers.set(eventType, handler);\n    }\n    off(eventType) {\n        this.messageHandlers.delete(eventType);\n    }\n    async sendMessage(message, options = {}) {\n        if (!this.isConnected || !this.ws) {\n            throw new Error(\"Not connected to TainoAI Engine\");\n        }\n        const payload = {\n            type: \"chat\",\n            data: {\n                message,\n                conversationId: this.conversationId,\n                userId: \"default\",\n                options\n            }\n        };\n        this.ws.send(JSON.stringify(payload));\n    }\n    async uploadDocument(content, filename, metadata = {}) {\n        if (!this.isConnected || !this.ws) {\n            throw new Error(\"Not connected to TainoAI Engine\");\n        }\n        const payload = {\n            type: \"upload_document\",\n            data: {\n                content,\n                filename,\n                metadata\n            }\n        };\n        this.ws.send(JSON.stringify(payload));\n    }\n    async searchKnowledge(query, maxResults = 5, source) {\n        if (!this.isConnected || !this.ws) {\n            throw new Error(\"Not connected to TainoAI Engine\");\n        }\n        const payload = {\n            type: \"search_knowledge\",\n            data: {\n                query,\n                maxResults,\n                source\n            }\n        };\n        this.ws.send(JSON.stringify(payload));\n    }\n    async executeCode(code, language, timeout, args) {\n        if (!this.isConnected || !this.ws) {\n            throw new Error(\"Not connected to TainoAI Engine\");\n        }\n        const payload = {\n            type: \"execute_code\",\n            data: {\n                code,\n                language,\n                timeout,\n                args\n            }\n        };\n        this.ws.send(JSON.stringify(payload));\n    }\n    async webSearch(query, maxResults = 5, source = \"duckduckgo\") {\n        if (!this.isConnected || !this.ws) {\n            throw new Error(\"Not connected to TainoAI Engine\");\n        }\n        const payload = {\n            type: \"web_search\",\n            data: {\n                query,\n                maxResults,\n                source\n            }\n        };\n        this.ws.send(JSON.stringify(payload));\n    }\n    async getStats() {\n        if (!this.isConnected || !this.ws) {\n            throw new Error(\"Not connected to TainoAI Engine\");\n        }\n        const payload = {\n            type: \"get_stats\",\n            data: {}\n        };\n        this.ws.send(JSON.stringify(payload));\n    }\n    ping() {\n        if (!this.isConnected || !this.ws) {\n            return;\n        }\n        const payload = {\n            type: \"ping\",\n            data: {\n                timestamp: Date.now()\n            }\n        };\n        this.ws.send(JSON.stringify(payload));\n    }\n    disconnect() {\n        this.autoReconnect = false;\n        if (this.ws) {\n            this.ws.close();\n            this.ws = null;\n        }\n        this.isConnected = false;\n    }\n    getConnectionStatus() {\n        return {\n            isConnected: this.isConnected,\n            reconnectAttempts: this.reconnectAttempts,\n            conversationId: this.conversationId\n        };\n    }\n    newConversation() {\n        this.conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    // Utility methods for React integration\n    createChatHook() {\n        return {\n            sendMessage: this.sendMessage.bind(this),\n            uploadDocument: this.uploadDocument.bind(this),\n            searchKnowledge: this.searchKnowledge.bind(this),\n            executeCode: this.executeCode.bind(this),\n            webSearch: this.webSearch.bind(this),\n            getStats: this.getStats.bind(this),\n            newConversation: this.newConversation.bind(this),\n            getConnectionStatus: this.getConnectionStatus.bind(this),\n            on: this.on.bind(this),\n            off: this.off.bind(this)\n        };\n    }\n}\n// React Hook for TainoAI\nfunction useTainoAI(options) {\n    const client = new TainoAIClient(options);\n    return {\n        client,\n        connect: ()=>client.connect(),\n        disconnect: ()=>client.disconnect(),\n        ...client.createChatHook()\n    };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TainoAIClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/ai-engine/client/TainoAIClient.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c4edf8e784c9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YWlub2FpLy4vYXBwL2dsb2JhbHMuY3NzP2FlMTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjNGVkZjhlNzg0YzlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\nconst metadata = {\n    title: \"TainoAI Rebirth v6.0 GENESIS\",\n    description: \"Independent AI Assistant - Built from scratch with Next.js, FastAPI, and PyTorch\",\n    keywords: [\n        \"AI\",\n        \"Assistant\",\n        \"TainoAI\",\n        \"Independent\",\n        \"PyTorch\",\n        \"FastAPI\",\n        \"Next.js\"\n    ],\n    authors: [\n        {\n            name: \"TainoAI Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#9c6bff\",\n    openGraph: {\n        title: \"TainoAI Rebirth v6.0 GENESIS\",\n        description: \"Independent AI Assistant - Built from scratch\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"TainoAI Rebirth v6.0 GENESIS\",\n        description: \"Independent AI Assistant - Built from scratch\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#9c6bff\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\layout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen animated-gradient\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"rgba(255, 255, 255, 0.1)\",\n                                backdropFilter: \"blur(10px)\",\n                                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                color: \"#fff\"\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\layout.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\tainoai\\\\app\\\\layout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\tainoai\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/@radix-ui","vendor-chunks/lucide-react","vendor-chunks/use-sync-external-store","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDESKTOP%5CDocuments%5Caugment-projects%5Ctainoai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();