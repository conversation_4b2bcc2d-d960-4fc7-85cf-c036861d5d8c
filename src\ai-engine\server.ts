/**
 * TainoAI Engine Server - WebSocket-based AI Communication
 */

import WebSocket from 'ws';
import http from 'http';
import { TainoAIEngine } from './core/TainoAIEngine';
import { AIEngineConfig } from './types';
import path from 'path';

// Configuration
const config: AIEngineConfig = {
  models: {
    primary: 'mistral-7b-instruct-v0.1.Q4_K_M.gguf',
    secondary: 'llama-2-7b-chat.Q4_K_M.gguf',
    fallback: 'phi-2.Q4_K_M.gguf',
  },
  performance: {
    maxTokens: 512,
    temperature: 0.8,
    topP: 0.9,
    topK: 40,
    threads: 4,
    contextSize: 2048,
  },
  features: {
    webSearch: true,
    documentLearning: true,
    voiceInput: false,
    imageAnalysis: false,
    codeExecution: true,
  },
  storage: {
    database: './data/tainoai.db',
    modelsPath: './models',
    documentsPath: './documents',
    cachePath: './cache',
  },
};

class TainoAIServer {
  private server: http.Server;
  private wss: WebSocket.Server;
  private aiEngine: TainoAIEngine;
  private port: number;
  private isInitialized = false;

  constructor(port: number = 8001) {
    this.port = port;
    this.server = http.createServer();
    this.wss = new WebSocket.Server({ server: this.server });
    this.aiEngine = new TainoAIEngine(config);

    this.setupWebSocketHandlers();
    this.setupServerHandlers();
  }

  private setupWebSocketHandlers(): void {
    this.wss.on('connection', (ws: WebSocket) => {
      console.log('🔌 New client connected');

      ws.on('message', async (data: WebSocket.Data) => {
        try {
          const message = JSON.parse(data.toString());
          await this.handleMessage(ws, message);
        } catch (error) {
          console.error('❌ Error handling message:', error);
          this.sendError(ws, 'Invalid message format');
        }
      });

      ws.on('close', () => {
        console.log('🔌 Client disconnected');
      });

      ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
      });

      // Send welcome message
      this.sendMessage(ws, {
        type: 'welcome',
        data: {
          message: '¡Hola! TainoAI Engine is ready to assist you.',
          version: '1.0.11',
          features: config.features,
          status: this.isInitialized ? 'ready' : 'initializing',
        },
      });
    });
  }

  private setupServerHandlers(): void {
    this.server.on('error', (error) => {
      console.error('❌ Server error:', error);
    });

    this.server.on('listening', () => {
      console.log(`🚀 TainoAI Engine Server listening on port ${this.port}`);
    });
  }

  private async handleMessage(ws: WebSocket, message: any): Promise<void> {
    const { type, data } = message;

    switch (type) {
      case 'chat':
        await this.handleChatMessage(ws, data);
        break;

      case 'upload_document':
        await this.handleDocumentUpload(ws, data);
        break;

      case 'search_knowledge':
        await this.handleKnowledgeSearch(ws, data);
        break;

      case 'execute_code':
        await this.handleCodeExecution(ws, data);
        break;

      case 'web_search':
        await this.handleWebSearch(ws, data);
        break;

      case 'get_stats':
        await this.handleGetStats(ws, data);
        break;

      case 'ping':
        this.sendMessage(ws, { type: 'pong', data: { timestamp: Date.now() } });
        break;

      default:
        this.sendError(ws, `Unknown message type: ${type}`);
    }
  }

  private async handleChatMessage(ws: WebSocket, data: any): Promise<void> {
    const { message, conversationId, userId, options = {} } = data;

    if (!this.isInitialized) {
      this.sendError(ws, 'AI Engine not initialized yet');
      return;
    }

    try {
      // Send typing indicator
      this.sendMessage(ws, {
        type: 'typing',
        data: { conversationId, isTyping: true },
      });

      // Generate response
      const response = await this.aiEngine.generateResponse(
        message,
        conversationId || `conv_${Date.now()}`,
        userId || 'default',
        {
          temperature: options.temperature,
          maxTokens: options.maxTokens,
          useTools: options.useTools !== false,
          useKnowledge: options.useKnowledge !== false,
        }
      );

      // Send response
      this.sendMessage(ws, {
        type: 'chat_response',
        data: {
          conversationId,
          response: response.content,
          metadata: response.metadata,
          confidence: response.confidence,
        },
      });

    } catch (error) {
      console.error('❌ Chat error:', error);
      this.sendError(ws, 'Failed to generate response');
    } finally {
      // Stop typing indicator
      this.sendMessage(ws, {
        type: 'typing',
        data: { conversationId, isTyping: false },
      });
    }
  }

  private async handleDocumentUpload(ws: WebSocket, data: any): Promise<void> {
    const { content, filename, metadata = {} } = data;

    try {
      await this.aiEngine['knowledgeManager'].addDocument(
        content,
        filename,
        { ...metadata, uploadedAt: new Date() }
      );

      this.sendMessage(ws, {
        type: 'document_uploaded',
        data: {
          filename,
          success: true,
          message: `Document "${filename}" processed and added to knowledge base`,
        },
      });

    } catch (error) {
      console.error('❌ Document upload error:', error);
      this.sendError(ws, 'Failed to process document');
    }
  }

  private async handleKnowledgeSearch(ws: WebSocket, data: any): Promise<void> {
    const { query, maxResults = 5, source } = data;

    try {
      const results = await this.aiEngine['knowledgeManager'].searchSimilar(
        query,
        maxResults,
        source
      );

      this.sendMessage(ws, {
        type: 'knowledge_search_results',
        data: {
          query,
          results,
          totalResults: results.length,
        },
      });

    } catch (error) {
      console.error('❌ Knowledge search error:', error);
      this.sendError(ws, 'Failed to search knowledge base');
    }
  }

  private async handleCodeExecution(ws: WebSocket, data: any): Promise<void> {
    const { code, language, timeout, args } = data;

    try {
      const codeExecutor = this.aiEngine['toolManager'].getTool('code_executor');
      if (!codeExecutor) {
        throw new Error('Code executor not available');
      }

      const result = await codeExecutor.execute({
        code,
        language,
        timeout,
        args,
      });

      this.sendMessage(ws, {
        type: 'code_execution_result',
        data: result,
      });

    } catch (error) {
      console.error('❌ Code execution error:', error);
      this.sendError(ws, 'Failed to execute code');
    }
  }

  private async handleWebSearch(ws: WebSocket, data: any): Promise<void> {
    const { query, maxResults = 5, source = 'duckduckgo' } = data;

    try {
      const webSearchTool = this.aiEngine['toolManager'].getTool('web_search');
      if (!webSearchTool) {
        throw new Error('Web search tool not available');
      }

      const results = await webSearchTool.execute({
        query,
        maxResults,
        source,
      });

      this.sendMessage(ws, {
        type: 'web_search_results',
        data: {
          query,
          results,
          source,
        },
      });

    } catch (error) {
      console.error('❌ Web search error:', error);
      this.sendError(ws, 'Failed to perform web search');
    }
  }

  private async handleGetStats(ws: WebSocket, data: any): Promise<void> {
    try {
      const memoryStats = await this.aiEngine['memory'].getMemoryStats();
      const knowledgeStats = await this.aiEngine['knowledgeManager'].getStats();
      const toolStats = this.aiEngine['toolManager'].getToolUsageStats();

      this.sendMessage(ws, {
        type: 'stats',
        data: {
          memory: memoryStats,
          knowledge: knowledgeStats,
          tools: toolStats,
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
        },
      });

    } catch (error) {
      console.error('❌ Stats error:', error);
      this.sendError(ws, 'Failed to get stats');
    }
  }

  private sendMessage(ws: WebSocket, message: any): void {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  private sendError(ws: WebSocket, error: string): void {
    this.sendMessage(ws, {
      type: 'error',
      data: { error, timestamp: Date.now() },
    });
  }

  async start(): Promise<void> {
    try {
      console.log('🚀 Starting TainoAI Engine Server...');

      // Initialize AI Engine
      console.log('🧠 Initializing AI Engine...');
      await this.aiEngine.initialize();
      this.isInitialized = true;
      console.log('✅ AI Engine initialized successfully');

      // Start server
      this.server.listen(this.port);

      // Graceful shutdown
      process.on('SIGINT', () => this.shutdown());
      process.on('SIGTERM', () => this.shutdown());

    } catch (error) {
      console.error('❌ Failed to start server:', error);
      process.exit(1);
    }
  }

  private async shutdown(): Promise<void> {
    console.log('🔄 Shutting down TainoAI Engine Server...');

    // Close WebSocket connections
    this.wss.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.close();
      }
    });

    // Close WebSocket server
    this.wss.close();

    // Close HTTP server
    this.server.close();

    // Shutdown AI Engine
    if (this.aiEngine) {
      await this.aiEngine.shutdown();
    }

    console.log('✅ Server shutdown complete');
    process.exit(0);
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new TainoAIServer();
  server.start().catch(error => {
    console.error('❌ Failed to start TainoAI Engine Server:', error);
    process.exit(1);
  });
}

export default TainoAIServer;
