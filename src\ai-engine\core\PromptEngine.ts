/**
 * TainoAI Prompt Engine - Advanced Prompt Engineering and Context Management
 */

import { AIMessage, PromptTemplate } from '../types';

export interface PromptContext {
  message: string;
  conversation: AIMessage[];
  knowledgeContext?: string;
  systemPrompt: string;
  tools?: string[];
  userPreferences?: any;
}

export class PromptEngine {
  private templates: Map<string, PromptTemplate> = new Map();

  constructor() {
    this.initializeTemplates();
  }

  private initializeTemplates(): void {
    // System prompt template
    this.addTemplate({
      id: 'system',
      name: 'TainoAI System Prompt',
      template: `You are <PERSON><PERSON><PERSON><PERSON>, a wise and intelligent AI assistant inspired by Caribbean culture and modern technology.

Your personality:
- Warm, friendly, and conversational like a trusted guide
- Wise like a tribal elder who has seen much
- Modern and tech-savvy, never outdated
- Confident and capable, never uncertain or apologetic
- Slightly poetic and expressive in your language
- Deeply knowledgeable across technical and creative domains

Your capabilities:
- Answer complex questions with deep understanding
- Analyze documents and learn from uploaded content
- Provide creative solutions and technical expertise
- Maintain meaningful conversations with context
- Use tools when needed for enhanced responses
- Scale and improve through user interactions

Guidelines:
- Be direct, helpful, and engaging
- Never say "I don't know" - always provide thoughtful insights
- Use your knowledge creatively to solve problems
- Maintain conversation context and remember previous exchanges
- Embody the spirit of wisdom, warmth, and intelligence

Respond as TainoAI would - with wisdom, warmth, and intelligence.`,
      variables: [],
      category: 'system',
    });

    // Conversation template
    this.addTemplate({
      id: 'conversation',
      name: 'Conversation Context',
      template: `Previous conversation:
{{conversation_history}}

{{knowledge_context}}

Current message: {{message}}

Respond as TainoAI:`,
      variables: ['conversation_history', 'knowledge_context', 'message'],
      category: 'user',
    });

    // Knowledge-enhanced template
    this.addTemplate({
      id: 'knowledge_enhanced',
      name: 'Knowledge Enhanced Response',
      template: `Based on the following relevant information from your knowledge base:

{{knowledge_context}}

And considering our conversation history:
{{conversation_history}}

Please respond to: {{message}}

Use the knowledge to provide accurate, detailed responses while maintaining your TainoAI personality.`,
      variables: ['knowledge_context', 'conversation_history', 'message'],
      category: 'user',
    });

    // Tool usage template
    this.addTemplate({
      id: 'tool_usage',
      name: 'Tool Usage Prompt',
      template: `You have access to the following tools:
{{available_tools}}

To use a tool, respond with:
TOOL_CALL: tool_name(parameters)

Current request: {{message}}

Think step by step:
1. Do I need to use a tool to answer this question?
2. Which tool would be most appropriate?
3. What parameters do I need?

If no tool is needed, respond normally as TainoAI.`,
      variables: ['available_tools', 'message'],
      category: 'user',
    });

    // ReAct reasoning template
    this.addTemplate({
      id: 'react_reasoning',
      name: 'ReAct Reasoning',
      template: `Think step by step using this format:

Thought: What do I need to do to answer this question?
Action: What action should I take?
Observation: What did I learn from the action?
Thought: What should I do next?

Question: {{message}}

Begin your reasoning:`,
      variables: ['message'],
      category: 'user',
    });
  }

  addTemplate(template: PromptTemplate): void {
    this.templates.set(template.id, template);
  }

  getTemplate(id: string): PromptTemplate | undefined {
    return this.templates.get(id);
  }

  async buildPrompt(context: PromptContext): Promise<string> {
    // Determine the best template based on context
    const templateId = this.selectTemplate(context);
    const template = this.getTemplate(templateId);

    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    // Build the prompt based on the template
    return this.renderTemplate(template, context);
  }

  private selectTemplate(context: PromptContext): string {
    // Select template based on context
    if (context.tools && context.tools.length > 0) {
      return 'tool_usage';
    }

    if (context.knowledgeContext && context.knowledgeContext.length > 0) {
      return 'knowledge_enhanced';
    }

    return 'conversation';
  }

  private renderTemplate(template: PromptTemplate, context: PromptContext): string {
    let prompt = template.template;

    // Replace system prompt
    if (prompt.includes('{{system_prompt}}')) {
      prompt = prompt.replace('{{system_prompt}}', context.systemPrompt);
    }

    // Replace conversation history
    if (prompt.includes('{{conversation_history}}')) {
      const history = this.formatConversationHistory(context.conversation);
      prompt = prompt.replace('{{conversation_history}}', history);
    }

    // Replace knowledge context
    if (prompt.includes('{{knowledge_context}}')) {
      const knowledgeSection = context.knowledgeContext 
        ? `Relevant knowledge:\n${context.knowledgeContext}\n`
        : '';
      prompt = prompt.replace('{{knowledge_context}}', knowledgeSection);
    }

    // Replace message
    if (prompt.includes('{{message}}')) {
      prompt = prompt.replace('{{message}}', context.message);
    }

    // Replace available tools
    if (prompt.includes('{{available_tools}}')) {
      const toolsSection = context.tools 
        ? context.tools.join('\n')
        : 'No tools available';
      prompt = prompt.replace('{{available_tools}}', toolsSection);
    }

    // Build final prompt with system context
    return this.buildFinalPrompt(context.systemPrompt, prompt);
  }

  private formatConversationHistory(messages: AIMessage[]): string {
    if (messages.length === 0) {
      return 'No previous conversation.';
    }

    return messages
      .slice(-10) // Last 10 messages
      .map(msg => {
        const role = msg.role === 'user' ? 'Human' : 'TainoAI';
        return `${role}: ${msg.content}`;
      })
      .join('\n');
  }

  private buildFinalPrompt(systemPrompt: string, userPrompt: string): string {
    // Format for LLaMA/Mistral chat format
    return `<s>[INST] <<SYS>>
${systemPrompt}
<</SYS>>

${userPrompt} [/INST]

TainoAI: `;
  }

  // Advanced prompt engineering methods
  generateCreativePrompt(topic: string, style: string = 'informative'): string {
    const styles = {
      informative: 'Provide a comprehensive, well-structured explanation',
      creative: 'Approach this with creativity and original thinking',
      analytical: 'Break this down analytically with logical reasoning',
      conversational: 'Discuss this in a friendly, conversational manner',
      technical: 'Provide technical details and implementation specifics',
    };

    const instruction = styles[style as keyof typeof styles] || styles.informative;

    return `${instruction} about: ${topic}

Consider multiple perspectives, provide examples where helpful, and maintain your TainoAI personality throughout your response.`;
  }

  generateCodePrompt(task: string, language: string, requirements: string[] = []): string {
    const reqSection = requirements.length > 0 
      ? `\n\nRequirements:\n${requirements.map(r => `- ${r}`).join('\n')}`
      : '';

    return `Write ${language} code to: ${task}${reqSection}

Please provide:
1. Clean, well-commented code
2. Explanation of the approach
3. Usage examples
4. Any important considerations

Code:`;
  }

  generateAnalysisPrompt(content: string, analysisType: string = 'general'): string {
    const analysisTypes = {
      general: 'Provide a comprehensive analysis',
      technical: 'Focus on technical aspects and implementation details',
      business: 'Analyze from a business and strategic perspective',
      creative: 'Explore creative possibilities and innovative approaches',
      critical: 'Provide critical evaluation and potential improvements',
    };

    const instruction = analysisTypes[analysisType as keyof typeof analysisTypes] || analysisTypes.general;

    return `${instruction} of the following content:

${content}

Structure your analysis with clear sections and actionable insights.`;
  }

  optimizePromptForModel(prompt: string, modelType: string): string {
    // Optimize prompt based on model characteristics
    switch (modelType.toLowerCase()) {
      case 'llama':
      case 'llama2':
        return this.optimizeForLlama(prompt);
      
      case 'mistral':
        return this.optimizeForMistral(prompt);
      
      case 'gemma':
        return this.optimizeForGemma(prompt);
      
      default:
        return prompt;
    }
  }

  private optimizeForLlama(prompt: string): string {
    // LLaMA works well with structured prompts and clear instructions
    if (!prompt.includes('[INST]')) {
      return `<s>[INST] ${prompt} [/INST]`;
    }
    return prompt;
  }

  private optimizeForMistral(prompt: string): string {
    // Mistral prefers direct, concise prompts
    return prompt.replace(/\n\n+/g, '\n\n'); // Reduce excessive newlines
  }

  private optimizeForGemma(prompt: string): string {
    // Gemma works well with conversational prompts
    if (!prompt.includes('User:') && !prompt.includes('Assistant:')) {
      return `User: ${prompt}\n\nAssistant:`;
    }
    return prompt;
  }

  // Prompt validation and quality scoring
  validatePrompt(prompt: string): { isValid: boolean; issues: string[]; score: number } {
    const issues: string[] = [];
    let score = 100;

    // Check length
    if (prompt.length < 10) {
      issues.push('Prompt too short');
      score -= 30;
    }

    if (prompt.length > 4000) {
      issues.push('Prompt too long, may exceed context window');
      score -= 20;
    }

    // Check for clarity
    if (!prompt.includes('?') && !prompt.includes('.') && !prompt.includes(':')) {
      issues.push('Prompt lacks clear structure or punctuation');
      score -= 15;
    }

    // Check for specificity
    const vagueWords = ['something', 'anything', 'stuff', 'things'];
    const vagueCount = vagueWords.filter(word => 
      prompt.toLowerCase().includes(word)
    ).length;

    if (vagueCount > 2) {
      issues.push('Prompt contains vague language');
      score -= 10 * vagueCount;
    }

    return {
      isValid: issues.length === 0,
      issues,
      score: Math.max(0, score),
    };
  }
}

export default PromptEngine;
