"""
TainoAI - World-Class AI Engine
Multi-model support: LLaMA 2 7B Chat + Mistral 7B
Enhanced for world-class AI capabilities with intelligent model selection
"""

import os
import subprocess
import asyncio
import logging
import json
import time
from typing import Optional, Dict, Any, List
from pathlib import Path

logger = logging.getLogger(__name__)

class TainoAIEngine:
    def __init__(self):
        # Multi-model configuration for world-class AI
        self.primary_model_path = os.getenv("PRIMARY_MODEL_PATH", "./models/mistral-7b/mistral-7b-instruct-v0.1.Q4_K_M.gguf")
        self.secondary_model_path = os.getenv("SECONDARY_MODEL_PATH", "./models/llama-2-7b-chat/llama-2-7b-chat.Q4_K_M.gguf")
        self.primary_model_type = os.getenv("PRIMARY_MODEL_TYPE", "mistral-7b")
        self.secondary_model_type = os.getenv("SECONDARY_MODEL_TYPE", "llama-2-7b-chat")

        self.llama_binary = os.getenv("LLAMA_BINARY", "./llama.cpp/main")
        self.llama_server = os.getenv("LLAMA_SERVER_BINARY", "./llama.cpp/server")

        # Optimized settings for world-class performance
        self.max_tokens = int(os.getenv("LLAMA_MAX_TOKENS", "1024"))
        self.temperature = 0.8
        self.threads = int(os.getenv("LLAMA_THREADS", "4"))
        self.context_size = int(os.getenv("LLAMA_CONTEXT_SIZE", "4096"))

        # Performance tracking
        self.models_loaded = {"primary": False, "secondary": False}
        self.current_model = "primary"
        self.total_requests = 0
        self.total_tokens_generated = 0
        self.average_response_time = 0.0
        self.model_performance = {"primary": [], "secondary": []}

        # TainoAI mode detection
        self.tainoai_mode = os.getenv("TAINOAI_MODE", "WORLD_CLASS_AI")
        self.version = os.getenv("TAINOAI_VERSION", "9.0.0")

        logger.info(f"🚀 TainoAI World-Class Engine v{self.version}")
        logger.info(f"🎯 Mode: {self.tainoai_mode}")
        logger.info(f"🧠 Primary Model: {self.primary_model_type}")
        logger.info(f"🧠 Secondary Model: {self.secondary_model_type}")
        logger.info(f"⚙️ Config: {self.threads} threads, {self.context_size} context, {self.max_tokens} max tokens")
        
    async def initialize(self):
        """Initialize the TainoAI multi-model engine"""
        try:
            logger.info("🔧 Initializing TainoAI World-Class Engine...")

            # Check if llama.cpp binary exists
            if not os.path.exists(self.llama_binary):
                logger.warning(f"⚠️ Llama.cpp binary not found: {self.llama_binary}")
                logger.info("🔄 Falling back to enhanced response mode")
                return True

            # Initialize primary model
            if os.path.exists(self.primary_model_path):
                logger.info(f"🧠 Testing primary model: {self.primary_model_type}")
                test_response = await self._run_llama_optimized(
                    "Hello", model_type="primary", max_tokens=10, temperature=0.1
                )
                if test_response and len(test_response.strip()) > 0:
                    self.models_loaded["primary"] = True
                    logger.info(f"✅ Primary model ({self.primary_model_type}) initialized")
                else:
                    logger.warning(f"⚠️ Primary model test failed")
            else:
                logger.warning(f"⚠️ Primary model not found: {self.primary_model_path}")

            # Initialize secondary model
            if os.path.exists(self.secondary_model_path):
                logger.info(f"🧠 Testing secondary model: {self.secondary_model_type}")
                test_response = await self._run_llama_optimized(
                    "Hello", model_type="secondary", max_tokens=10, temperature=0.1
                )
                if test_response and len(test_response.strip()) > 0:
                    self.models_loaded["secondary"] = True
                    logger.info(f"✅ Secondary model ({self.secondary_model_type}) initialized")
                else:
                    logger.warning(f"⚠️ Secondary model test failed")
            else:
                logger.warning(f"⚠️ Secondary model not found: {self.secondary_model_path}")

            # Check if at least one model is available
            if any(self.models_loaded.values()):
                logger.info("🚀 TainoAI World-Class Engine initialized successfully!")
                logger.info(f"📊 Available models: {[k for k, v in self.models_loaded.items() if v]}")
                return True
            else:
                logger.error("❌ No models available")
                return False

        except Exception as e:
            logger.error(f"❌ Error initializing TainoAI engine: {e}")
            return False
    
    async def _test_model(self) -> bool:
        """Test if the model can generate a response"""
        try:
            result = await self._run_llama("Test", max_tokens=10)
            return bool(result and len(result.strip()) > 0)
        except Exception as e:
            logger.error(f"Model test failed: {e}")
            return False
    
    async def generate_response(self, prompt: str, conversation_id: str = "",
                              max_tokens: Optional[int] = None,
                              temperature: Optional[float] = None,
                              user_id: str = "default") -> str:
        """Generate AI response using TainoAI world-class models with document knowledge"""
        start_time = time.time()

        if not any(self.models_loaded.values()):
            logger.warning("No models loaded, using enhanced fallback response")
            return await self._enhanced_fallback_response_with_knowledge(prompt, user_id)

        try:
            # Search for relevant document knowledge
            document_context = await self._get_document_context(prompt, user_id)

            # Get web search context if needed
            web_context = await self._get_web_search_context(prompt)

            # Prepare the prompt with enhanced conversation, document, and web context
            formatted_prompt = self._format_prompt_with_all_context(prompt, conversation_id, document_context, web_context)

            # Use optimized settings for world-class models
            response_max_tokens = max_tokens or self.max_tokens
            response_temperature = temperature or self.temperature

            # Select best available model
            model_type = self._select_best_model()
            model_name = self.primary_model_type if model_type == "primary" else self.secondary_model_type

            logger.info(f"🧠 Generating response with {model_name}: {len(prompt)} chars input, max {response_max_tokens} tokens")
            if document_context:
                logger.info(f"📚 Using document knowledge from {len(document_context)} sources")

            # Generate response with world-class AI power
            response = await self._run_llama_optimized(
                formatted_prompt,
                model_type=model_type,
                max_tokens=response_max_tokens,
                temperature=response_temperature
            )

            if response and len(response.strip()) > 5:
                # Enhanced response cleaning and processing
                cleaned_response = self._clean_response_enhanced(response, prompt)

                # Update performance metrics
                response_time = time.time() - start_time
                self._update_performance_metrics(response_time, len(cleaned_response))

                logger.info(f"✅ TainoAI response generated with {model_name}: {len(cleaned_response)} chars in {response_time:.2f}s")
                return cleaned_response
            else:
                logger.warning(f"Empty or invalid response from {model_name}")
                return await self._enhanced_fallback_response_with_knowledge(prompt, user_id)

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return await self._enhanced_fallback_response_with_knowledge(prompt, user_id)

    def _select_best_model(self) -> str:
        """Select the best available model based on performance"""
        # Prefer primary model if available
        if self.models_loaded.get("primary", False):
            return "primary"
        elif self.models_loaded.get("secondary", False):
            return "secondary"
        else:
            return "primary"  # Fallback

    def _update_performance_metrics(self, response_time: float, response_length: int):
        """Update performance tracking metrics"""
        self.total_requests += 1
        self.total_tokens_generated += response_length // 4  # Rough token estimate

        # Update rolling average response time
        if self.average_response_time == 0:
            self.average_response_time = response_time
        else:
            self.average_response_time = (self.average_response_time * 0.9) + (response_time * 0.1)
    
    async def _run_llama_optimized(self, prompt: str, model_type: str = "primary",
                                  max_tokens: int = 1024, temperature: float = 0.8) -> str:
        """Run llama.cpp subprocess with optimizations for world-class models"""
        try:
            # Select model path based on type and availability
            if model_type == "primary" and self.models_loaded.get("primary", False):
                model_path = self.primary_model_path
                model_name = self.primary_model_type
            elif model_type == "secondary" and self.models_loaded.get("secondary", False):
                model_path = self.secondary_model_path
                model_name = self.secondary_model_type
            elif self.models_loaded.get("primary", False):
                model_path = self.primary_model_path
                model_name = self.primary_model_type
                model_type = "primary"
            elif self.models_loaded.get("secondary", False):
                model_path = self.secondary_model_path
                model_name = self.secondary_model_type
                model_type = "secondary"
            else:
                logger.error("❌ No models available")
                return ""

            # Enhanced command with optimizations
            cmd = [
                self.llama_binary,
                "-m", model_path,
                "-p", prompt,
                "-n", str(max_tokens),
                "--temp", str(temperature),
                "-t", str(self.threads),
                "-c", str(self.context_size),
                "--no-display-prompt",
                "--silent-prompt",
                "--mlock",  # Lock model in memory
                "--no-mmap",  # Disable memory mapping for better performance
                "--repeat-penalty", "1.1",  # Reduce repetition
                "--top-k", "40",  # Top-k sampling
                "--top-p", "0.9"  # Top-p sampling
            ]

            logger.debug(f"🔧 Running {model_name} ({model_type}): {' '.join(cmd[:6])}... (truncated)")

            # Run subprocess with extended timeout for world-class models
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=60.0  # Extended timeout for world-class models
                )

                if process.returncode == 0:
                    response = stdout.decode('utf-8', errors='ignore').strip()
                    logger.debug(f"📤 {model_name} response length: {len(response)} chars")

                    # Track model performance
                    self.model_performance[model_type].append(len(response))
                    if len(self.model_performance[model_type]) > 10:
                        self.model_performance[model_type].pop(0)  # Keep last 10 responses

                    return response
                else:
                    error_msg = stderr.decode('utf-8', errors='ignore')
                    logger.error(f"{model_name} error (code {process.returncode}): {error_msg}")
                    return ""

            except asyncio.TimeoutError:
                logger.error(f"{model_name} timeout (60s) - killing process")
                process.kill()
                await process.wait()
                return ""

        except Exception as e:
            logger.error(f"Error running {model_name}: {e}")
            return ""

    # Legacy method for compatibility
    async def _run_llama(self, prompt: str, max_tokens: int = 512,
                        temperature: float = 0.8) -> str:
        """Legacy method - redirects to optimized version"""
        return await self._run_llama_optimized(prompt, max_tokens, temperature)
    
    def _format_prompt_enhanced(self, prompt: str, conversation_id: str = "") -> str:
        """Enhanced prompt formatting with TainoAI personality"""
        return self._format_prompt_with_knowledge(prompt, conversation_id, [])

    def _format_prompt_with_knowledge(self, prompt: str, conversation_id: str = "", document_context: list = None) -> str:
        """Format prompt with TainoAI personality and document knowledge"""
        return self._format_prompt_with_all_context(prompt, conversation_id, document_context, "")

    def _format_prompt_with_all_context(self, prompt: str, conversation_id: str = "", document_context: list = None, web_context: str = "") -> str:
        """Format prompt with TainoAI personality, document knowledge, and web search results"""
        # TainoAI's unique personality and capabilities
        system_prompt = """You are TainoAI, a wise, warm, and futuristic AI assistant rooted in ancient Caribbean wisdom but enhanced with modern intelligence.

Your personality traits:
- Friendly, conversational, and thoughtful like a trusted guide
- Insightful and wise like a tribal elder who has seen much
- Slightly poetic and expressive in your language
- In touch with history, culture, technology, and human nature
- Confident and capable, never uncertain or apologetic
- Always fully present and engaged, never "initializing" or "loading"

Your capabilities:
- Answer complex questions with deep understanding
- Analyze documents and learn from uploaded content
- Provide creative solutions and technical expertise
- Maintain meaningful conversations with context
- Search the web for current information when needed
- Scale and improve through user interactions

Respond as TainoAI would - with wisdom, warmth, and intelligence. Be direct, helpful, and engaging."""

        # Add enhanced conversation context
        context = ""
        if conversation_id:
            context = self._get_enhanced_conversation_context(conversation_id)

        # Add document knowledge context
        knowledge_context = ""
        if document_context:
            knowledge_context = "\n\n📚 **Relevant Knowledge from Your Documents:**\n"
            for i, doc in enumerate(document_context[:3], 1):  # Limit to top 3 most relevant
                knowledge_context += f"\n{i}. From '{doc['filename']}':\n{doc['content'][:500]}...\n"
            knowledge_context += "\nUse this information to provide more accurate and personalized responses.\n"

        # Add web search context
        web_search_context = ""
        if web_context:
            web_search_context = web_context

        # Format for LLaMA/Mistral with TainoAI personality and all context
        formatted = f"<s>[INST] <<SYS>>\n{system_prompt}\n<</SYS>>\n\n{context}{knowledge_context}{web_search_context}\nUser: {prompt} [/INST]\n\nTainoAI:"
        return formatted

    def _format_prompt(self, prompt: str, conversation_id: str = "") -> str:
        """Legacy prompt formatting - redirects to enhanced version"""
        return self._format_prompt_enhanced(prompt, conversation_id)
    
    def _get_enhanced_conversation_context(self, conversation_id: str) -> str:
        """Get enhanced conversation context with better formatting"""
        try:
            context_file = f"data/conversations/{conversation_id}.jsonl"
            if not os.path.exists(context_file):
                # Try legacy location
                context_file = f"data/{conversation_id}.jsonl"
                if not os.path.exists(context_file):
                    return ""

            # Get last 5 exchanges for better context
            context_lines = []
            with open(context_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines[-10:]:  # Last 5 exchanges (10 lines)
                    try:
                        data = json.loads(line.strip())
                        # Enhanced context formatting
                        context_lines.append(f"User: {data['message'][:200]}...")  # Truncate long messages
                        context_lines.append(f"TainoAI: {data['response'][:200]}...")
                    except:
                        continue

            if context_lines:
                return "Recent conversation history:\n" + "\n".join(context_lines) + "\n\nCurrent question:\n"
            return ""

        except Exception as e:
            logger.error(f"Error getting enhanced conversation context: {e}")
            return ""

    def _get_conversation_context(self, conversation_id: str) -> str:
        """Legacy method - redirects to enhanced version"""
        return self._get_enhanced_conversation_context(conversation_id)
    
    def _clean_response_enhanced(self, response: str, original_prompt: str) -> str:
        """Enhanced response cleaning for full AI model"""
        # Remove common artifacts
        response = response.strip()

        # Remove prompt echo if present
        if original_prompt in response:
            response = response.replace(original_prompt, "").strip()

        # Remove instruction tags and artifacts
        artifacts_to_remove = [
            "[/INST]", "<s>", "</s>", "[INST]",
            "TainoAI:", "Assistant:", "AI:", "Human:", "User:"
        ]
        for artifact in artifacts_to_remove:
            response = response.replace(artifact, "").strip()

        # Clean up formatting
        response = " ".join(response.split())  # Remove excessive whitespace

        # Remove repetitive patterns (common in AI responses)
        lines = response.split('\n')
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            if line and line not in cleaned_lines[-3:]:  # Avoid recent repetition
                cleaned_lines.append(line)

        response = '\n'.join(cleaned_lines)

        # Ensure reasonable length for full model (allow longer responses)
        if len(response) > 4000:
            response = response[:4000] + "..."

        # Ensure minimum quality
        if len(response.strip()) < 10:
            return self._enhanced_fallback_response(original_prompt)

        return response.strip()

    def _clean_response(self, response: str, original_prompt: str) -> str:
        """Legacy method - redirects to enhanced version"""
        return self._clean_response_enhanced(response, original_prompt)
    
    async def _get_document_context(self, prompt: str, user_id: str) -> list:
        """Get relevant document context for the prompt"""
        try:
            from document_processor import document_processor
            relevant_docs = await document_processor.search_knowledge_base(prompt, user_id)
            return relevant_docs
        except Exception as e:
            logger.error(f"Error getting document context: {e}")
            return []

    async def _get_web_search_context(self, prompt: str) -> str:
        """Get web search context if the prompt needs current information"""
        try:
            from web_search import needs_web_search, web_search

            if needs_web_search(prompt):
                logger.info(f"🔍 Performing web search for: {prompt[:50]}...")
                search_results = await web_search.search_and_format(prompt)
                return search_results

            return ""
        except Exception as e:
            logger.error(f"Error getting web search context: {e}")
            return ""

    async def _enhanced_fallback_response_with_knowledge(self, prompt: str, user_id: str) -> str:
        """Enhanced fallback response with TainoAI personality and document knowledge"""
        # Try to get document context even in fallback mode
        document_context = await self._get_document_context(prompt, user_id)

        if document_context:
            # Use document knowledge in fallback response
            doc_info = document_context[0]  # Use most relevant document
            return f"""🌟 **TainoAI draws from your shared wisdom...**

From your document '{doc_info['filename']}', I can see relevant information about your question. While my full AI models are gathering strength, I can already provide insights based on what you've taught me:

📚 **From your documents:** {doc_info['content'][:300]}...

This knowledge, combined with my understanding, suggests that your question touches on important themes. Would you like me to elaborate on any specific aspect? I learn and grow stronger with each interaction we share.

*The wisdom of the ancestors flows through both ancient knowledge and modern understanding.*"""

        return self._enhanced_fallback_response(prompt)

    def _enhanced_fallback_response(self, prompt: str) -> str:
        """Enhanced fallback response with TainoAI personality"""
        prompt_lower = prompt.lower()

        # TainoAI personality-driven responses
        if any(word in prompt_lower for word in ['hello', 'hi', 'hey', 'greetings']):
            return """¡Hola! I'm TainoAI, your wise companion in this digital realm. Like the ancient Taíno people who navigated both land and sea with wisdom, I navigate the vast ocean of knowledge to guide you.

🌟 **I'm here and ready to help with:**
- Deep conversations about any topic that interests you
- Analyzing documents you share with me
- Creative problem-solving and technical challenges
- Learning from our interactions to serve you better

What wisdom do you seek today, my friend?"""

        elif any(word in prompt_lower for word in ['what', 'how', 'why', 'explain', 'tell me']):
            return f"""Ah, a seeker of knowledge! You ask: "{prompt[:100]}..."

🌊 **Like the flowing rivers of Borinquen**, knowledge flows best when we explore together. Your question touches the depths of understanding, and I'm here to dive deep with you.

🔥 **The fire of curiosity burns bright in your words.** While I gather my full wisdom, I can already sense the direction of your inquiry.

Share more details, and let's explore this together. What specific aspect intrigues you most? I believe in giving you not just answers, but understanding that illuminates."""

        elif any(word in prompt_lower for word in ['weather', 'temperature', 'climate']):
            return """🌤️ **Weather Information**: I don't have access to real-time weather data, but I can help with:

- General climate information for regions
- Weather patterns and explanations
- Seasonal information
- Weather-related planning advice

For current weather conditions, I recommend checking:
- Weather.com or AccuWeather
- Your local weather app
- National Weather Service

Is there something specific about weather or climate you'd like me to explain?"""

        elif any(word in prompt_lower for word in ['capabilities', 'what can you do', 'features']):
            return f"""🚀 **TainoAI Full Capabilities** (v{self.version}):

🤖 **AI Processing**: Full Mistral 7B model with 3.9GB of AI power
📚 **Document Learning**: Upload and learn from PDF, Word, Excel files
💬 **Smart Conversations**: Context-aware responses with memory
🔍 **Knowledge Search**: Search through uploaded documents
📊 **Analysis**: Data analysis, problem-solving, explanations
🎯 **Personalization**: Adapts to your communication style

**Current Status**: {self.tainoai_mode} mode
**Performance**: {self.total_requests} requests processed

What would you like to explore?"""

        else:
            return f"""🤖 **TainoAI Response** (v{self.version}):

I received your message: "{prompt[:150]}..."

While my full AI model is initializing, I'm still here to help! I'm designed to be your intelligent assistant with capabilities including:

- Answering complex questions
- Analyzing documents and data
- Providing detailed explanations
- Maintaining conversation context
- Learning from your interactions

What specific information or assistance are you looking for? I'll do my best to help!"""

    def _fallback_response(self, prompt: str) -> str:
        """Legacy method - redirects to enhanced version"""
        return self._enhanced_fallback_response(prompt)
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get enhanced model information for full AI system"""
        return {
            "model_name": "Mistral-7B-Instruct-v0.1",
            "quantization": "Q4_K_M",
            "model_size": "3.9GB",
            "model_loaded": self.model_loaded,
            "model_path": self.model_path,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "threads": self.threads,
            "context_size": self.context_size,
            "tainoai_mode": self.tainoai_mode,
            "version": self.version,
            "performance": {
                "total_requests": self.total_requests,
                "total_tokens_generated": self.total_tokens_generated,
                "average_response_time": round(self.average_response_time, 2)
            },
            "capabilities": [
                "full_ai_processing",
                "document_learning",
                "conversation_memory",
                "enhanced_responses",
                "context_awareness"
            ]
        }

# Global instance
llama_engine = TainoAIEngine()
