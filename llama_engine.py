"""
TainoAI - Lightweight Llama.cpp Integration
Mistral 7B Q4 quantized model for Heroku deployment
"""

import os
import subprocess
import asyncio
import logging
import json
from typing import Optional, Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class LlamaEngine:
    def __init__(self):
        self.model_path = os.getenv("MODEL_PATH", "./models/mistral-7b-instruct-v0.1.Q4_K_M.gguf")
        self.llama_binary = os.getenv("LLAMA_BINARY", "./llama.cpp/main")
        self.max_tokens = 512
        self.temperature = 0.8
        self.threads = int(os.getenv("LLAMA_THREADS", "2"))
        self.context_size = 2048
        self.model_loaded = False
        
    async def initialize(self):
        """Initialize the Llama engine"""
        try:
            # Check if model exists
            if not os.path.exists(self.model_path):
                logger.warning(f"Model not found at {self.model_path}")
                return False
                
            # Check if llama binary exists
            if not os.path.exists(self.llama_binary):
                logger.warning(f"Llama binary not found at {self.llama_binary}")
                return False
                
            # Test model loading
            test_result = await self._test_model()
            if test_result:
                self.model_loaded = True
                logger.info("✅ Llama.cpp model loaded successfully")
                return True
            else:
                logger.error("❌ Failed to load Llama.cpp model")
                return False
                
        except Exception as e:
            logger.error(f"Error initializing Llama engine: {e}")
            return False
    
    async def _test_model(self) -> bool:
        """Test if the model can generate a response"""
        try:
            result = await self._run_llama("Test", max_tokens=10)
            return bool(result and len(result.strip()) > 0)
        except Exception as e:
            logger.error(f"Model test failed: {e}")
            return False
    
    async def generate_response(self, prompt: str, conversation_id: str = "", 
                              max_tokens: Optional[int] = None,
                              temperature: Optional[float] = None) -> str:
        """Generate AI response using Llama.cpp"""
        if not self.model_loaded:
            logger.warning("Model not loaded, using fallback response")
            return self._fallback_response(prompt)
        
        try:
            # Prepare the prompt with conversation context
            formatted_prompt = self._format_prompt(prompt, conversation_id)
            
            # Generate response
            response = await self._run_llama(
                formatted_prompt,
                max_tokens=max_tokens or self.max_tokens,
                temperature=temperature or self.temperature
            )
            
            if response:
                # Clean up the response
                cleaned_response = self._clean_response(response, prompt)
                logger.info(f"✅ Generated response ({len(cleaned_response)} chars)")
                return cleaned_response
            else:
                logger.warning("Empty response from Llama.cpp")
                return self._fallback_response(prompt)
                
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return self._fallback_response(prompt)
    
    async def _run_llama(self, prompt: str, max_tokens: int = 512, 
                        temperature: float = 0.8) -> str:
        """Run llama.cpp subprocess"""
        try:
            # Prepare command
            cmd = [
                self.llama_binary,
                "-m", self.model_path,
                "-p", prompt,
                "-n", str(max_tokens),
                "--temp", str(temperature),
                "-t", str(self.threads),
                "-c", str(self.context_size),
                "--no-display-prompt",
                "--silent-prompt"
            ]
            
            # Run subprocess with timeout
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=30.0  # 30 second timeout
                )
                
                if process.returncode == 0:
                    response = stdout.decode('utf-8', errors='ignore').strip()
                    return response
                else:
                    error_msg = stderr.decode('utf-8', errors='ignore')
                    logger.error(f"Llama.cpp error: {error_msg}")
                    return ""
                    
            except asyncio.TimeoutError:
                logger.error("Llama.cpp timeout")
                process.kill()
                return ""
                
        except Exception as e:
            logger.error(f"Error running llama.cpp: {e}")
            return ""
    
    def _format_prompt(self, prompt: str, conversation_id: str = "") -> str:
        """Format prompt for Mistral 7B Instruct"""
        # Mistral 7B Instruct format
        system_prompt = "You are TainoAI, a helpful and intelligent AI assistant. Provide clear, accurate, and helpful responses."
        
        # Add conversation context if available
        context = ""
        if conversation_id:
            context = self._get_conversation_context(conversation_id)
        
        # Format for Mistral Instruct
        formatted = f"<s>[INST] {system_prompt}\n\n{context}{prompt} [/INST]"
        return formatted
    
    def _get_conversation_context(self, conversation_id: str) -> str:
        """Get recent conversation context"""
        try:
            context_file = f"data/{conversation_id}.jsonl"
            if not os.path.exists(context_file):
                return ""
            
            # Get last 3 exchanges for context
            context_lines = []
            with open(context_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines[-6:]:  # Last 3 exchanges (6 lines)
                    try:
                        data = json.loads(line.strip())
                        context_lines.append(f"Human: {data['message']}")
                        context_lines.append(f"Assistant: {data['response']}")
                    except:
                        continue
            
            if context_lines:
                return "Previous conversation:\n" + "\n".join(context_lines) + "\n\nCurrent question: "
            return ""
            
        except Exception as e:
            logger.error(f"Error getting conversation context: {e}")
            return ""
    
    def _clean_response(self, response: str, original_prompt: str) -> str:
        """Clean up the model response"""
        # Remove common artifacts
        response = response.strip()
        
        # Remove prompt echo if present
        if original_prompt in response:
            response = response.replace(original_prompt, "").strip()
        
        # Remove instruction tags
        response = response.replace("[/INST]", "").replace("<s>", "").replace("</s>", "")
        
        # Remove excessive whitespace
        response = " ".join(response.split())
        
        # Ensure reasonable length
        if len(response) > 2000:
            response = response[:2000] + "..."
        
        return response.strip()
    
    def _fallback_response(self, prompt: str) -> str:
        """Fallback response when model is not available"""
        prompt_lower = prompt.lower()
        
        # Smart fallback responses
        if any(word in prompt_lower for word in ['hello', 'hi', 'hey']):
            return "Hello! I'm TainoAI. I'm currently running in lightweight mode. How can I help you today?"
        
        elif any(word in prompt_lower for word in ['what', 'how', 'why', 'explain']):
            return f"I understand you're asking about '{prompt}'. I'm currently running in lightweight mode, but I'd be happy to help! Could you provide more specific details about what you'd like to know?"
        
        elif 'weather' in prompt_lower:
            return "I'm currently running in lightweight mode and don't have access to real-time weather data. For current weather information, I recommend checking a weather service like Weather.com or your local weather app."
        
        else:
            return f"I'm TainoAI running in lightweight mode. I received your message: '{prompt}'. While I don't have my full AI model loaded right now, I'm still here to help! What specific information are you looking for?"
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        return {
            "model_name": "Mistral-7B-Instruct-v0.1",
            "quantization": "Q4_K_M",
            "model_loaded": self.model_loaded,
            "model_path": self.model_path,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "threads": self.threads,
            "context_size": self.context_size
        }

# Global instance
llama_engine = LlamaEngine()
