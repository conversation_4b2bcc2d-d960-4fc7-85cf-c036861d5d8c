"""
TainoAI - World-Class AI Engine
Multi-model support: LLaMA 2 7B Chat + Mistral 7B + <PERSON>
Enhanced for world-class AI capabilities with intelligent model selection
"""

import os
import subprocess
import asyncio
import logging
import json
import time
from typing import Optional, Dict, Any, List
from pathlib import Path
from claude_engine import claude_engine

logger = logging.getLogger(__name__)

class TainoAIEngine:
    def __init__(self):
        # Multi-model configuration for world-class AI
        self.primary_model_path = os.getenv("PRIMARY_MODEL_PATH", "./models/mistral-7b/mistral-7b-instruct-v0.1.Q4_K_M.gguf")
        self.secondary_model_path = os.getenv("SECONDARY_MODEL_PATH", "./models/llama-2-7b-chat/llama-2-7b-chat.Q4_K_M.gguf")
        self.primary_model_type = os.getenv("PRIMARY_MODEL_TYPE", "mistral-7b")
        self.secondary_model_type = os.getenv("SECONDARY_MODEL_TYPE", "llama-2-7b-chat")
        
        # Claude API configuration
        self.use_claude = bool(os.getenv("CLAUDE_API_KEY"))
        if self.use_claude:
            logger.info("✨ Claude API enabled as fallback model")

    async def generate(self, prompt: str, max_tokens: int = 100, temperature: float = 0.7) -> str:
        """
        Generate text using the selected AI models with the given prompt.
        This method intelligently selects between the primary, secondary, and Claude models
        based on availability and configuration.
        """
        logger.info(f"Generating text with prompt: {prompt[:50]}...")  # Log the prompt (truncated)
        loop = asyncio.get_event_loop()

        # Try primary model (Mistral) first
        try:
            logger.info("Using primary model (Mistral) for generation.")
            response = await loop.run_in_executor(None, self.run_primary_model, prompt, max_tokens, temperature)
            if response:
                return response
        except Exception as e:
            logger.error(f"Error using primary model: {e}")

        # If primary model fails or response is empty, try secondary model (LLaMA)
        try:
            logger.info("Using secondary model (LLaMA) for generation.")
            response = await loop.run_in_executor(None, self.run_secondary_model, prompt, max_tokens, temperature)
            if response:
                return response
        except Exception as e:
            logger.error(f"Error using secondary model: {e}")

        # If both models fail, use Claude API as a fallback
        if self.use_claude:
            try:
                logger.info("Using Claude API for generation.")
                response = await loop.run_in_executor(None, self.run_claude_api, prompt, max_tokens, temperature)
                if response:
                    return response
            except Exception as e:
                logger.error(f"Error using Claude API: {e}")

        logger.warning("All models and APIs failed to generate a response.")
        return "Error: Unable to generate response."

    def run_primary_model(self, prompt: str, max_tokens: int, temperature: float) -> str:
        # ...existing code for running the primary model...
        pass

    def run_secondary_model(self, prompt: str, max_tokens: int, temperature: float) -> str:
        # ...existing code for running the secondary model...
        pass

    def run_claude_api(self, prompt: str, max_tokens: int, temperature: float) -> str:
        """
        Run the Claude API for text generation.
        This method assumes that the Claude API client is properly configured
        and authenticated using the CLAUDE_API_KEY environment variable.
        """
        try:
            response = claude_engine.generate(
                prompt=prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=1.0,
                frequency_penalty=0.0,
                presence_penalty=0.0
            )
            return response['choices'][0]['text'].strip()
        except Exception as e:
            logger.error(f"Claude API error: {e}")
            return ""

    async def generate_response(self, prompt: str, conversation_id: str = "",
                              max_tokens: Optional[int] = None,
                              temperature: Optional[float] = None,
                              user_id: str = "default") -> str:
        """Generate AI response using TainoAI world-class models with document knowledge"""
        start_time = time.time()

        # Initialize context variables with default values
        document_context = []
        web_context = ""

        # TainoAI v1.0.11 - FORCE MODEL LOADING if not available
        if not any(self.models_loaded.values()):
            logger.info("🔥 TainoAI - Force loading models for LIVE AI response")
            await self._force_model_initialization()

        # If models still not available and Claude is configured, use Claude API
        if not any(self.models_loaded.values()) and self.use_claude:
            logger.info("🤖 TainoAI - Using Claude API as fallback")
            try:
                response = await claude_engine.generate_response(
                    prompt=self._format_prompt_with_all_context(prompt, conversation_id, document_context, web_context),
                    max_tokens=max_tokens,
                    temperature=temperature
                )
                if response:
                    logger.info("✅ Claude API response generated successfully")
                    return response
            except Exception as e:
                logger.error(f"Claude API fallback failed: {e}")

        # If Claude fails or is not available, proceed with direct LLaMA execution
        logger.info("🚀 TainoAI - Direct LLaMA execution mode")
        
        # ...existing code for direct LLaMA execution...