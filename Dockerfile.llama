# TainoAI with <PERSON>lam<PERSON>.cpp - Lightweight Heroku Build
# Optimized for <500MB with Mistral 7B Q4

# Stage 1: Build llama.cpp
FROM ubuntu:22.04 AS llama-builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Clone and build llama.cpp
WORKDIR /build
RUN git clone https://github.com/ggerganov/llama.cpp.git
WORKDIR /build/llama.cpp
RUN make -j$(nproc) main

# Download Mistral 7B Q4 model (lightweight)
RUN mkdir -p /models
RUN wget -O /models/mistral-7b-instruct-v0.1.Q4_K_M.gguf \
    "https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.1-GGUF/resolve/main/mistral-7b-instruct-v0.1.Q4_K_M.gguf"

# Stage 2: Build React Frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./
COPY next.config.js ./
COPY tailwind.config.js ./
COPY postcss.config.js ./

# Install dependencies
RUN npm ci --only=production --no-audit --no-fund

# Copy source code
COPY app/ ./app/
COPY components/ ./components/
COPY lib/ ./lib/

# Build and export static frontend
RUN npm run build && npm run export

# Stage 3: Production Runtime
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy requirements and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy llama.cpp binary and model
COPY --from=llama-builder /build/llama.cpp/main ./llama.cpp/main
COPY --from=llama-builder /models ./models

# Make llama.cpp executable
RUN chmod +x ./llama.cpp/main

# Copy built frontend
COPY --from=frontend-builder /app/out/ ./static/

# Copy backend code
COPY main.py ./
COPY llama_engine.py ./
COPY document_processor.py ./

# Create data directory
RUN mkdir -p data

# Set environment variables
ENV MODEL_PATH=./models/mistral-7b-instruct-v0.1.Q4_K_M.gguf
ENV LLAMA_BINARY=./llama.cpp/main
ENV LLAMA_THREADS=2
ENV PORT=8000
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the application
CMD ["python", "main.py"]
