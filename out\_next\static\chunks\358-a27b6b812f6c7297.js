"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[358],{2898:function(t,e,r){r.d(e,{Z:function(){return s}});var i=r(2265),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),s=(t,e)=>{let r=(0,i.forwardRef)(({color:r="currentColor",size:s=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:d,...c},h)=>(0,i.createElement)("svg",{ref:h,...n,width:s,height:s,stroke:r,strokeWidth:l?24*Number(a)/Number(s):a,className:["lucide",`lucide-${o(t)}`,u].join(" "),...c},[...e.map(([t,e])=>(0,i.createElement)(t,e)),...Array.isArray(d)?d:[d]]));return r.displayName=`${t}`,r}},3966:function(t,e,r){r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},6637:function(t,e,r){r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},3088:function(t,e,r){r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},5432:function(t,e,r){r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},6020:function(t,e,r){r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},2851:function(t,e,r){r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},4135:function(t,e,r){r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},1138:function(t,e,r){r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},1541:function(t,e,r){r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},7972:function(t,e,r){r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},622:function(t,e,r){var i=r(2265),n=Symbol.for("react.element"),o=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,a=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(t,e,r){var i,o={},u=null,d=null;for(i in void 0!==r&&(u=""+r),void 0!==e.key&&(u=""+e.key),void 0!==e.ref&&(d=e.ref),e)s.call(e,i)&&!l.hasOwnProperty(i)&&(o[i]=e[i]);if(t&&t.defaultProps)for(i in e=t.defaultProps)void 0===o[i]&&(o[i]=e[i]);return{$$typeof:n,type:t,key:u,ref:d,props:o,_owner:a.current}}e.Fragment=o,e.jsx=u,e.jsxs=u},7437:function(t,e,r){t.exports=r(622)},9808:function(t,e,r){/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var i=r(2265),n="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},o=i.useState,s=i.useEffect,a=i.useLayoutEffect,l=i.useDebugValue;function u(t){var e=t.getSnapshot;t=t.value;try{var r=e();return!n(t,r)}catch(t){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,e){var r=e(),i=o({inst:{value:r,getSnapshot:e}}),n=i[0].inst,d=i[1];return a(function(){n.value=r,n.getSnapshot=e,u(n)&&d({inst:n})},[t,r,e]),s(function(){return u(n)&&d({inst:n}),t(function(){u(n)&&d({inst:n})})},[t]),l(r),r};e.useSyncExternalStore=void 0!==i.useSyncExternalStore?i.useSyncExternalStore:d},6272:function(t,e,r){t.exports=r(9808)},1465:function(t,e,r){r.d(e,{NY:function(){return P},Ee:function(){return S},fC:function(){return T}});var i=r(2265),n=r(6989),o=r(6459),s=r(1030),a=r(9381),l=r(6272);function u(){return()=>{}}var d=r(7437),c="Avatar",[h,p]=(0,n.b)(c),[m,f]=h(c),g=i.forwardRef((t,e)=>{let{__scopeAvatar:r,...n}=t,[o,s]=i.useState("idle");return(0,d.jsx)(m,{scope:r,imageLoadingStatus:o,onImageLoadingStatusChange:s,children:(0,d.jsx)(a.WV.span,{...n,ref:e})})});g.displayName=c;var y="AvatarImage",v=i.forwardRef((t,e)=>{let{__scopeAvatar:r,src:n,onLoadingStatusChange:c=()=>{},...h}=t,p=f(y,r),m=function(t,{referrerPolicy:e,crossOrigin:r}){let n=(0,l.useSyncExternalStore)(u,()=>!0,()=>!1),o=i.useRef(null),a=n?(o.current||(o.current=new window.Image),o.current):null,[d,c]=i.useState(()=>w(a,t));return(0,s.b)(()=>{c(w(a,t))},[a,t]),(0,s.b)(()=>{let t=t=>()=>{c(t)};if(!a)return;let i=t("loaded"),n=t("error");return a.addEventListener("load",i),a.addEventListener("error",n),e&&(a.referrerPolicy=e),"string"==typeof r&&(a.crossOrigin=r),()=>{a.removeEventListener("load",i),a.removeEventListener("error",n)}},[a,r,e]),d}(n,h),g=(0,o.W)(t=>{c(t),p.onImageLoadingStatusChange(t)});return(0,s.b)(()=>{"idle"!==m&&g(m)},[m,g]),"loaded"===m?(0,d.jsx)(a.WV.img,{...h,ref:e,src:n}):null});v.displayName=y;var b="AvatarFallback",x=i.forwardRef((t,e)=>{let{__scopeAvatar:r,delayMs:n,...o}=t,s=f(b,r),[l,u]=i.useState(void 0===n);return i.useEffect(()=>{if(void 0!==n){let t=window.setTimeout(()=>u(!0),n);return()=>window.clearTimeout(t)}},[n]),l&&"loaded"!==s.imageLoadingStatus?(0,d.jsx)(a.WV.span,{...o,ref:e}):null});function w(t,e){return t?e?(t.src!==e&&(t.src=e),t.complete&&t.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=b;var T=g,S=v,P=x},2210:function(t,e,r){r.d(e,{F:function(){return o},e:function(){return s}});var i=r(2265);function n(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}function o(...t){return e=>{let r=!1,i=t.map(t=>{let i=n(t,e);return r||"function"!=typeof i||(r=!0),i});if(r)return()=>{for(let e=0;e<i.length;e++){let r=i[e];"function"==typeof r?r():n(t[e],null)}}}}function s(...t){return i.useCallback(o(...t),t)}},6989:function(t,e,r){r.d(e,{b:function(){return o}});var i=r(2265),n=r(7437);function o(t,e=[]){let r=[],o=()=>{let e=r.map(t=>i.createContext(t));return function(r){let n=r?.[t]||e;return i.useMemo(()=>({[`__scope${t}`]:{...r,[t]:n}}),[r,n])}};return o.scopeName=t,[function(e,o){let s=i.createContext(o),a=r.length;r=[...r,o];let l=e=>{let{scope:r,children:o,...l}=e,u=r?.[t]?.[a]||s,d=i.useMemo(()=>l,Object.values(l));return(0,n.jsx)(u.Provider,{value:d,children:o})};return l.displayName=e+"Provider",[l,function(r,n){let l=n?.[t]?.[a]||s,u=i.useContext(l);if(u)return u;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${e}\``)}]},function(...t){let e=t[0];if(1===t.length)return e;let r=()=>{let r=t.map(t=>({useScope:t(),scopeName:t.scopeName}));return function(t){let n=r.reduce((e,{useScope:r,scopeName:i})=>{let n=r(t)[`__scope${i}`];return{...e,...n}},{});return i.useMemo(()=>({[`__scope${e.scopeName}`]:n}),[n])}};return r.scopeName=e.scopeName,r}(o,...e)]}},9381:function(t,e,r){r.d(e,{WV:function(){return s}});var i=r(2265);r(4887);var n=r(7256),o=r(7437),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((t,e)=>{let r=(0,n.Z8)(`Primitive.${e}`),s=i.forwardRef((t,i)=>{let{asChild:n,...s}=t,a=n?r:e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(a,{...s,ref:i})});return s.displayName=`Primitive.${e}`,{...t,[e]:s}},{})},88:function(t,e,r){r.d(e,{Ns:function(){return q},fC:function(){return X},gb:function(){return S},q4:function(){return L},l_:function(){return Z}});var i=r(2265),n=r(9381),o=r(2210),s=r(1030),a=t=>{let e,r;let{present:n,children:a}=t,u=function(t){var e,r;let[n,o]=i.useState(),a=i.useRef(null),u=i.useRef(t),d=i.useRef("none"),[c,h]=(e=t?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((t,e)=>r[t][e]??t,e));return i.useEffect(()=>{let t=l(a.current);d.current="mounted"===c?t:"none"},[c]),(0,s.b)(()=>{let e=a.current,r=u.current;if(r!==t){let i=d.current,n=l(e);t?h("MOUNT"):"none"===n||e?.display==="none"?h("UNMOUNT"):r&&i!==n?h("ANIMATION_OUT"):h("UNMOUNT"),u.current=t}},[t,h]),(0,s.b)(()=>{if(n){let t;let e=n.ownerDocument.defaultView??window,r=r=>{let i=l(a.current).includes(r.animationName);if(r.target===n&&i&&(h("ANIMATION_END"),!u.current)){let r=n.style.animationFillMode;n.style.animationFillMode="forwards",t=e.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},i=t=>{t.target===n&&(d.current=l(a.current))};return n.addEventListener("animationstart",i),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{e.clearTimeout(t),n.removeEventListener("animationstart",i),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}h("ANIMATION_END")},[n,h]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:i.useCallback(t=>{a.current=t?getComputedStyle(t):null,o(t)},[])}}(n),d="function"==typeof a?a({present:u.isPresent}):i.Children.only(a),c=(0,o.e)(u.ref,(e=Object.getOwnPropertyDescriptor(d.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?d.ref:(e=Object.getOwnPropertyDescriptor(d,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?d.props.ref:d.props.ref||d.ref);return"function"==typeof a||u.isPresent?i.cloneElement(d,{ref:c}):null};function l(t){return t?.animationName||"none"}a.displayName="Presence";var u=r(6989),d=r(6459),c=r(7437),h=i.createContext(void 0);function p(t,e,{checkForDefaultPrevented:r=!0}={}){return function(i){if(t?.(i),!1===r||!i.defaultPrevented)return e?.(i)}}var m="ScrollArea",[f,g]=(0,u.b)(m),[y,v]=f(m),b=i.forwardRef((t,e)=>{let{__scopeScrollArea:r,type:s="hover",dir:a,scrollHideDelay:l=600,...u}=t,[d,p]=i.useState(null),[m,f]=i.useState(null),[g,v]=i.useState(null),[b,x]=i.useState(null),[w,T]=i.useState(null),[S,P]=i.useState(0),[E,k]=i.useState(0),[A,M]=i.useState(!1),[C,R]=i.useState(!1),D=(0,o.e)(e,t=>p(t)),V=function(t){let e=i.useContext(h);return t||e||"ltr"}(a);return(0,c.jsx)(y,{scope:r,type:s,dir:V,scrollHideDelay:l,scrollArea:d,viewport:m,onViewportChange:f,content:g,onContentChange:v,scrollbarX:b,onScrollbarXChange:x,scrollbarXEnabled:A,onScrollbarXEnabledChange:M,scrollbarY:w,onScrollbarYChange:T,scrollbarYEnabled:C,onScrollbarYEnabledChange:R,onCornerWidthChange:P,onCornerHeightChange:k,children:(0,c.jsx)(n.WV.div,{dir:V,...u,ref:D,style:{position:"relative","--radix-scroll-area-corner-width":S+"px","--radix-scroll-area-corner-height":E+"px",...t.style}})})});b.displayName=m;var x="ScrollAreaViewport",w=i.forwardRef((t,e)=>{let{__scopeScrollArea:r,children:s,nonce:a,...l}=t,u=v(x,r),d=i.useRef(null),h=(0,o.e)(e,d,u.onViewportChange);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,c.jsx)(n.WV.div,{"data-radix-scroll-area-viewport":"",...l,ref:h,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...t.style},children:(0,c.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:s})})]})});w.displayName=x;var T="ScrollAreaScrollbar",S=i.forwardRef((t,e)=>{let{forceMount:r,...n}=t,o=v(T,t.__scopeScrollArea),{onScrollbarXEnabledChange:s,onScrollbarYEnabledChange:a}=o,l="horizontal"===t.orientation;return i.useEffect(()=>(l?s(!0):a(!0),()=>{l?s(!1):a(!1)}),[l,s,a]),"hover"===o.type?(0,c.jsx)(P,{...n,ref:e,forceMount:r}):"scroll"===o.type?(0,c.jsx)(E,{...n,ref:e,forceMount:r}):"auto"===o.type?(0,c.jsx)(k,{...n,ref:e,forceMount:r}):"always"===o.type?(0,c.jsx)(A,{...n,ref:e}):null});S.displayName=T;var P=i.forwardRef((t,e)=>{let{forceMount:r,...n}=t,o=v(T,t.__scopeScrollArea),[s,l]=i.useState(!1);return i.useEffect(()=>{let t=o.scrollArea,e=0;if(t){let r=()=>{window.clearTimeout(e),l(!0)},i=()=>{e=window.setTimeout(()=>l(!1),o.scrollHideDelay)};return t.addEventListener("pointerenter",r),t.addEventListener("pointerleave",i),()=>{window.clearTimeout(e),t.removeEventListener("pointerenter",r),t.removeEventListener("pointerleave",i)}}},[o.scrollArea,o.scrollHideDelay]),(0,c.jsx)(a,{present:r||s,children:(0,c.jsx)(k,{"data-state":s?"visible":"hidden",...n,ref:e})})}),E=i.forwardRef((t,e)=>{var r;let{forceMount:n,...o}=t,s=v(T,t.__scopeScrollArea),l="horizontal"===t.orientation,u=H(()=>h("SCROLL_END"),100),[d,h]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},i.useReducer((t,e)=>r[t][e]??t,"hidden"));return i.useEffect(()=>{if("idle"===d){let t=window.setTimeout(()=>h("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(t)}},[d,s.scrollHideDelay,h]),i.useEffect(()=>{let t=s.viewport,e=l?"scrollLeft":"scrollTop";if(t){let r=t[e],i=()=>{let i=t[e];r!==i&&(h("SCROLL"),u()),r=i};return t.addEventListener("scroll",i),()=>t.removeEventListener("scroll",i)}},[s.viewport,l,h,u]),(0,c.jsx)(a,{present:n||"hidden"!==d,children:(0,c.jsx)(A,{"data-state":"hidden"===d?"hidden":"visible",...o,ref:e,onPointerEnter:p(t.onPointerEnter,()=>h("POINTER_ENTER")),onPointerLeave:p(t.onPointerLeave,()=>h("POINTER_LEAVE"))})})}),k=i.forwardRef((t,e)=>{let r=v(T,t.__scopeScrollArea),{forceMount:n,...o}=t,[s,l]=i.useState(!1),u="horizontal"===t.orientation,d=H(()=>{if(r.viewport){let t=r.viewport.offsetWidth<r.viewport.scrollWidth,e=r.viewport.offsetHeight<r.viewport.scrollHeight;l(u?t:e)}},10);return Y(r.viewport,d),Y(r.content,d),(0,c.jsx)(a,{present:n||s,children:(0,c.jsx)(A,{"data-state":s?"visible":"hidden",...o,ref:e})})}),A=i.forwardRef((t,e)=>{let{orientation:r="vertical",...n}=t,o=v(T,t.__scopeScrollArea),s=i.useRef(null),a=i.useRef(0),[l,u]=i.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=z(l.viewport,l.content),h={...n,sizes:l,onSizesChange:u,hasThumb:!!(d>0&&d<1),onThumbChange:t=>s.current=t,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:t=>a.current=t};function p(t,e){return function(t,e,r,i="ltr"){let n=$(r),o=e||n/2,s=r.scrollbar.paddingStart+o,a=r.scrollbar.size-r.scrollbar.paddingEnd-(n-o),l=r.content-r.viewport;return W([s,a],"ltr"===i?[0,l]:[-1*l,0])(t)}(t,a.current,l,e)}return"horizontal"===r?(0,c.jsx)(M,{...h,ref:e,onThumbPositionChange:()=>{if(o.viewport&&s.current){let t=U(o.viewport.scrollLeft,l,o.dir);s.current.style.transform=`translate3d(${t}px, 0, 0)`}},onWheelScroll:t=>{o.viewport&&(o.viewport.scrollLeft=t)},onDragScroll:t=>{o.viewport&&(o.viewport.scrollLeft=p(t,o.dir))}}):"vertical"===r?(0,c.jsx)(C,{...h,ref:e,onThumbPositionChange:()=>{if(o.viewport&&s.current){let t=U(o.viewport.scrollTop,l);s.current.style.transform=`translate3d(0, ${t}px, 0)`}},onWheelScroll:t=>{o.viewport&&(o.viewport.scrollTop=t)},onDragScroll:t=>{o.viewport&&(o.viewport.scrollTop=p(t))}}):null}),M=i.forwardRef((t,e)=>{let{sizes:r,onSizesChange:n,...s}=t,a=v(T,t.__scopeScrollArea),[l,u]=i.useState(),d=i.useRef(null),h=(0,o.e)(e,d,a.onScrollbarXChange);return i.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,c.jsx)(V,{"data-orientation":"horizontal",...s,ref:h,sizes:r,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":$(r)+"px",...t.style},onThumbPointerDown:e=>t.onThumbPointerDown(e.x),onDragScroll:e=>t.onDragScroll(e.x),onWheelScroll:(e,r)=>{if(a.viewport){let i=a.viewport.scrollLeft+e.deltaX;t.onWheelScroll(i),i>0&&i<r&&e.preventDefault()}},onResize:()=>{d.current&&a.viewport&&l&&n({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:B(l.paddingLeft),paddingEnd:B(l.paddingRight)}})}})}),C=i.forwardRef((t,e)=>{let{sizes:r,onSizesChange:n,...s}=t,a=v(T,t.__scopeScrollArea),[l,u]=i.useState(),d=i.useRef(null),h=(0,o.e)(e,d,a.onScrollbarYChange);return i.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,c.jsx)(V,{"data-orientation":"vertical",...s,ref:h,sizes:r,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":$(r)+"px",...t.style},onThumbPointerDown:e=>t.onThumbPointerDown(e.y),onDragScroll:e=>t.onDragScroll(e.y),onWheelScroll:(e,r)=>{if(a.viewport){let i=a.viewport.scrollTop+e.deltaY;t.onWheelScroll(i),i>0&&i<r&&e.preventDefault()}},onResize:()=>{d.current&&a.viewport&&l&&n({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:B(l.paddingTop),paddingEnd:B(l.paddingBottom)}})}})}),[R,D]=f(T),V=i.forwardRef((t,e)=>{let{__scopeScrollArea:r,sizes:s,hasThumb:a,onThumbChange:l,onThumbPointerUp:u,onThumbPointerDown:h,onThumbPositionChange:m,onDragScroll:f,onWheelScroll:g,onResize:y,...b}=t,x=v(T,r),[w,S]=i.useState(null),P=(0,o.e)(e,t=>S(t)),E=i.useRef(null),k=i.useRef(""),A=x.viewport,M=s.content-s.viewport,C=(0,d.W)(g),D=(0,d.W)(m),V=H(y,10);function j(t){E.current&&f({x:t.clientX-E.current.left,y:t.clientY-E.current.top})}return i.useEffect(()=>{let t=t=>{let e=t.target;w?.contains(e)&&C(t,M)};return document.addEventListener("wheel",t,{passive:!1}),()=>document.removeEventListener("wheel",t,{passive:!1})},[A,w,M,C]),i.useEffect(D,[s,D]),Y(w,V),Y(x.content,V),(0,c.jsx)(R,{scope:r,scrollbar:w,hasThumb:a,onThumbChange:(0,d.W)(l),onThumbPointerUp:(0,d.W)(u),onThumbPositionChange:D,onThumbPointerDown:(0,d.W)(h),children:(0,c.jsx)(n.WV.div,{...b,ref:P,style:{position:"absolute",...b.style},onPointerDown:p(t.onPointerDown,t=>{0===t.button&&(t.target.setPointerCapture(t.pointerId),E.current=w.getBoundingClientRect(),k.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",x.viewport&&(x.viewport.style.scrollBehavior="auto"),j(t))}),onPointerMove:p(t.onPointerMove,j),onPointerUp:p(t.onPointerUp,t=>{let e=t.target;e.hasPointerCapture(t.pointerId)&&e.releasePointerCapture(t.pointerId),document.body.style.webkitUserSelect=k.current,x.viewport&&(x.viewport.style.scrollBehavior=""),E.current=null})})})}),j="ScrollAreaThumb",L=i.forwardRef((t,e)=>{let{forceMount:r,...i}=t,n=D(j,t.__scopeScrollArea);return(0,c.jsx)(a,{present:r||n.hasThumb,children:(0,c.jsx)(O,{ref:e,...i})})}),O=i.forwardRef((t,e)=>{let{__scopeScrollArea:r,style:s,...a}=t,l=v(j,r),u=D(j,r),{onThumbPositionChange:d}=u,h=(0,o.e)(e,t=>u.onThumbChange(t)),m=i.useRef(void 0),f=H(()=>{m.current&&(m.current(),m.current=void 0)},100);return i.useEffect(()=>{let t=l.viewport;if(t){let e=()=>{if(f(),!m.current){let e=_(t,d);m.current=e,d()}};return d(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[l.viewport,f,d]),(0,c.jsx)(n.WV.div,{"data-state":u.hasThumb?"visible":"hidden",...a,ref:h,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...s},onPointerDownCapture:p(t.onPointerDownCapture,t=>{let e=t.target.getBoundingClientRect(),r=t.clientX-e.left,i=t.clientY-e.top;u.onThumbPointerDown({x:r,y:i})}),onPointerUp:p(t.onPointerUp,u.onThumbPointerUp)})});L.displayName=j;var F="ScrollAreaCorner",N=i.forwardRef((t,e)=>{let r=v(F,t.__scopeScrollArea),i=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&i?(0,c.jsx)(I,{...t,ref:e}):null});N.displayName=F;var I=i.forwardRef((t,e)=>{let{__scopeScrollArea:r,...o}=t,s=v(F,r),[a,l]=i.useState(0),[u,d]=i.useState(0),h=!!(a&&u);return Y(s.scrollbarX,()=>{let t=s.scrollbarX?.offsetHeight||0;s.onCornerHeightChange(t),d(t)}),Y(s.scrollbarY,()=>{let t=s.scrollbarY?.offsetWidth||0;s.onCornerWidthChange(t),l(t)}),h?(0,c.jsx)(n.WV.div,{...o,ref:e,style:{width:a,height:u,position:"absolute",right:"ltr"===s.dir?0:void 0,left:"rtl"===s.dir?0:void 0,bottom:0,...t.style}}):null});function B(t){return t?parseInt(t,10):0}function z(t,e){let r=t/e;return isNaN(r)?0:r}function $(t){let e=z(t.viewport,t.content),r=t.scrollbar.paddingStart+t.scrollbar.paddingEnd;return Math.max((t.scrollbar.size-r)*e,18)}function U(t,e,r="ltr"){let i=$(e),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,o=e.scrollbar.size-n,s=e.content-e.viewport,a=function(t,[e,r]){return Math.min(r,Math.max(e,t))}(t,"ltr"===r?[0,s]:[-1*s,0]);return W([0,s],[0,o-i])(a)}function W(t,e){return r=>{if(t[0]===t[1]||e[0]===e[1])return e[0];let i=(e[1]-e[0])/(t[1]-t[0]);return e[0]+i*(r-t[0])}}var _=(t,e=()=>{})=>{let r={left:t.scrollLeft,top:t.scrollTop},i=0;return!function n(){let o={left:t.scrollLeft,top:t.scrollTop},s=r.left!==o.left,a=r.top!==o.top;(s||a)&&e(),r=o,i=window.requestAnimationFrame(n)}(),()=>window.cancelAnimationFrame(i)};function H(t,e){let r=(0,d.W)(t),n=i.useRef(0);return i.useEffect(()=>()=>window.clearTimeout(n.current),[]),i.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(r,e)},[r,e])}function Y(t,e){let r=(0,d.W)(e);(0,s.b)(()=>{let e=0;if(t){let i=new ResizeObserver(()=>{cancelAnimationFrame(e),e=window.requestAnimationFrame(r)});return i.observe(t),()=>{window.cancelAnimationFrame(e),i.unobserve(t)}}},[t,r])}var X=b,Z=w,q=N},7256:function(t,e,r){r.d(e,{Z8:function(){return s},g7:function(){return a}});var i=r(2265),n=r(2210),o=r(7437);function s(t){let e=function(t){let e=i.forwardRef((t,e)=>{let{children:r,...o}=t;if(i.isValidElement(r)){let t,s;let a=(t=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?r.ref:(t=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?r.props.ref:r.props.ref||r.ref,l=function(t,e){let r={...e};for(let i in e){let n=t[i],o=e[i];/^on[A-Z]/.test(i)?n&&o?r[i]=(...t)=>{let e=o(...t);return n(...t),e}:n&&(r[i]=n):"style"===i?r[i]={...n,...o}:"className"===i&&(r[i]=[n,o].filter(Boolean).join(" "))}return{...t,...r}}(o,r.props);return r.type!==i.Fragment&&(l.ref=e?(0,n.F)(e,a):a),i.cloneElement(r,l)}return i.Children.count(r)>1?i.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}(t),r=i.forwardRef((t,r)=>{let{children:n,...s}=t,a=i.Children.toArray(n),l=a.find(u);if(l){let t=l.props.children,n=a.map(e=>e!==l?e:i.Children.count(t)>1?i.Children.only(null):i.isValidElement(t)?t.props.children:null);return(0,o.jsx)(e,{...s,ref:r,children:i.isValidElement(t)?i.cloneElement(t,void 0,n):null})}return(0,o.jsx)(e,{...s,ref:r,children:n})});return r.displayName=`${t}.Slot`,r}var a=s("Slot"),l=Symbol("radix.slottable");function u(t){return i.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===l}},6459:function(t,e,r){r.d(e,{W:function(){return n}});var i=r(2265);function n(t){let e=i.useRef(t);return i.useEffect(()=>{e.current=t}),i.useMemo(()=>(...t)=>e.current?.(...t),[])}},1030:function(t,e,r){r.d(e,{b:function(){return n}});var i=r(2265),n=globalThis?.document?i.useLayoutEffect:()=>{}},6061:function(t,e,r){r.d(e,{j:function(){return s}});var i=r(7042);let n=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,o=i.W,s=(t,e)=>r=>{var i;if((null==e?void 0:e.variants)==null)return o(t,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:a}=e,l=Object.keys(s).map(t=>{let e=null==r?void 0:r[t],i=null==a?void 0:a[t];if(null===e)return null;let o=n(e)||n(i);return s[t][o]}),u=r&&Object.entries(r).reduce((t,e)=>{let[r,i]=e;return void 0===i||(t[r]=i),t},{});return o(t,l,null==e?void 0:null===(i=e.compoundVariants)||void 0===i?void 0:i.reduce((t,e)=>{let{class:r,className:i,...n}=e;return Object.entries(n).every(t=>{let[e,r]=t;return Array.isArray(r)?r.includes({...a,...u}[e]):({...a,...u})[e]===r})?[...t,r,i]:t},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},7042:function(t,e,r){r.d(e,{W:function(){return i}});function i(){for(var t,e,r=0,i="",n=arguments.length;r<n;r++)(t=arguments[r])&&(e=function t(e){var r,i,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e){if(Array.isArray(e)){var o=e.length;for(r=0;r<o;r++)e[r]&&(i=t(e[r]))&&(n&&(n+=" "),n+=i)}else for(i in e)e[i]&&(n&&(n+=" "),n+=i)}return n}(t))&&(i&&(i+=" "),i+=e);return i}},1393:function(t,e,r){r.d(e,{M:function(){return v}});var i=r(7437),n=r(2265),o=r(781),s=r(961),a=r(538),l=r(8243),u=r(6119),d=r(5968);class c extends n.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,r=(0,u.R)(t)&&t.offsetWidth||0,i=this.props.sizeRef.current;i.height=e.offsetHeight||0,i.width=e.offsetWidth||0,i.top=e.offsetTop,i.left=e.offsetLeft,i.right=r-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h({children:t,isPresent:e,anchorX:r}){let o=(0,n.useId)(),s=(0,n.useRef)(null),a=(0,n.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,n.useContext)(d._);return(0,n.useInsertionEffect)(()=>{let{width:t,height:i,top:n,left:u,right:d}=a.current;if(e||!s.current||!t||!i)return;let c="left"===r?`left: ${u}`:`right: ${d}`;s.current.dataset.motionPopId=o;let h=document.createElement("style");return l&&(h.nonce=l),document.head.appendChild(h),h.sheet&&h.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${t}px !important;
            height: ${i}px !important;
            ${c}px !important;
            top: ${n}px !important;
          }
        `),()=>{document.head.contains(h)&&document.head.removeChild(h)}},[e]),(0,i.jsx)(c,{isPresent:e,childRef:s,sizeRef:a,children:n.cloneElement(t,{ref:s})})}let p=({children:t,initial:e,isPresent:r,onExitComplete:o,custom:a,presenceAffectsLayout:u,mode:d,anchorX:c})=>{let p=(0,s.h)(m),f=(0,n.useId)(),g=!0,y=(0,n.useMemo)(()=>(g=!1,{id:f,initial:e,isPresent:r,custom:a,onExitComplete:t=>{for(let e of(p.set(t,!0),p.values()))if(!e)return;o&&o()},register:t=>(p.set(t,!1),()=>p.delete(t))}),[r,p,o]);return u&&g&&(y={...y}),(0,n.useMemo)(()=>{p.forEach((t,e)=>p.set(e,!1))},[r]),n.useEffect(()=>{r||p.size||!o||o()},[r]),"popLayout"===d&&(t=(0,i.jsx)(h,{isPresent:r,anchorX:c,children:t})),(0,i.jsx)(l.O.Provider,{value:y,children:t})};function m(){return new Map}var f=r(7196);let g=t=>t.key||"";function y(t){let e=[];return n.Children.forEach(t,t=>{(0,n.isValidElement)(t)&&e.push(t)}),e}let v=({children:t,custom:e,initial:r=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:d="sync",propagate:c=!1,anchorX:h="left"})=>{let[m,v]=(0,f.oO)(c),b=(0,n.useMemo)(()=>y(t),[t]),x=c&&!m?[]:b.map(g),w=(0,n.useRef)(!0),T=(0,n.useRef)(b),S=(0,s.h)(()=>new Map),[P,E]=(0,n.useState)(b),[k,A]=(0,n.useState)(b);(0,a.L)(()=>{w.current=!1,T.current=b;for(let t=0;t<k.length;t++){let e=g(k[t]);x.includes(e)?S.delete(e):!0!==S.get(e)&&S.set(e,!1)}},[k,x.length,x.join("-")]);let M=[];if(b!==P){let t=[...b];for(let e=0;e<k.length;e++){let r=k[e],i=g(r);x.includes(i)||(t.splice(e,0,r),M.push(r))}return"wait"===d&&M.length&&(t=M),A(y(t)),E(b),null}let{forceRender:C}=(0,n.useContext)(o.p);return(0,i.jsx)(i.Fragment,{children:k.map(t=>{let n=g(t),o=(!c||!!m)&&(b===k||x.includes(n));return(0,i.jsx)(p,{isPresent:o,initial:(!w.current||!!r)&&void 0,custom:e,presenceAffectsLayout:u,mode:d,onExitComplete:o?void 0:()=>{if(!S.has(n))return;S.set(n,!0);let t=!0;S.forEach(e=>{e||(t=!1)}),t&&(C?.(),A(T.current),c&&v?.(),l&&l())},anchorX:h,children:t},n)})})}},7196:function(t,e,r){r.d(e,{oO:function(){return o}});var i=r(2265),n=r(8243);function o(t=!0){let e=(0,i.useContext)(n.O);if(null===e)return[!0,null];let{isPresent:r,onExitComplete:o,register:s}=e,a=(0,i.useId)();(0,i.useEffect)(()=>{if(t)return s(a)},[t]);let l=(0,i.useCallback)(()=>t&&o&&o(a),[a,o,t]);return!r&&o?[!1,l]:[!0]}},781:function(t,e,r){r.d(e,{p:function(){return i}});let i=(0,r(2265).createContext)({})},5968:function(t,e,r){r.d(e,{_:function(){return i}});let i=(0,r(2265).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},8243:function(t,e,r){r.d(e,{O:function(){return i}});let i=(0,r(2265).createContext)(null)},8173:function(t,e,r){let i;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function o(t){let e=[{},{}];return t?.values.forEach((t,r)=>{e[0][r]=t.get(),e[1][r]=t.getVelocity()}),e}function s(t,e,r,i){if("function"==typeof e){let[n,s]=o(i);e=e(void 0!==r?r:t.custom,n,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,s]=o(i);e=e(void 0!==r?r:t.custom,n,s)}return e}function a(t,e,r){let i=t.getProps();return s(i,e,void 0!==r?r:i.custom,t)}function l(t,e){return t?.[e]??t?.default??t}r.d(e,{E:function(){return oC}});let u=t=>t,d={},c=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],h={value:null,addProjectionMetrics:null};function p(t,e){let r=!1,i=!0,n={delta:0,timestamp:0,isProcessing:!1},o=()=>r=!0,s=c.reduce((t,r)=>(t[r]=function(t,e){let r=new Set,i=new Set,n=!1,o=!1,s=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){s.has(e)&&(d.schedule(e),t()),l++,e(a)}let d={schedule:(t,e=!1,o=!1)=>{let a=o&&n?r:i;return e&&s.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),s.delete(t)},process:t=>{if(a=t,n){o=!0;return}n=!0,[r,i]=[i,r],r.forEach(u),e&&h.value&&h.value.frameloop[e].push(l),l=0,r.clear(),n=!1,o&&(o=!1,d.process(t))}};return d}(o,e?r:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:p,update:m,preRender:f,render:g,postRender:y}=s,v=()=>{let o=d.useManualTiming?n.timestamp:performance.now();r=!1,d.useManualTiming||(n.delta=i?1e3/60:Math.max(Math.min(o-n.timestamp,40),1)),n.timestamp=o,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),p.process(n),m.process(n),f.process(n),g.process(n),y.process(n),n.isProcessing=!1,r&&e&&(i=!1,t(v))},b=()=>{r=!0,i=!0,n.isProcessing||t(v)};return{schedule:c.reduce((t,e)=>{let i=s[e];return t[e]=(t,e=!1,n=!1)=>(r||b(),i.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<c.length;e++)s[c[e]].cancel(t)},state:n,steps:s}}let{schedule:m,cancel:f,state:g,steps:y}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(v),x=new Set(["width","height","top","left","right","bottom",...v]);function w(t,e){-1===t.indexOf(e)&&t.push(e)}function T(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}class S{constructor(){this.subscriptions=[]}add(t){return w(this.subscriptions,t),()=>T(this.subscriptions,t)}notify(t,e,r){let i=this.subscriptions.length;if(i){if(1===i)this.subscriptions[0](t,e,r);else for(let n=0;n<i;n++){let i=this.subscriptions[n];i&&i(t,e,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function P(){i=void 0}let E={now:()=>(void 0===i&&E.set(g.isProcessing||d.useManualTiming?g.timestamp:performance.now()),i),set:t=>{i=t,queueMicrotask(P)}},k=t=>!isNaN(parseFloat(t)),A={current:void 0};class M{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let r=E.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=E.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=k(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new S);let r=this.events[t].add(e);return"change"===t?()=>{r(),m.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,r){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return A.current&&A.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=E.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let r=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),r?1e3/r*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function C(t,e){return new M(t,e)}let R=t=>Array.isArray(t),D=t=>!!(t&&t.getVelocity);function V(t,e){let r=t.getValue("willChange");if(D(r)&&r.add)return r.add(e);if(!r&&d.WillChange){let r=new d.WillChange("auto");t.addValue("willChange",r),r.add(e)}}let j=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),L="data-"+j("framerAppearId"),O=(t,e)=>r=>e(t(r)),F=(...t)=>t.reduce(O),N=(t,e,r)=>r>e?e:r<t?t:r,I=t=>1e3*t,B=t=>t/1e3,z={layout:0,mainThread:0,waapi:0},$=()=>{},U=()=>{},W=t=>e=>"string"==typeof e&&e.startsWith(t),_=W("--"),H=W("var(--"),Y=t=>!!H(t)&&X.test(t.split("/*")[0].trim()),X=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Z={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},q={...Z,transform:t=>N(0,1,t)},K={...Z,default:1},G=t=>Math.round(1e5*t)/1e5,J=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,Q=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>r=>!!("string"==typeof r&&Q.test(r)&&r.startsWith(t)||e&&null!=r&&Object.prototype.hasOwnProperty.call(r,e)),te=(t,e,r)=>i=>{if("string"!=typeof i)return i;let[n,o,s,a]=i.match(J);return{[t]:parseFloat(n),[e]:parseFloat(o),[r]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},tr=t=>N(0,255,t),ti={...Z,transform:t=>Math.round(tr(t))},tn={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:r,alpha:i=1})=>"rgba("+ti.transform(t)+", "+ti.transform(e)+", "+ti.transform(r)+", "+G(q.transform(i))+")"},to={test:tt("#"),parse:function(t){let e="",r="",i="",n="";return t.length>5?(e=t.substring(1,3),r=t.substring(3,5),i=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),r=t.substring(2,3),i=t.substring(3,4),n=t.substring(4,5),e+=e,r+=r,i+=i,n+=n),{red:parseInt(e,16),green:parseInt(r,16),blue:parseInt(i,16),alpha:n?parseInt(n,16)/255:1}},transform:tn.transform},ts=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=ts("deg"),tl=ts("%"),tu=ts("px"),td=ts("vh"),tc=ts("vw"),th={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tp={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:r,alpha:i=1})=>"hsla("+Math.round(t)+", "+tl.transform(G(e))+", "+tl.transform(G(r))+", "+G(q.transform(i))+")"},tm={test:t=>tn.test(t)||to.test(t)||tp.test(t),parse:t=>tn.test(t)?tn.parse(t):tp.test(t)?tp.parse(t):to.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tn.transform(t):tp.transform(t)},tf=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tg="number",ty="color",tv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tb(t){let e=t.toString(),r=[],i={color:[],number:[],var:[]},n=[],o=0,s=e.replace(tv,t=>(tm.test(t)?(i.color.push(o),n.push(ty),r.push(tm.parse(t))):t.startsWith("var(")?(i.var.push(o),n.push("var"),r.push(t)):(i.number.push(o),n.push(tg),r.push(parseFloat(t))),++o,"${}")).split("${}");return{values:r,split:s,indexes:i,types:n}}function tx(t){return tb(t).values}function tw(t){let{split:e,types:r}=tb(t),i=e.length;return t=>{let n="";for(let o=0;o<i;o++)if(n+=e[o],void 0!==t[o]){let e=r[o];e===tg?n+=G(t[o]):e===ty?n+=tm.transform(t[o]):n+=t[o]}return n}}let tT=t=>"number"==typeof t?0:t,tS={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(J)?.length||0)+(t.match(tf)?.length||0)>0},parse:tx,createTransformer:tw,getAnimatableNone:function(t){let e=tx(t);return tw(t)(e.map(tT))}};function tP(t,e,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?t+(e-t)*6*r:r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}function tE(t,e){return r=>r>0?e:t}let tk=(t,e,r)=>t+(e-t)*r,tA=(t,e,r)=>{let i=t*t,n=r*(e*e-i)+i;return n<0?0:Math.sqrt(n)},tM=[to,tn,tp],tC=t=>tM.find(e=>e.test(t));function tR(t){let e=tC(t);if($(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let r=e.parse(t);return e===tp&&(r=function({hue:t,saturation:e,lightness:r,alpha:i}){t/=360,r/=100;let n=0,o=0,s=0;if(e/=100){let i=r<.5?r*(1+e):r+e-r*e,a=2*r-i;n=tP(a,i,t+1/3),o=tP(a,i,t),s=tP(a,i,t-1/3)}else n=o=s=r;return{red:Math.round(255*n),green:Math.round(255*o),blue:Math.round(255*s),alpha:i}}(r)),r}let tD=(t,e)=>{let r=tR(t),i=tR(e);if(!r||!i)return tE(t,e);let n={...r};return t=>(n.red=tA(r.red,i.red,t),n.green=tA(r.green,i.green,t),n.blue=tA(r.blue,i.blue,t),n.alpha=tk(r.alpha,i.alpha,t),tn.transform(n))},tV=new Set(["none","hidden"]);function tj(t,e){return r=>tk(t,e,r)}function tL(t){return"number"==typeof t?tj:"string"==typeof t?Y(t)?tE:tm.test(t)?tD:tN:Array.isArray(t)?tO:"object"==typeof t?tm.test(t)?tD:tF:tE}function tO(t,e){let r=[...t],i=r.length,n=t.map((t,r)=>tL(t)(t,e[r]));return t=>{for(let e=0;e<i;e++)r[e]=n[e](t);return r}}function tF(t,e){let r={...t,...e},i={};for(let n in r)void 0!==t[n]&&void 0!==e[n]&&(i[n]=tL(t[n])(t[n],e[n]));return t=>{for(let e in i)r[e]=i[e](t);return r}}let tN=(t,e)=>{let r=tS.createTransformer(e),i=tb(t),n=tb(e);return i.indexes.var.length===n.indexes.var.length&&i.indexes.color.length===n.indexes.color.length&&i.indexes.number.length>=n.indexes.number.length?tV.has(t)&&!n.values.length||tV.has(e)&&!i.values.length?tV.has(t)?r=>r<=0?t:e:r=>r>=1?e:t:F(tO(function(t,e){let r=[],i={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let o=e.types[n],s=t.indexes[o][i[o]],a=t.values[s]??0;r[n]=a,i[o]++}return r}(i,n),n.values),r):($(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tE(t,e))};function tI(t,e,r){return"number"==typeof t&&"number"==typeof e&&"number"==typeof r?tk(t,e,r):tL(t)(t,e)}let tB=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>m.update(e,t),stop:()=>f(e),now:()=>g.isProcessing?g.timestamp:E.now()}},tz=(t,e,r=10)=>{let i="",n=Math.max(Math.round(e/r),2);for(let e=0;e<n;e++)i+=t(e/(n-1))+", ";return`linear(${i.substring(0,i.length-2)})`};function t$(t){let e=0,r=t.next(e);for(;!r.done&&e<2e4;)e+=50,r=t.next(e);return e>=2e4?1/0:e}function tU(t,e,r){var i,n;let o=Math.max(e-5,0);return i=r-t(o),(n=e-o)?1e3/n*i:0}let tW={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function t_(t,e){return t*Math.sqrt(1-e*e)}let tH=["duration","bounce"],tY=["stiffness","damping","mass"];function tX(t,e){return e.some(e=>void 0!==t[e])}function tZ(t=tW.visualDuration,e=tW.bounce){let r;let i="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:o}=i,s=i.keyframes[0],a=i.keyframes[i.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:d,mass:c,duration:h,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:tW.velocity,stiffness:tW.stiffness,damping:tW.damping,mass:tW.mass,isResolvedFromDuration:!1,...t};if(!tX(t,tY)&&tX(t,tH)){if(t.visualDuration){let r=2*Math.PI/(1.2*t.visualDuration),i=r*r,n=2*N(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:tW.mass,stiffness:i,damping:n}}else{let r=function({duration:t=tW.duration,bounce:e=tW.bounce,velocity:r=tW.velocity,mass:i=tW.mass}){let n,o;$(t<=I(tW.maxDuration),"Spring duration must be 10 seconds or less");let s=1-e;s=N(tW.minDamping,tW.maxDamping,s),t=N(tW.minDuration,tW.maxDuration,B(t)),s<1?(n=e=>{let i=e*s,n=i*t;return .001-(i-r)/t_(e,s)*Math.exp(-n)},o=e=>{let i=e*s*t,o=Math.pow(s,2)*Math.pow(e,2)*t,a=t_(Math.pow(e,2),s);return(i*r+r-o)*Math.exp(-i)*(-n(e)+.001>0?-1:1)/a}):(n=e=>-.001+Math.exp(-e*t)*((e-r)*t+1),o=e=>t*t*(r-e)*Math.exp(-e*t));let a=function(t,e,r){let i=r;for(let r=1;r<12;r++)i-=t(i)/e(i);return i}(n,o,5/t);if(t=I(t),isNaN(a))return{stiffness:tW.stiffness,damping:tW.damping,duration:t};{let e=Math.pow(a,2)*i;return{stiffness:e,damping:2*s*Math.sqrt(i*e),duration:t}}}(t);(e={...e,...r,mass:tW.mass}).isResolvedFromDuration=!0}}return e}({...i,velocity:-B(i.velocity||0)}),f=p||0,g=d/(2*Math.sqrt(u*c)),y=a-s,v=B(Math.sqrt(u/c)),b=5>Math.abs(y);if(n||(n=b?tW.restSpeed.granular:tW.restSpeed.default),o||(o=b?tW.restDelta.granular:tW.restDelta.default),g<1){let t=t_(v,g);r=e=>a-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)r=t=>a-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);r=e=>{let r=Math.exp(-g*v*e),i=Math.min(t*e,300);return a-r*((f+g*v*y)*Math.sinh(i)+t*y*Math.cosh(i))/t}}let x={calculatedDuration:m&&h||null,next:t=>{let e=r(t);if(m)l.done=t>=h;else{let i=0===t?f:0;g<1&&(i=0===t?I(f):tU(r,t,e));let s=Math.abs(i)<=n,u=Math.abs(a-e)<=o;l.done=s&&u}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(t$(x),2e4),e=tz(e=>x.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return x}function tq({keyframes:t,velocity:e=0,power:r=.8,timeConstant:i=325,bounceDamping:n=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:d}){let c,h;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,y=r*e,v=p+y,b=void 0===s?v:s(v);b!==v&&(y=b-p);let x=t=>-y*Math.exp(-t/i),w=t=>b+x(t),T=t=>{let e=x(t),r=w(t);m.done=Math.abs(e)<=u,m.value=m.done?b:r},S=t=>{f(m.value)&&(c=t,h=tZ({keyframes:[m.value,g(m.value)],velocity:tU(w,t,m.value),damping:n,stiffness:o,restDelta:u,restSpeed:d}))};return S(0),{calculatedDuration:null,next:t=>{let e=!1;return(h||void 0!==c||(e=!0,T(t),S(t)),void 0!==c&&t>=c)?h.next(t-c):(e||T(t),m)}}}tZ.applyToOptions=t=>{let e=function(t,e=100,r){let i=r({...t,keyframes:[0,e]}),n=Math.min(t$(i),2e4);return{type:"keyframes",ease:t=>i.next(n*t).value/e,duration:B(n)}}(t,100,tZ);return t.ease=e.ease,t.duration=I(e.duration),t.type="keyframes",t};let tK=(t,e,r)=>(((1-3*r+3*e)*t+(3*r-6*e))*t+3*e)*t;function tG(t,e,r,i){if(t===e&&r===i)return u;let n=e=>(function(t,e,r,i,n){let o,s;let a=0;do(o=tK(s=e+(r-e)/2,i,n)-t)>0?r=s:e=s;while(Math.abs(o)>1e-7&&++a<12);return s})(e,0,1,t,r);return t=>0===t||1===t?t:tK(n(t),e,i)}let tJ=tG(.42,0,1,1),tQ=tG(0,0,.58,1),t0=tG(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t5=t=>e=>1-t(1-e),t3=tG(.33,1.53,.69,.99),t4=t5(t3),t6=t2(t4),t9=t=>(t*=2)<1?.5*t4(t):.5*(2-Math.pow(2,-10*(t-1))),t8=t=>1-Math.sin(Math.acos(t)),t7=t5(t8),et=t2(t8),ee=t=>Array.isArray(t)&&"number"==typeof t[0],er={linear:u,easeIn:tJ,easeInOut:t0,easeOut:tQ,circIn:t8,circInOut:et,circOut:t7,backIn:t4,backInOut:t6,backOut:t3,anticipate:t9},ei=t=>"string"==typeof t,en=t=>{if(ee(t)){U(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,r,i,n]=t;return tG(e,r,i,n)}return ei(t)?(U(void 0!==er[t],`Invalid easing type '${t}'`),er[t]):t},eo=(t,e,r)=>{let i=e-t;return 0===i?1:(r-t)/i};function es({duration:t=300,keyframes:e,times:r,ease:i="easeInOut"}){let n=t1(i)?i.map(en):en(i),o={done:!1,value:e[0]},s=function(t,e,{clamp:r=!0,ease:i,mixer:n}={}){let o=t.length;if(U(o===e.length,"Both input and output ranges must be the same length"),1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];let s=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,r){let i=[],n=r||d.mix||tI,o=t.length-1;for(let r=0;r<o;r++){let o=n(t[r],t[r+1]);e&&(o=F(Array.isArray(e)?e[r]||u:e,o)),i.push(o)}return i}(e,i,n),l=a.length,c=r=>{if(s&&r<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(r<t[i+1]);i++);let n=eo(t[i],t[i+1],r);return a[i](n)};return r?e=>c(N(t[0],t[o-1],e)):c}((r&&r.length===e.length?r:function(t){let e=[0];return function(t,e){let r=t[t.length-1];for(let i=1;i<=e;i++){let n=eo(0,e,i);t.push(tk(r,1,n))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(n)?n:e.map(()=>n||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=s(e),o.done=e>=t,o)}}let ea=t=>null!==t;function el(t,{repeat:e,repeatType:r="loop"},i,n=1){let o=t.filter(ea),s=n<0||e&&"loop"!==r&&e%2==1?0:o.length-1;return s&&void 0!==i?i:o[s]}let eu={decay:tq,inertia:tq,tween:es,keyframes:es,spring:tZ};function ed(t){"string"==typeof t.type&&(t.type=eu[t.type])}class ec{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let eh=t=>t/100;class ep extends ec{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==E.now()&&this.tick(E.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},z.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;ed(t);let{type:e=es,repeat:r=0,repeatDelay:i=0,repeatType:n,velocity:o=0}=t,{keyframes:s}=t,a=e||es;a!==es&&"number"!=typeof s[0]&&(this.mixKeyframes=F(eh,tI(s[0],s[1])),s=[0,100]);let l=a({...t,keyframes:s});"mirror"===n&&(this.mirroredGenerator=a({...t,keyframes:[...s].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=t$(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(r+1)-i,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:r,totalDuration:i,mixKeyframes:n,mirroredGenerator:o,resolvedDuration:s,calculatedDuration:a}=this;if(null===this.startTime)return r.next(0);let{delay:l=0,keyframes:u,repeat:d,repeatType:c,repeatDelay:h,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let v=this.currentTime,b=r;if(d){let t=Math.min(this.currentTime,i)/s,e=Math.floor(t),r=t%1;!r&&t>=1&&(r=1),1===r&&e--,(e=Math.min(e,d+1))%2&&("reverse"===c?(r=1-r,h&&(r-=h/s)):"mirror"===c&&(b=o)),v=N(0,1,r)*s}let x=y?{done:!1,value:u[0]}:b.next(v);n&&(x.value=n(x.value));let{done:w}=x;y||null===a||(w=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return T&&p!==tq&&(x.value=el(u,this.options,f,this.speed)),m&&m(x.value),T&&this.finish(),x}then(t,e){return this.finished.then(t,e)}get duration(){return B(this.calculatedDuration)}get time(){return B(this.currentTime)}set time(t){t=I(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(E.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=B(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tB,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let r=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=r):null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime||(this.startTime=e??r),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(E.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,z.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let em=t=>180*t/Math.PI,ef=t=>ey(em(Math.atan2(t[1],t[0]))),eg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ef,rotateZ:ef,skewX:t=>em(Math.atan(t[1])),skewY:t=>em(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ey=t=>((t%=360)<0&&(t+=360),t),ev=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eb=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ex={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ev,scaleY:eb,scale:t=>(ev(t)+eb(t))/2,rotateX:t=>ey(em(Math.atan2(t[6],t[5]))),rotateY:t=>ey(em(Math.atan2(-t[2],t[0]))),rotateZ:ef,rotate:ef,skewX:t=>em(Math.atan(t[4])),skewY:t=>em(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ew(t){return t.includes("scale")?1:0}function eT(t,e){let r,i;if(!t||"none"===t)return ew(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)r=ex,i=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=eg,i=e}if(!i)return ew(e);let o=r[e],s=i[1].split(",").map(eP);return"function"==typeof o?o(s):s[o]}let eS=(t,e)=>{let{transform:r="none"}=getComputedStyle(t);return eT(r,e)};function eP(t){return parseFloat(t.trim())}let eE=t=>t===Z||t===tu,ek=new Set(["x","y","z"]),eA=v.filter(t=>!ek.has(t)),eM={width:({x:t},{paddingLeft:e="0",paddingRight:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),height:({y:t},{paddingTop:e="0",paddingBottom:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eT(e,"x"),y:(t,{transform:e})=>eT(e,"y")};eM.translateX=eM.x,eM.translateY=eM.y;let eC=new Set,eR=!1,eD=!1,eV=!1;function ej(){if(eD){let t=Array.from(eC).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),r=new Map;e.forEach(t=>{let e=function(t){let e=[];return eA.forEach(r=>{let i=t.getValue(r);void 0!==i&&(e.push([r,i.get()]),i.set(r.startsWith("scale")?1:0))}),e}(t);e.length&&(r.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=r.get(t);e&&e.forEach(([e,r])=>{t.getValue(e)?.set(r)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eD=!1,eR=!1,eC.forEach(t=>t.complete(eV)),eC.clear()}function eL(){eC.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eD=!0)})}class eO{constructor(t,e,r,i,n,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=r,this.motionValue=i,this.element=n,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(eC.add(this),eR||(eR=!0,m.read(eL),m.resolveKeyframes(ej))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:r,motionValue:i}=this;if(null===t[0]){let n=i?.get(),o=t[t.length-1];if(void 0!==n)t[0]=n;else if(r&&e){let i=r.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===n&&i.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eC.delete(this)}cancel(){"scheduled"===this.state&&(eC.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eF=t=>t.startsWith("--");function eN(t){let e;return()=>(void 0===e&&(e=t()),e)}let eI=eN(()=>void 0!==window.ScrollTimeline),eB={},ez=function(t,e){let r=eN(t);return()=>eB[e]??r()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),e$=([t,e,r,i])=>`cubic-bezier(${t}, ${e}, ${r}, ${i})`,eU={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:e$([0,.65,.55,1]),circOut:e$([.55,0,1,.45]),backIn:e$([.31,.01,.66,-.59]),backOut:e$([.33,1.53,.69,.99])};function eW(t){return"function"==typeof t&&"applyToOptions"in t}class e_ extends ec{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:r,keyframes:i,pseudoElement:n,allowFlatten:o=!1,finalKeyframe:s,onComplete:a}=t;this.isPseudoElement=!!n,this.allowFlatten=o,this.options=t,U("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return eW(t)&&ez()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,r,{delay:i=0,duration:n=300,repeat:o=0,repeatType:s="loop",ease:a="easeOut",times:l}={},u){let d={[e]:r};l&&(d.offset=l);let c=function t(e,r){if(e)return"function"==typeof e?ez()?tz(e,r):"ease-out":ee(e)?e$(e):Array.isArray(e)?e.map(e=>t(e,r)||eU.easeOut):eU[e]}(a,n);Array.isArray(c)&&(d.easing=c),h.value&&z.waapi++;let p={delay:i,duration:n,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"};u&&(p.pseudoElement=u);let m=t.animate(d,p);return h.value&&m.finished.finally(()=>{z.waapi--}),m}(e,r,i,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=el(i,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(t):eF(r)?e.style.setProperty(r,t):e.style[r]=t,this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return B(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return B(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=I(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eI())?(this.animation.timeline=t,u):e(this)}}let eH={anticipate:t9,backInOut:t6,circInOut:et};class eY extends e_{constructor(t){"string"==typeof t.ease&&t.ease in eH&&(t.ease=eH[t.ease]),ed(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:r,onComplete:i,element:n,...o}=this.options;if(!e)return;if(void 0!==t){e.set(t);return}let s=new ep({...o,autoplay:!1}),a=I(this.finishedTime??this.time);e.setWithVelocity(s.sample(a-10).value,s.sample(a).value,10),s.stop()}}let eX=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tS.test(t)||"0"===t)&&!t.startsWith("url("));var eZ,eq,eK,eG=r(6119);let eJ=new Set(["opacity","clipPath","filter","transform"]),eQ=eN(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class e0 extends ec{constructor({autoplay:t=!0,delay:e=0,type:r="keyframes",repeat:i=0,repeatDelay:n=0,repeatType:o="loop",keyframes:s,name:a,motionValue:l,element:u,...d}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=E.now();let c={autoplay:t,delay:e,type:r,repeat:i,repeatDelay:n,repeatType:o,name:a,motionValue:l,element:u,...d},h=u?.KeyframeResolver||eO;this.keyframeResolver=new h(s,(t,e,r)=>this.onKeyframesResolved(t,e,c,!r),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,r,i){this.keyframeResolver=void 0;let{name:n,type:o,velocity:s,delay:a,isHandoff:l,onUpdate:c}=r;this.resolvedAt=E.now(),!function(t,e,r,i){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let o=t[t.length-1],s=eX(n,e),a=eX(o,e);return $(s===a,`You are trying to animate ${e} from "${n}" to "${o}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${o} via the \`style\` property.`),!!s&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let r=0;r<t.length;r++)if(t[r]!==e)return!0}(t)||("spring"===r||eW(r))&&i)}(t,n,o,s)&&((d.instantAnimations||!a)&&c?.(el(t,r,e)),t[0]=t[t.length-1],r.duration=0,r.repeat=0);let h={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...r,keyframes:t},p=!l&&function(t){let{motionValue:e,name:r,repeatDelay:i,repeatType:n,damping:o,type:s}=t;if(!(0,eG.R)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eQ()&&r&&eJ.has(r)&&("transform"!==r||!l)&&!a&&!i&&"mirror"!==n&&0!==o&&"inertia"!==s}(h)?new eY({...h,element:h.motionValue.owner.current}):new ep(h);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eV=!0,eL(),ej(),eV=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e1=t=>null!==t,e2={type:"spring",stiffness:500,damping:25,restSpeed:10},e5=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e3={type:"keyframes",duration:.8},e4={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e6=(t,{keyframes:e})=>e.length>2?e3:b.has(t)?t.startsWith("scale")?e5(e[1]):e2:e4,e9=(t,e,r,i={},n,o)=>s=>{let a=l(i,t)||{},u=a.delay||i.delay||0,{elapsed:c=0}=i;c-=I(u);let h={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{s(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:n};!function({when:t,delay:e,delayChildren:r,staggerChildren:i,staggerDirection:n,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...d}){return!!Object.keys(d).length}(a)&&Object.assign(h,e6(t,h)),h.duration&&(h.duration=I(h.duration)),h.repeatDelay&&(h.repeatDelay=I(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let p=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0!==h.delay||(p=!0)),(d.instantAnimations||d.skipAnimations)&&(p=!0,h.duration=0,h.delay=0),h.allowFlatten=!a.type&&!a.ease,p&&!o&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:r="loop"},i){let n=t.filter(e1),o=e&&"loop"!==r&&e%2==1?0:n.length-1;return o&&void 0!==i?i:n[o]}(h.keyframes,a);if(void 0!==t){m.update(()=>{h.onUpdate(t),h.onComplete()});return}}return a.isSync?new ep(h):new e0(h)};function e8(t,e,{delay:r=0,transitionOverride:i,type:n}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:s,...u}=e;i&&(o=i);let d=[],c=n&&t.animationState&&t.animationState.getState()[n];for(let e in u){let i=t.getValue(e,t.latestValues[e]??null),n=u[e];if(void 0===n||c&&function({protectedKeys:t,needsAnimating:e},r){let i=t.hasOwnProperty(r)&&!0!==e[r];return e[r]=!1,i}(c,e))continue;let s={delay:r,...l(o||{},e)},a=i.get();if(void 0!==a&&!i.isAnimating&&!Array.isArray(n)&&n===a&&!s.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let r=t.props[L];if(r){let t=window.MotionHandoffAnimation(r,e,m);null!==t&&(s.startTime=t,h=!0)}}V(t,e),i.start(e9(e,i,n,t.shouldReduceMotion&&x.has(e)?{type:!1}:s,t,h));let p=i.animation;p&&d.push(p)}return s&&Promise.all(d).then(()=>{m.update(()=>{s&&function(t,e){let{transitionEnd:r={},transition:i={},...n}=a(t,e)||{};for(let e in n={...n,...r}){var o;let r=R(o=n[e])?o[o.length-1]||0:o;t.hasValue(e)?t.getValue(e).set(r):t.addValue(e,C(r))}}(t,s)})}),d}function e7(t,e,r={}){let i=a(t,e,"exit"===r.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(n=r.transitionOverride);let o=i?()=>Promise.all(e8(t,i,r)):()=>Promise.resolve(),s=t.variantChildren&&t.variantChildren.size?(i=0)=>{let{delayChildren:o=0,staggerChildren:s,staggerDirection:a}=n;return function(t,e,r=0,i=0,n=1,o){let s=[],a=(t.variantChildren.size-1)*i,l=1===n?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(rt).forEach((t,i)=>{t.notify("AnimationStart",e),s.push(e7(t,e,{...o,delay:r+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(s)}(t,e,o+i,s,a,r)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([o(),s(r.delay)]);{let[t,e]="beforeChildren"===l?[o,s]:[s,o];return t().then(()=>e())}}function rt(t,e){return t.sortNodePosition(e)}function re(t,e){if(!Array.isArray(e))return!1;let r=e.length;if(r!==t.length)return!1;for(let i=0;i<r;i++)if(e[i]!==t[i])return!1;return!0}function rr(t){return"string"==typeof t||Array.isArray(t)}let ri=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],rn=["initial",...ri],ro=rn.length,rs=[...ri].reverse(),ra=ri.length;function rl(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ru(){return{animate:rl(!0),whileInView:rl(),whileHover:rl(),whileTap:rl(),whileDrag:rl(),whileFocus:rl(),exit:rl()}}class rd{constructor(t){this.isMounted=!1,this.node=t}update(){}}class rc extends rd{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:r})=>(function(t,e,r={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e))i=Promise.all(e.map(e=>e7(t,e,r)));else if("string"==typeof e)i=e7(t,e,r);else{let n="function"==typeof e?a(t,e,r.custom):e;i=Promise.all(e8(t,n,r))}return i.then(()=>{t.notify("AnimationComplete",e)})})(t,e,r))),r=ru(),i=!0,o=e=>(r,i)=>{let n=a(t,i,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...i}=n;r={...r,...i,...e}}return r};function s(s){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let r=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(r.initial=e.props.initial),r}let r={};for(let t=0;t<ro;t++){let i=rn[t],n=e.props[i];(rr(n)||!1===n)&&(r[i]=n)}return r}(t.parent)||{},d=[],c=new Set,h={},p=1/0;for(let e=0;e<ra;e++){var m;let a=rs[e],f=r[a],g=void 0!==l[a]?l[a]:u[a],y=rr(g),v=a===s?f.isActive:null;!1===v&&(p=e);let b=g===u[a]&&g!==l[a]&&y;if(b&&i&&t.manuallyAnimateOnMount&&(b=!1),f.protectedKeys={...h},!f.isActive&&null===v||!g&&!f.prevProp||n(g)||"boolean"==typeof g)continue;let x=(m=f.prevProp,"string"==typeof g?g!==m:!!Array.isArray(g)&&!re(g,m)),w=x||a===s&&f.isActive&&!b&&y||e>p&&y,T=!1,S=Array.isArray(g)?g:[g],P=S.reduce(o(a),{});!1===v&&(P={});let{prevResolvedValues:E={}}=f,k={...E,...P},A=e=>{w=!0,c.has(e)&&(T=!0,c.delete(e)),f.needsAnimating[e]=!0;let r=t.getValue(e);r&&(r.liveStyle=!1)};for(let t in k){let e=P[t],r=E[t];if(!h.hasOwnProperty(t))(R(e)&&R(r)?re(e,r):e===r)?void 0!==e&&c.has(t)?A(t):f.protectedKeys[t]=!0:null!=e?A(t):c.add(t)}f.prevProp=g,f.prevResolvedValues=P,f.isActive&&(h={...h,...P}),i&&t.blockInitialAnimation&&(w=!1);let M=!(b&&x)||T;w&&M&&d.push(...S.map(t=>({animation:t,options:{type:a}})))}if(c.size){let e={};if("boolean"!=typeof l.initial){let r=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);r&&r.transition&&(e.transition=r.transition)}c.forEach(r=>{let i=t.getBaseTarget(r),n=t.getValue(r);n&&(n.liveStyle=!0),e[r]=i??null}),d.push({animation:e})}let f=!!d.length;return i&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(f=!1),i=!1,f?e(d):Promise.resolve()}return{animateChanges:s,setActive:function(e,i){if(r[e].isActive===i)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,i)),r[e].isActive=i;let n=s(e);for(let t in r)r[t].protectedKeys={};return n},setAnimateFunction:function(r){e=r(t)},getState:()=>r,reset:()=>{r=ru(),i=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let rh=0;class rp extends rd{constructor(){super(...arguments),this.id=rh++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;let i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let rm={x:!1,y:!1};function rf(t,e,r,i={passive:!0}){return t.addEventListener(e,r,i),()=>t.removeEventListener(e,r)}let rg=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function ry(t){return{point:{x:t.pageX,y:t.pageY}}}let rv=t=>e=>rg(e)&&t(e,ry(e));function rb(t,e,r,i){return rf(t,e,rv(r),i)}function rx({top:t,left:e,right:r,bottom:i}){return{x:{min:e,max:r},y:{min:t,max:i}}}function rw(t){return t.max-t.min}function rT(t,e,r,i=.5){t.origin=i,t.originPoint=tk(e.min,e.max,t.origin),t.scale=rw(r)/rw(e),t.translate=tk(r.min,r.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function rS(t,e,r,i){rT(t.x,e.x,r.x,i?i.originX:void 0),rT(t.y,e.y,r.y,i?i.originY:void 0)}function rP(t,e,r){t.min=r.min+e.min,t.max=t.min+rw(e)}function rE(t,e,r){t.min=e.min-r.min,t.max=t.min+rw(e)}function rk(t,e,r){rE(t.x,e.x,r.x),rE(t.y,e.y,r.y)}let rA=()=>({translate:0,scale:1,origin:0,originPoint:0}),rM=()=>({x:rA(),y:rA()}),rC=()=>({min:0,max:0}),rR=()=>({x:rC(),y:rC()});function rD(t){return[t("x"),t("y")]}function rV(t){return void 0===t||1===t}function rj({scale:t,scaleX:e,scaleY:r}){return!rV(t)||!rV(e)||!rV(r)}function rL(t){return rj(t)||rO(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function rO(t){var e,r;return(e=t.x)&&"0%"!==e||(r=t.y)&&"0%"!==r}function rF(t,e,r,i,n){return void 0!==n&&(t=i+n*(t-i)),i+r*(t-i)+e}function rN(t,e=0,r=1,i,n){t.min=rF(t.min,e,r,i,n),t.max=rF(t.max,e,r,i,n)}function rI(t,{x:e,y:r}){rN(t.x,e.translate,e.scale,e.originPoint),rN(t.y,r.translate,r.scale,r.originPoint)}function rB(t,e){t.min=t.min+e,t.max=t.max+e}function rz(t,e,r,i,n=.5){let o=tk(t.min,t.max,n);rN(t,e,r,o,i)}function r$(t,e){rz(t.x,e.x,e.scaleX,e.scale,e.originX),rz(t.y,e.y,e.scaleY,e.scale,e.originY)}function rU(t,e){return rx(function(t,e){if(!e)return t;let r=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:r.y,left:r.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}let rW=({current:t})=>t?t.ownerDocument.defaultView:null;function r_(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let rH=(t,e)=>Math.abs(t-e);class rY{constructor(t,e,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{var t,e;if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=rq(this.lastMoveEventInfo,this.history),i=null!==this.startEvent,n=(t=r.offset,e={x:0,y:0},Math.sqrt(rH(t.x,e.x)**2+rH(t.y,e.y)**2)>=3);if(!i&&!n)return;let{point:o}=r,{timestamp:s}=g;this.history.push({...o,timestamp:s});let{onStart:a,onMove:l}=this.handlers;i||(a&&a(this.lastMoveEvent,r),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,r)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=rX(e,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:r,onSessionEnd:i,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=rq("pointercancel"===t.type?this.lastMoveEventInfo:rX(e,this.transformPagePoint),this.history);this.startEvent&&r&&r(t,o),i&&i(t,o)},!rg(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=r,this.contextWindow=i||window;let o=rX(ry(t),this.transformPagePoint),{point:s}=o,{timestamp:a}=g;this.history=[{...s,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,rq(o,this.history)),this.removeListeners=F(rb(this.contextWindow,"pointermove",this.handlePointerMove),rb(this.contextWindow,"pointerup",this.handlePointerUp),rb(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function rX(t,e){return e?{point:e(t.point)}:t}function rZ(t,e){return{x:t.x-e.x,y:t.y-e.y}}function rq({point:t},e){return{point:t,delta:rZ(t,rK(e)),offset:rZ(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let r=t.length-1,i=null,n=rK(t);for(;r>=0&&(i=t[r],!(n.timestamp-i.timestamp>I(.1)));)r--;if(!i)return{x:0,y:0};let o=B(n.timestamp-i.timestamp);if(0===o)return{x:0,y:0};let s={x:(n.x-i.x)/o,y:(n.y-i.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(e,0)}}function rK(t){return t[t.length-1]}function rG(t,e,r){return{min:void 0!==e?t.min+e:void 0,max:void 0!==r?t.max+r-(t.max-t.min):void 0}}function rJ(t,e){let r=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([r,i]=[i,r]),{min:r,max:i}}function rQ(t,e,r){return{min:r0(t,e),max:r0(t,r)}}function r0(t,e){return"number"==typeof t?t:t[e]||0}let r1=new WeakMap;class r2{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=rR(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new rY(t,{onSessionStart:t=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ry(t).point)},onStart:(t,e)=>{let{drag:r,dragPropagation:i,onDragStart:n}=this.getProps();if(r&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===r||"y"===r?rm[r]?null:(rm[r]=!0,()=>{rm[r]=!1}):rm.x||rm.y?null:(rm.x=rm.y=!0,()=>{rm.x=rm.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rD(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:r}=this.visualElement;if(r&&r.layout){let i=r.layout.layoutBox[t];if(i){let t=rw(i);e=parseFloat(e)/100*t}}}this.originPoint[t]=e}),n&&m.postRender(()=>n(t,e)),V(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:r,dragDirectionLock:i,onDirectionLock:n,onDrag:o}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:s}=e;if(i&&null===this.currentDirection){this.currentDirection=function(t,e=10){let r=null;return Math.abs(t.y)>e?r="y":Math.abs(t.x)>e&&(r="x"),r}(s),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,s),this.updateAxis("y",e.point,s),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>rD(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:rW(this.visualElement)})}stop(t,e){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:i}=e;this.startAnimation(i);let{onDragEnd:n}=this.getProps();n&&m.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,r){let{drag:i}=this.getProps();if(!r||!r5(t,i,this.currentDirection))return;let n=this.getAxisMotionValue(t),o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:r},i){return void 0!==e&&t<e?t=i?tk(e,t,i.min):Math.max(t,e):void 0!==r&&t>r&&(t=i?tk(r,t,i.max):Math.min(t,r)),t}(o,this.constraints[t],this.elastic[t])),n.set(o)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&r_(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=function(t,{top:e,left:r,bottom:i,right:n}){return{x:rG(t.x,r,n),y:rG(t.y,e,i)}}(r.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:rQ(t,"left","right"),y:rQ(t,"top","bottom")}}(e),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&rD(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let r={};return void 0!==e.min&&(r.min=e.min-t.min),void 0!==e.max&&(r.max=e.max-t.min),r}(r.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:r}=this.getProps();if(!e||!r_(e))return!1;let i=e.current;U(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let o=function(t,e,r){let i=rU(t,r),{scroll:n}=e;return n&&(rB(i.x,n.offset.x),rB(i.y,n.offset.y)),i}(i,n.root,this.visualElement.getTransformPagePoint()),s={x:rJ((t=n.layout.layoutBox).x,o.x),y:rJ(t.y,o.y)};if(r){let t=r(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(s));this.hasMutatedConstraints=!!t,t&&(s=rx(t))}return s}startAnimation(t){let{drag:e,dragMomentum:r,dragElastic:i,dragTransition:n,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(rD(s=>{if(!r5(s,e,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:r?t[s]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(t,e){let r=this.getAxisMotionValue(t);return V(this.visualElement,t),r.start(e9(t,r,0,e,this.visualElement,!1))}stopAnimation(){rD(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){rD(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps();return r[e]||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){rD(e=>{let{drag:r}=this.getProps();if(!r5(e,r,this.currentDirection))return;let{projection:i}=this.visualElement,n=this.getAxisMotionValue(e);if(i&&i.layout){let{min:r,max:o}=i.layout.layoutBox[e];n.set(t[e]-tk(r,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:r}=this.visualElement;if(!r_(e)||!r||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};rD(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let r=e.get();i[t]=function(t,e){let r=.5,i=rw(t),n=rw(e);return n>i?r=eo(e.min,e.max-i,t.min):i>n&&(r=eo(t.min,t.max-n,e.min)),N(0,1,r)}({min:r,max:r},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),rD(e=>{if(!r5(e,t,null))return;let r=this.getAxisMotionValue(e),{min:n,max:o}=this.constraints[e];r.set(tk(n,o,i[e]))})}addListeners(){if(!this.visualElement.current)return;r1.set(this.visualElement,this);let t=rb(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:r=!0}=this.getProps();e&&r&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();r_(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,i=r.addEventListener("measure",e);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),m.read(e);let n=rf(window,"resize",()=>this.scalePositionWithinConstraints()),o=r.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(rD(e=>{let r=this.getAxisMotionValue(e);r&&(this.originPoint[e]+=t[e].translate,r.set(r.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),i(),o&&o()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:n=!1,dragElastic:o=.35,dragMomentum:s=!0}=t;return{...t,drag:e,dragDirectionLock:r,dragPropagation:i,dragConstraints:n,dragElastic:o,dragMomentum:s}}}function r5(t,e,r){return(!0===e||e===t)&&(null===r||r===t)}class r3 extends rd{constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new r2(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let r4=t=>(e,r)=>{t&&m.postRender(()=>t(e,r))};class r6 extends rd{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(t){this.session=new rY(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rW(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:r4(t),onStart:r4(e),onMove:r,onEnd:(t,e)=>{delete this.session,i&&m.postRender(()=>i(t,e))}}}mount(){this.removePointerDownListener=rb(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var r9=r(7437);let{schedule:r8,cancel:r7}=p(queueMicrotask,!1);var it=r(2265),ie=r(7196),ir=r(781);let ii=(0,it.createContext)({}),io={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function is(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let ia={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!tu.test(t))return t;t=parseFloat(t)}let r=is(t,e.target.x),i=is(t,e.target.y);return`${r}% ${i}%`}},il={};class iu extends it.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r,layoutId:i}=this.props,{projection:n}=t;!function(t){for(let e in t)il[e]=t[e],_(e)&&(il[e].isCSSVariable=!0)}(ic),n&&(e.group&&e.group.add(n),r&&r.register&&i&&r.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),io.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:r,drag:i,isPresent:n}=this.props,{projection:o}=r;return o&&(o.isPresent=n,i||t.layoutDependency!==e||void 0===e||t.isPresent!==n?o.willUpdate():this.safeToRemove(),t.isPresent===n||(n?o.promote():o.relegate()||m.postRender(()=>{let t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),r8.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function id(t){let[e,r]=(0,ie.oO)(),i=(0,it.useContext)(ir.p);return(0,r9.jsx)(iu,{...t,layoutGroup:i,switchLayoutGroup:(0,it.useContext)(ii),isPresent:e,safeToRemove:r})}let ic={borderRadius:{...ia,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ia,borderTopRightRadius:ia,borderBottomLeftRadius:ia,borderBottomRightRadius:ia,boxShadow:{correct:(t,{treeScale:e,projectionDelta:r})=>{let i=tS.parse(t);if(i.length>5)return t;let n=tS.createTransformer(t),o="number"!=typeof i[0]?1:0,s=r.x.scale*e.x,a=r.y.scale*e.y;i[0+o]/=s,i[1+o]/=a;let l=tk(s,a,.5);return"number"==typeof i[2+o]&&(i[2+o]/=l),"number"==typeof i[3+o]&&(i[3+o]/=l),n(i)}}};var ih=r(1643);function ip(t){return(0,ih.K)(t)&&"ownerSVGElement"in t}let im=(t,e)=>t.depth-e.depth;class ig{constructor(){this.children=[],this.isDirty=!1}add(t){w(this.children,t),this.isDirty=!0}remove(t){T(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(im),this.isDirty=!1,this.children.forEach(t)}}function iy(t){return D(t)?t.get():t}let iv=["TopLeft","TopRight","BottomLeft","BottomRight"],ib=iv.length,ix=t=>"string"==typeof t?parseFloat(t):t,iw=t=>"number"==typeof t||tu.test(t);function iT(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let iS=iE(0,.5,t7),iP=iE(.5,.95,u);function iE(t,e,r){return i=>i<t?0:i>e?1:r(eo(t,e,i))}function ik(t,e){t.min=e.min,t.max=e.max}function iA(t,e){ik(t.x,e.x),ik(t.y,e.y)}function iM(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function iC(t,e,r,i,n){return t-=e,t=i+1/r*(t-i),void 0!==n&&(t=i+1/n*(t-i)),t}function iR(t,e,[r,i,n],o,s){!function(t,e=0,r=1,i=.5,n,o=t,s=t){if(tl.test(e)&&(e=parseFloat(e),e=tk(s.min,s.max,e/100)-s.min),"number"!=typeof e)return;let a=tk(o.min,o.max,i);t===o&&(a-=e),t.min=iC(t.min,e,r,a,n),t.max=iC(t.max,e,r,a,n)}(t,e[r],e[i],e[n],e.scale,o,s)}let iD=["x","scaleX","originX"],iV=["y","scaleY","originY"];function ij(t,e,r,i){iR(t.x,e,iD,r?r.x:void 0,i?i.x:void 0),iR(t.y,e,iV,r?r.y:void 0,i?i.y:void 0)}function iL(t){return 0===t.translate&&1===t.scale}function iO(t){return iL(t.x)&&iL(t.y)}function iF(t,e){return t.min===e.min&&t.max===e.max}function iN(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function iI(t,e){return iN(t.x,e.x)&&iN(t.y,e.y)}function iB(t){return rw(t.x)/rw(t.y)}function iz(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class i${constructor(){this.members=[]}add(t){w(this.members,t),t.scheduleRender()}remove(t){if(T(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let r=this.members.findIndex(e=>t===e);if(0===r)return!1;for(let t=r;t>=0;t--){let r=this.members[t];if(!1!==r.isPresent){e=r;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,e&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:i}=t.options;!1===i&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:r}=t;e.onExitComplete&&e.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let iU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},iW=["","X","Y","Z"],i_={visibility:"hidden"},iH=0;function iY(t,e,r,i){let{latestValues:n}=e;n[t]&&(r[t]=n[t],e.setStaticValue(t,0),i&&(i[t]=0))}function iX({attachResizeListener:t,defaultParent:e,measureScroll:r,checkIsScrollRoot:i,resetTransform:n}){return class{constructor(t={},r=e?.()){this.id=iH++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,h.value&&(iU.nodes=iU.calculatedTargetDeltas=iU.calculatedProjections=0),this.nodes.forEach(iK),this.nodes.forEach(i5),this.nodes.forEach(i3),this.nodes.forEach(iG),h.addProjectionMetrics&&h.addProjectionMetrics(iU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new ig)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new S),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let r=this.eventHandlers.get(t);r&&r.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=ip(e)&&!(ip(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:r,layout:i,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(i||r)&&(this.isLayoutDirty=!0),t){let r;let i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(t,e){let r=E.now(),i=({timestamp:n})=>{let o=n-r;o>=e&&(f(i),t(o-e))};return m.setup(i,!0),()=>f(i)}(i,250),io.hasAnimatedSinceResize&&(io.hasAnimatedSinceResize=!1,this.nodes.forEach(i2))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&n&&(r||i)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:r,layout:i})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||n.getDefaultTransition()||nt,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=n.getProps(),u=!this.targetLayout||!iI(this.targetLayout,i),d=!e&&r;if(this.options.layoutRoot||this.resumeFrom||d||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...l(o,"layout"),onPlay:s,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,d)}else e||i2(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(i4),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:r}=e.options;if(!r)return;let i=r.props[L];if(window.MotionHasOptimisedAnimation(i,"transform")){let{layout:t,layoutId:r}=e.options;window.MotionCancelOptimisedAnimation(i,"transform",m,!(t||r))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:r}=this.options;if(void 0===e&&!r)return;let i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(iQ);return}this.isUpdating||this.nodes.forEach(i0),this.isUpdating=!1,this.nodes.forEach(i1),this.nodes.forEach(iZ),this.nodes.forEach(iq),this.clearAllSnapshots();let t=E.now();g.delta=N(0,1e3/60,t-g.timestamp),g.timestamp=t,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,r8.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(iJ),this.sharedNodes.forEach(i6)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||rw(this.snapshot.measuredBox.x)||rw(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=rR(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!iO(this.projectionDelta),r=this.getTransformTemplate(),i=r?r(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||rL(this.latestValues)||o)&&(n(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let r=this.measurePageBox(),i=this.removeElementScroll(r);return t&&(i=this.removeTransform(i)),ni((e=i).x),ni(e.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return rR();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(no))){let{scroll:t}=this.root;t&&(rB(e.x,t.offset.x),rB(e.y,t.offset.y))}return e}removeElementScroll(t){let e=rR();if(iA(e,t),this.scroll?.wasRoot)return e;for(let r=0;r<this.path.length;r++){let i=this.path[r],{scroll:n,options:o}=i;i!==this.root&&n&&o.layoutScroll&&(n.wasRoot&&iA(e,t),rB(e.x,n.offset.x),rB(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let r=rR();iA(r,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&r$(r,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),rL(i.latestValues)&&r$(r,i.latestValues)}return rL(this.latestValues)&&r$(r,this.latestValues),r}removeTransform(t){let e=rR();iA(e,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];if(!r.instance||!rL(r.latestValues))continue;rj(r.latestValues)&&r.updateSnapshot();let i=rR();iA(i,r.measurePageBox()),ij(e,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,i)}return rL(this.latestValues)&&ij(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let r=!!this.resumingFrom||this!==e;if(!(t||r&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:i,layoutId:n}=this.options;if(this.layout&&(i||n)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rR(),this.relativeTargetOrigin=rR(),rk(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),iA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=rR(),this.targetWithTransforms=rR()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,s,a;this.forceRelativeParentToResolveTarget(),o=this.target,s=this.relativeTarget,a=this.relativeParent.target,rP(o.x,s.x,a.x),rP(o.y,s.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iA(this.target,this.layout.layoutBox),rI(this.target,this.targetDelta)):iA(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rR(),this.relativeTargetOrigin=rR(),rk(this.relativeTargetOrigin,this.target,t.target),iA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}h.value&&iU.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||rj(this.parent.latestValues)||rO(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,r=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(r=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===g.timestamp&&(r=!1),r)return;let{layout:i,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||n))return;iA(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,s=this.treeScale.y;!function(t,e,r,i=!1){let n,o;let s=r.length;if(s){e.x=e.y=1;for(let a=0;a<s;a++){o=(n=r[a]).projectionDelta;let{visualElement:s}=n.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(i&&n.options.layoutScroll&&n.scroll&&n!==n.root&&r$(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,rI(t,o)),i&&rL(n.latestValues)&&r$(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=rR());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(iM(this.prevProjectionDelta.x,this.projectionDelta.x),iM(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rS(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===s&&iz(this.projectionDelta.x,this.prevProjectionDelta.x)&&iz(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),h.value&&iU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rM(),this.projectionDelta=rM(),this.projectionDeltaWithTransform=rM()}setAnimationOrigin(t,e=!1){let r;let i=this.snapshot,n=i?i.latestValues:{},o={...this.latestValues},s=rM();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=rR(),l=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),d=!u||u.members.length<=1,c=!!(l&&!d&&!0===this.options.crossfade&&!this.path.some(i7));this.animationProgress=0,this.mixTargetDelta=e=>{let i=e/1e3;if(i9(s.x,t.x,i),i9(s.y,t.y,i),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,p,m;rk(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,i8(p.x,m.x,a.x,i),i8(p.y,m.y,a.y,i),r&&(u=this.relativeTarget,h=r,iF(u.x,h.x)&&iF(u.y,h.y))&&(this.isProjectionDirty=!1),r||(r=rR()),iA(r,this.relativeTarget)}l&&(this.animationValues=o,function(t,e,r,i,n,o){n?(t.opacity=tk(0,r.opacity??1,iS(i)),t.opacityExit=tk(e.opacity??1,0,iP(i))):o&&(t.opacity=tk(e.opacity??1,r.opacity??1,i));for(let n=0;n<ib;n++){let o=`border${iv[n]}Radius`,s=iT(e,o),a=iT(r,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||iw(s)===iw(a)?(t[o]=Math.max(tk(ix(s),ix(a),i),0),(tl.test(a)||tl.test(s))&&(t[o]+="%")):t[o]=a)}(e.rotate||r.rotate)&&(t.rotate=tk(e.rotate||0,r.rotate||0,i))}(o,n,this.latestValues,i,c,d)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{io.hasAnimatedSinceResize=!0,z.layout++,this.motionValue||(this.motionValue=C(0)),this.currentAnimation=function(t,e,r){let i=D(t)?t:C(t);return i.start(e9("",i,e,r)),i.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{z.layout--},onComplete:()=>{z.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:r,layout:i,latestValues:n}=t;if(e&&r&&i){if(this!==t&&this.layout&&i&&nn(this.options.animationType,this.layout.layoutBox,i.layoutBox)){r=this.target||rR();let e=rw(this.layout.layoutBox.x);r.x.min=t.target.x.min,r.x.max=r.x.min+e;let i=rw(this.layout.layoutBox.y);r.y.min=t.target.y.min,r.y.max=r.y.min+i}iA(e,r),r$(e,n),rS(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new i$),this.sharedNodes.get(t).add(e);let r=e.options.initialPromotionConfig;e.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:r}={}){let i=this.getStack();i&&i.promote(this,r),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:r}=t;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(e=!0),!e)return;let i={};r.z&&iY("z",t,i,this.animationValues);for(let e=0;e<iW.length;e++)iY(`rotate${iW[e]}`,t,i,this.animationValues),iY(`skew${iW[e]}`,t,i,this.animationValues);for(let e in t.render(),i)t.setStaticValue(e,i[e]),this.animationValues&&(this.animationValues[e]=i[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return i_;let e={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=iy(t?.pointerEvents)||"",e.transform=r?r(this.latestValues,""):"none",e;let i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=iy(t?.pointerEvents)||""),this.hasProjected&&!rL(this.latestValues)&&(e.transform=r?r({},""):"none",this.hasProjected=!1),e}let n=i.animationValues||i.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,r){let i="",n=t.x.translate/e.x,o=t.y.translate/e.y,s=r?.z||0;if((n||o||s)&&(i=`translate3d(${n}px, ${o}px, ${s}px) `),(1!==e.x||1!==e.y)&&(i+=`scale(${1/e.x}, ${1/e.y}) `),r){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:o,skewX:s,skewY:a}=r;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),n&&(i+=`rotateX(${n}deg) `),o&&(i+=`rotateY(${o}deg) `),s&&(i+=`skewX(${s}deg) `),a&&(i+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),r&&(e.transform=r(n,e.transform));let{x:o,y:s}=this.projectionDelta;for(let t in e.transformOrigin=`${100*o.origin}% ${100*s.origin}% 0`,i.animationValues?e.opacity=i===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=i===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,il){if(void 0===n[t])continue;let{correct:r,applyTo:o,isCSSVariable:s}=il[t],a="none"===e.transform?n[t]:r(n[t],i);if(o){let t=o.length;for(let r=0;r<t;r++)e[o[r]]=a}else s?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=i===this?iy(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(iQ),this.root.sharedNodes.clear()}}}function iZ(t){t.updateLayout()}function iq(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:r,measuredBox:i}=t.layout,{animationType:n}=t.options,o=e.source!==t.layout.source;"size"===n?rD(t=>{let i=o?e.measuredBox[t]:e.layoutBox[t],n=rw(i);i.min=r[t].min,i.max=i.min+n}):nn(n,e.layoutBox,r)&&rD(i=>{let n=o?e.measuredBox[i]:e.layoutBox[i],s=rw(r[i]);n.max=n.min+s,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+s)});let s=rM();rS(s,r,e.layoutBox);let a=rM();o?rS(a,t.applyTransform(i,!0),e.measuredBox):rS(a,r,e.layoutBox);let l=!iO(s),u=!1;if(!t.resumeFrom){let i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){let{snapshot:n,layout:o}=i;if(n&&o){let s=rR();rk(s,e.layoutBox,n.layoutBox);let a=rR();rk(a,r,o.layoutBox),iI(s,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=s,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:r,snapshot:e,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function iK(t){h.value&&iU.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function iG(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function iJ(t){t.clearSnapshot()}function iQ(t){t.clearMeasurements()}function i0(t){t.isLayoutDirty=!1}function i1(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function i2(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function i5(t){t.resolveTargetDelta()}function i3(t){t.calcProjection()}function i4(t){t.resetSkewAndRotation()}function i6(t){t.removeLeadSnapshot()}function i9(t,e,r){t.translate=tk(e.translate,0,r),t.scale=tk(e.scale,1,r),t.origin=e.origin,t.originPoint=e.originPoint}function i8(t,e,r,i){t.min=tk(e.min,r.min,i),t.max=tk(e.max,r.max,i)}function i7(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nt={duration:.45,ease:[.4,0,.1,1]},ne=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nr=ne("applewebkit/")&&!ne("chrome/")?Math.round:u;function ni(t){t.min=nr(t.min),t.max=nr(t.max)}function nn(t,e,r){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(iB(e)-iB(r)))}function no(t){return t!==t.root&&t.scroll?.wasRoot}let ns=iX({attachResizeListener:(t,e)=>rf(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),na={current:void 0},nl=iX({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!na.current){let t=new ns({});t.mount(window),t.setOptions({layoutScroll:!0}),na.current=t}return na.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function nu(t,e){let r=function(t,e,r){if(t instanceof EventTarget)return[t];if("string"==typeof t){let r=document;e&&(r=e.current);let i=(void 0)??r.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),i=new AbortController;return[r,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function nd(t){return!("touch"===t.pointerType||rm.x||rm.y)}function nc(t,e,r){let{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===r);let n=i["onHover"+r];n&&m.postRender(()=>n(e,ry(e)))}class nh extends rd{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,r={}){let[i,n,o]=nu(t,r),s=t=>{if(!nd(t))return;let{target:r}=t,i=e(r,t);if("function"!=typeof i||!r)return;let o=t=>{nd(t)&&(i(t),r.removeEventListener("pointerleave",o))};r.addEventListener("pointerleave",o,n)};return i.forEach(t=>{t.addEventListener("pointerenter",s,n)}),o}(t,(t,e)=>(nc(this.node,e,"Start"),t=>nc(this.node,t,"End"))))}unmount(){}}class np extends rd{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=F(rf(this.node.current,"focus",()=>this.onFocus()),rf(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let nm=(t,e)=>!!e&&(t===e||nm(t,e.parentElement)),nf=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ng=new WeakSet;function ny(t){return e=>{"Enter"===e.key&&t(e)}}function nv(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let nb=(t,e)=>{let r=t.currentTarget;if(!r)return;let i=ny(()=>{if(ng.has(r))return;nv(r,"down");let t=ny(()=>{nv(r,"up")});r.addEventListener("keyup",t,e),r.addEventListener("blur",()=>nv(r,"cancel"),e)});r.addEventListener("keydown",i,e),r.addEventListener("blur",()=>r.removeEventListener("keydown",i),e)};function nx(t){return rg(t)&&!(rm.x||rm.y)}function nw(t,e,r){let{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===r);let n=i["onTap"+("End"===r?"":r)];n&&m.postRender(()=>n(e,ry(e)))}class nT extends rd{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,r={}){let[i,n,o]=nu(t,r),s=t=>{let i=t.currentTarget;if(!nx(t))return;ng.add(i);let o=e(i,t),s=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),ng.has(i)&&ng.delete(i),nx(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{s(t,i===window||i===document||r.useGlobalTarget||nm(i,t.target))},l=t=>{s(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return i.forEach(t=>{(r.useGlobalTarget?window:t).addEventListener("pointerdown",s,n),(0,eG.R)(t)&&(t.addEventListener("focus",t=>nb(t,n)),nf.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),o}(t,(t,e)=>(nw(this.node,e,"Start"),(t,{success:e})=>nw(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nS=new WeakMap,nP=new WeakMap,nE=t=>{let e=nS.get(t.target);e&&e(t)},nk=t=>{t.forEach(nE)},nA={some:0,all:1};class nM extends rd{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:r,amount:i="some",once:n}=t,o={root:e?e.current:void 0,rootMargin:r,threshold:"number"==typeof i?i:nA[i]};return function(t,e,r){let i=function({root:t,...e}){let r=t||document;nP.has(r)||nP.set(r,{});let i=nP.get(r),n=JSON.stringify(e);return i[n]||(i[n]=new IntersectionObserver(nk,{root:t,...e})),i[n]}(e);return nS.set(t,r),i.observe(t),()=>{nS.delete(t),i.unobserve(t)}}(this.node.current,o,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:r,onViewportLeave:i}=this.node.getProps(),o=e?r:i;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return r=>t[r]!==e[r]}(t,e))&&this.startObserver()}unmount(){}}let nC=(0,it.createContext)({strict:!1});var nR=r(5968);let nD=(0,it.createContext)({});function nV(t){return n(t.animate)||rn.some(e=>rr(t[e]))}function nj(t){return!!(nV(t)||t.variants)}function nL(t){return Array.isArray(t)?t.join(" "):t}var nO=r(6613);let nF={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nN={};for(let t in nF)nN[t]={isEnabled:e=>nF[t].some(t=>!!e[t])};let nI=Symbol.for("motionComponentSymbol");var nB=r(8243),nz=r(538);function n$(t,{layout:e,layoutId:r}){return b.has(t)||t.startsWith("origin")||(e||void 0!==r)&&(!!il[t]||"opacity"===t)}let nU=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nW={...Z,transform:Math.round},n_={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:K,scaleX:K,scaleY:K,scaleZ:K,skew:ta,skewX:ta,skewY:ta,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:q,originX:th,originY:th,originZ:tu,zIndex:nW,fillOpacity:q,strokeOpacity:q,numOctaves:nW},nH={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nY=v.length;function nX(t,e,r){let{style:i,vars:n,transformOrigin:o}=t,s=!1,a=!1;for(let t in e){let r=e[t];if(b.has(t)){s=!0;continue}if(_(t)){n[t]=r;continue}{let e=nU(r,n_[t]);t.startsWith("origin")?(a=!0,o[t]=e):i[t]=e}}if(!e.transform&&(s||r?i.transform=function(t,e,r){let i="",n=!0;for(let o=0;o<nY;o++){let s=v[o],a=t[s];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===(s.startsWith("scale")?1:0):0===parseFloat(a))||r){let t=nU(a,n_[s]);if(!l){n=!1;let e=nH[s]||s;i+=`${e}(${t}) `}r&&(e[s]=t)}}return i=i.trim(),r?i=r(e,n?"":i):n&&(i="none"),i}(e,t.transform,r):i.transform&&(i.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:r=0}=o;i.transformOrigin=`${t} ${e} ${r}`}}let nZ=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nq(t,e,r){for(let i in e)D(e[i])||n$(i,r)||(t[i]=e[i])}let nK={offset:"stroke-dashoffset",array:"stroke-dasharray"},nG={offset:"strokeDashoffset",array:"strokeDasharray"};function nJ(t,{attrX:e,attrY:r,attrScale:i,pathLength:n,pathSpacing:o=1,pathOffset:s=0,...a},l,u,d){if(nX(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:h}=t;c.transform&&(h.transform=c.transform,delete c.transform),(h.transform||c.transformOrigin)&&(h.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),h.transform&&(h.transformBox=d?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==r&&(c.y=r),void 0!==i&&(c.scale=i),void 0!==n&&function(t,e,r=1,i=0,n=!0){t.pathLength=1;let o=n?nK:nG;t[o.offset]=tu.transform(-i);let s=tu.transform(e),a=tu.transform(r);t[o.array]=`${s} ${a}`}(c,n,o,s,!1)}let nQ=()=>({...nZ(),attrs:{}}),n0=t=>"string"==typeof t&&"svg"===t.toLowerCase(),n1=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function n2(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||n1.has(t)}let n5=t=>!n2(t);try{(eZ=require("@emotion/is-prop-valid").default)&&(n5=t=>t.startsWith("on")?!n2(t):eZ(t))}catch{}let n3=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function n4(t){if("string"!=typeof t||t.includes("-"));else if(n3.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var n6=r(961);let n9=t=>(e,r)=>{let i=(0,it.useContext)(nD),o=(0,it.useContext)(nB.O),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},r,i,o){return{latestValues:function(t,e,r,i){let o={},a=i(t,{});for(let t in a)o[t]=iy(a[t]);let{initial:l,animate:u}=t,d=nV(t),c=nj(t);e&&c&&!d&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let h=!!r&&!1===r.initial,p=(h=h||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let r=0;r<e.length;r++){let i=s(t,e[r]);if(i){let{transitionEnd:t,transition:e,...r}=i;for(let t in r){let e=r[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(o[t]=e)}for(let e in t)o[e]=t[e]}}}return o}(r,i,o,t),renderState:e()}})(t,e,i,o);return r?a():(0,n6.h)(a)};function n8(t,e,r){let{style:i}=t,n={};for(let o in i)(D(i[o])||e.style&&D(e.style[o])||n$(o,t)||r?.getValue(o)?.liveStyle!==void 0)&&(n[o]=i[o]);return n}let n7={useVisualState:n9({scrapeMotionValuesFromProps:n8,createRenderState:nZ})};function ot(t,e,r){let i=n8(t,e,r);for(let r in t)(D(t[r])||D(e[r]))&&(i[-1!==v.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=t[r]);return i}let oe={useVisualState:n9({scrapeMotionValuesFromProps:ot,createRenderState:nQ})},or=t=>e=>e.test(t),oi=[Z,tu,tl,ta,tc,td,{test:t=>"auto"===t,parse:t=>t}],on=t=>oi.find(or(t)),oo=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),os=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,oa=t=>/^0[^.\s]+$/u.test(t),ol=new Set(["brightness","contrast","saturate","opacity"]);function ou(t){let[e,r]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[i]=r.match(J)||[];if(!i)return t;let n=r.replace(i,""),o=ol.has(e)?1:0;return i!==r&&(o*=100),e+"("+o+n+")"}let od=/\b([a-z-]*)\(.*?\)/gu,oc={...tS,getAnimatableNone:t=>{let e=t.match(od);return e?e.map(ou).join(" "):t}},oh={...n_,color:tm,backgroundColor:tm,outlineColor:tm,fill:tm,stroke:tm,borderColor:tm,borderTopColor:tm,borderRightColor:tm,borderBottomColor:tm,borderLeftColor:tm,filter:oc,WebkitFilter:oc},op=t=>oh[t];function om(t,e){let r=op(t);return r!==oc&&(r=tS),r.getAnimatableNone?r.getAnimatableNone(e):void 0}let of=new Set(["auto","none","0"]);class og extends eO{constructor(t,e,r,i,n){super(t,e,r,i,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:r}=this;if(!e||!e.current)return;super.readKeyframes();for(let r=0;r<t.length;r++){let i=t[r];if("string"==typeof i&&Y(i=i.trim())){let n=function t(e,r,i=1){U(i<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,o]=function(t){let e=os.exec(t);if(!e)return[,];let[,r,i,n]=e;return[`--${r??i}`,n]}(e);if(!n)return;let s=window.getComputedStyle(r).getPropertyValue(n);if(s){let t=s.trim();return oo(t)?parseFloat(t):t}return Y(o)?t(o,r,i+1):o}(i,e.current);void 0!==n&&(t[r]=n),r===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!x.has(r)||2!==t.length)return;let[i,n]=t,o=on(i),s=on(n);if(o!==s){if(eE(o)&&eE(s))for(let e=0;e<t.length;e++){let r=t[e];"string"==typeof r&&(t[e]=parseFloat(r))}else eM[r]&&(this.needsMeasurement=!0)}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,r=[];for(let e=0;e<t.length;e++){var i;(null===t[e]||("number"==typeof(i=t[e])?0===i:null===i||"none"===i||"0"===i||oa(i)))&&r.push(e)}r.length&&function(t,e,r){let i,n=0;for(;n<t.length&&!i;){let e=t[n];"string"==typeof e&&!of.has(e)&&tb(e).values.length&&(i=t[n]),n++}if(i&&r)for(let n of e)t[n]=om(r,i)}(t,r,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:r}=this;if(!t||!t.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eM[r](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let i=e[e.length-1];void 0!==i&&t.getValue(r,i).jump(i,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:r}=this;if(!t||!t.current)return;let i=t.getValue(e);i&&i.jump(this.measuredOrigin,!1);let n=r.length-1,o=r[n];r[n]=eM[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,r])=>{t.getValue(e).set(r)}),this.resolveNoneKeyframes()}}let oy=[...oi,tm,tS],ov=t=>oy.find(or(t)),ob={current:null},ox={current:!1},ow=new WeakMap,oT=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class oS{scrapeMotionValuesFromProps(t,e,r){return{}}constructor({parent:t,props:e,presenceContext:r,reducedMotionConfig:i,blockInitialAnimation:n,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eO,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=E.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,m.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.blockInitialAnimation=!!n,this.isControllingVariants=nV(e),this.isVariantNode=nj(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&D(e)&&e.set(a[t],!1)}}mount(t){this.current=t,ow.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),ox.current||function(){if(ox.current=!0,nO.j){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ob.current=t.matches;t.addListener(e),e()}else ob.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ob.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let r;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let i=b.has(t);i&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&m.preRender(this.notifyUpdate),i&&this.projection&&(this.projection.isTransformDirty=!0)}),o=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),o(),r&&r(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nN){let e=nN[t];if(!e)continue;let{isEnabled:r,Feature:i}=e;if(!this.features[t]&&i&&r(this.props)&&(this.features[t]=new i(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):rR()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<oT.length;e++){let r=oT[e];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let i=t["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=function(t,e,r){for(let i in e){let n=e[i],o=r[i];if(D(n))t.addValue(i,n);else if(D(o))t.addValue(i,C(n,{owner:t}));else if(o!==n){if(t.hasValue(i)){let e=t.getValue(i);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(i);t.addValue(i,C(void 0!==e?e:n,{owner:t}))}}}for(let i in r)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let r=this.values.get(t);e!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return void 0===r&&void 0!==e&&(r=C(null===e?void 0:e,{owner:this}),this.addValue(t,r)),r}readValue(t,e){let r=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=r&&("string"==typeof r&&(oo(r)||oa(r))?r=parseFloat(r):!ov(r)&&tS.test(e)&&(r=om(t,e)),this.setBaseTarget(t,D(r)?r.get():r)),D(r)?r.get():r}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e;let{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let i=s(this.props,r,this.presenceContext?.custom);i&&(e=i[t])}if(r&&void 0!==e)return e;let i=this.getBaseTargetFromProps(this.props,t);return void 0===i||D(i)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new S),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class oP extends oS{constructor(){super(...arguments),this.KeyframeResolver=og}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:r}){delete e[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;D(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function oE(t,{style:e,vars:r},i,n){for(let o in Object.assign(t.style,e,n&&n.getProjectionStyles(i)),r)t.style.setProperty(o,r[o])}class ok extends oP{constructor(){super(...arguments),this.type="html",this.renderInstance=oE}readValueFromInstance(t,e){if(b.has(e))return this.projection?.isProjecting?ew(e):eS(t,e);{let r=window.getComputedStyle(t),i=(_(e)?r.getPropertyValue(e):r[e])||0;return"string"==typeof i?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:e}){return rU(t,e)}build(t,e,r){nX(t,e,r.transformTemplate)}scrapeMotionValuesFromProps(t,e,r){return n8(t,e,r)}}let oA=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class oM extends oP{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=rR}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(b.has(e)){let t=op(e);return t&&t.default||0}return e=oA.has(e)?e:j(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,r){return ot(t,e,r)}build(t,e,r){nJ(t,e,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(t,e,r,i){!function(t,e,r,i){for(let r in oE(t,e,void 0,i),e.attrs)t.setAttribute(oA.has(r)?r:j(r),e.attrs[r])}(t,e,0,i)}mount(t){this.isSVGTag=n0(t.tagName),super.mount(t)}}let oC=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(r,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}((eq={animation:{Feature:rc},exit:{Feature:rp},inView:{Feature:nM},tap:{Feature:nT},focus:{Feature:np},hover:{Feature:nh},pan:{Feature:r6},drag:{Feature:r3,ProjectionNode:nl,MeasureLayout:id},layout:{ProjectionNode:nl,MeasureLayout:id}},eK=(t,e)=>n4(t)?new oM(e):new ok(e,{allowProjection:t!==it.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:r,useVisualState:i,Component:n}){function o(t,o){var s;let a;let l={...(0,it.useContext)(nR._),...t,layoutId:function({layoutId:t}){let e=(0,it.useContext)(ir.p).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:u}=l,d=function(t){let{initial:e,animate:r}=function(t,e){if(nV(t)){let{initial:e,animate:r}=t;return{initial:!1===e||rr(e)?e:void 0,animate:rr(r)?r:void 0}}return!1!==t.inherit?e:{}}(t,(0,it.useContext)(nD));return(0,it.useMemo)(()=>({initial:e,animate:r}),[nL(e),nL(r)])}(t),c=i(t,u);if(!u&&nO.j){(0,it.useContext)(nC).strict;let t=function(t){let{drag:e,layout:r}=nN;if(!e&&!r)return{};let i={...e,...r};return{MeasureLayout:e?.isEnabled(t)||r?.isEnabled(t)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(l);a=t.MeasureLayout,d.visualElement=function(t,e,r,i,n){let{visualElement:o}=(0,it.useContext)(nD),s=(0,it.useContext)(nC),a=(0,it.useContext)(nB.O),l=(0,it.useContext)(nR._).reducedMotion,u=(0,it.useRef)(null);i=i||s.renderer,!u.current&&i&&(u.current=i(t,{visualState:e,parent:o,props:r,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let d=u.current,c=(0,it.useContext)(ii);d&&!d.projection&&n&&("html"===d.type||"svg"===d.type)&&function(t,e,r,i){let{layoutId:n,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:d}=e;t.projection=new r(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:o,alwaysMeasureLayout:!!s||a&&r_(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,crossfade:d,layoutScroll:l,layoutRoot:u})}(u.current,r,n,c);let h=(0,it.useRef)(!1);(0,it.useInsertionEffect)(()=>{d&&h.current&&d.update(r,a)});let p=r[L],m=(0,it.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,nz.L)(()=>{d&&(h.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),r8.render(d.render),m.current&&d.animationState&&d.animationState.animateChanges())}),(0,it.useEffect)(()=>{d&&(!m.current&&d.animationState&&d.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),d}(n,c,l,e,t.ProjectionNode)}return(0,r9.jsxs)(nD.Provider,{value:d,children:[a&&d.visualElement?(0,r9.jsx)(a,{visualElement:d.visualElement,...l}):null,r(n,t,(s=d.visualElement,(0,it.useCallback)(t=>{t&&c.onMount&&c.onMount(t),s&&(t?s.mount(t):s.unmount()),o&&("function"==typeof o?o(t):r_(o)&&(o.current=t))},[s])),c,u,d.visualElement)]})}t&&function(t){for(let e in t)nN[e]={...nN[e],...t[e]}}(t),o.displayName=`motion.${"string"==typeof n?n:`create(${n.displayName??n.name??""})`}`;let s=(0,it.forwardRef)(o);return s[nI]=n,s}({...n4(t)?oe:n7,preloadedFeatures:eq,useRender:function(t=!1){return(e,r,i,{latestValues:n},o)=>{let s=(n4(e)?function(t,e,r,i){let n=(0,it.useMemo)(()=>{let r=nQ();return nJ(r,e,n0(i),t.transformTemplate,t.style),{...r.attrs,style:{...r.style}}},[e]);if(t.style){let e={};nq(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let r={},i=function(t,e){let r=t.style||{},i={};return nq(i,r,t),Object.assign(i,function({transformTemplate:t},e){return(0,it.useMemo)(()=>{let r=nZ();return nX(r,e,t),Object.assign({},r.vars,r.style)},[e])}(t,e)),i}(t,e);return t.drag&&!1!==t.dragListener&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(r.tabIndex=0),r.style=i,r})(r,n,o,e),a=function(t,e,r){let i={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(n5(n)||!0===r&&n2(n)||!e&&!n2(n)||t.draggable&&n.startsWith("onDrag"))&&(i[n]=t[n]);return i}(r,"string"==typeof e,t),l=e!==it.Fragment?{...a,...s,ref:i}:{},{children:u}=r,d=(0,it.useMemo)(()=>D(u)?u.get():u,[u]);return(0,it.createElement)(e,{...l,children:d})}}(e),createVisualElement:eK,Component:t})}))},6613:function(t,e,r){r.d(e,{j:function(){return i}});let i="undefined"!=typeof window},961:function(t,e,r){r.d(e,{h:function(){return n}});var i=r(2265);function n(t){let e=(0,i.useRef)(null);return null===e.current&&(e.current=t()),e.current}},538:function(t,e,r){r.d(e,{L:function(){return n}});var i=r(2265);let n=r(6613).j?i.useLayoutEffect:i.useEffect},6119:function(t,e,r){r.d(e,{R:function(){return n}});var i=r(1643);function n(t){return(0,i.K)(t)&&"offsetHeight"in t}},1643:function(t,e,r){r.d(e,{K:function(){return i}});function i(t){return"object"==typeof t&&null!==t}},5925:function(t,e,r){let i,n;r.r(e),r.d(e,{CheckmarkIcon:function(){return K},ErrorIcon:function(){return H},LoaderIcon:function(){return X},ToastBar:function(){return ta},ToastIcon:function(){return te},Toaster:function(){return tc},default:function(){return th},resolveValue:function(){return P},toast:function(){return O},useToaster:function(){return $},useToasterStore:function(){return V}});var o,s=r(2265);let a={data:""},l=t=>"object"==typeof window?((t?t.querySelector("#_goober"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:t||a,u=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,d=/\/\*[^]*?\*\/|  +/g,c=/\n+/g,h=(t,e)=>{let r="",i="",n="";for(let o in t){let s=t[o];"@"==o[0]?"i"==o[1]?r=o+" "+s+";":i+="f"==o[1]?h(s,o):o+"{"+h(s,"k"==o[1]?"":e)+"}":"object"==typeof s?i+=h(s,e?e.replace(/([^,])+/g,t=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,e=>/&/.test(e)?e.replace(/&/g,t):t?t+" "+e:e)):o):null!=s&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),n+=h.p?h.p(o,s):o+":"+s+";")}return r+(e&&n?e+"{"+n+"}":n)+i},p={},m=t=>{if("object"==typeof t){let e="";for(let r in t)e+=r+m(t[r]);return e}return t},f=(t,e,r,i,n)=>{var o;let s=m(t),a=p[s]||(p[s]=(t=>{let e=0,r=11;for(;e<t.length;)r=101*r+t.charCodeAt(e++)>>>0;return"go"+r})(s));if(!p[a]){let e=s!==t?t:(t=>{let e,r,i=[{}];for(;e=u.exec(t.replace(d,""));)e[4]?i.shift():e[3]?(r=e[3].replace(c," ").trim(),i.unshift(i[0][r]=i[0][r]||{})):i[0][e[1]]=e[2].replace(c," ").trim();return i[0]})(t);p[a]=h(n?{["@keyframes "+a]:e}:e,r?"":"."+a)}let l=r&&p.g?p.g:null;return r&&(p.g=p[a]),o=p[a],l?e.data=e.data.replace(l,o):-1===e.data.indexOf(o)&&(e.data=i?o+e.data:e.data+o),a},g=(t,e,r)=>t.reduce((t,i,n)=>{let o=e[n];if(o&&o.call){let t=o(r),e=t&&t.props&&t.props.className||/^go/.test(t)&&t;o=e?"."+e:t&&"object"==typeof t?t.props?"":h(t,""):!1===t?"":t}return t+i+(null==o?"":o)},"");function y(t){let e=this||{},r=t.call?t(e.p):t;return f(r.unshift?r.raw?g(r,[].slice.call(arguments,1),e.p):r.reduce((t,r)=>Object.assign(t,r&&r.call?r(e.p):r),{}):r,l(e.target),e.g,e.o,e.k)}y.bind({g:1});let v,b,x,w=y.bind({k:1});function T(t,e){let r=this||{};return function(){let i=arguments;function n(o,s){let a=Object.assign({},o),l=a.className||n.className;r.p=Object.assign({theme:b&&b()},a),r.o=/ *go\d+/.test(l),a.className=y.apply(r,i)+(l?" "+l:""),e&&(a.ref=s);let u=t;return t[0]&&(u=a.as||t,delete a.as),x&&u[0]&&x(a),v(u,a)}return e?e(n):n}}var S=t=>"function"==typeof t,P=(t,e)=>S(t)?t(e):t,E=(i=0,()=>(++i).toString()),k=()=>{if(void 0===n&&"u">typeof window){let t=matchMedia("(prefers-reduced-motion: reduce)");n=!t||t.matches}return n},A=(t,e)=>{switch(e.type){case 0:return{...t,toasts:[e.toast,...t.toasts].slice(0,20)};case 1:return{...t,toasts:t.toasts.map(t=>t.id===e.toast.id?{...t,...e.toast}:t)};case 2:let{toast:r}=e;return A(t,{type:t.toasts.find(t=>t.id===r.id)?1:0,toast:r});case 3:let{toastId:i}=e;return{...t,toasts:t.toasts.map(t=>t.id===i||void 0===i?{...t,dismissed:!0,visible:!1}:t)};case 4:return void 0===e.toastId?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(t=>t.id!==e.toastId)};case 5:return{...t,pausedAt:e.time};case 6:let n=e.time-(t.pausedAt||0);return{...t,pausedAt:void 0,toasts:t.toasts.map(t=>({...t,pauseDuration:t.pauseDuration+n}))}}},M=[],C={toasts:[],pausedAt:void 0},R=t=>{C=A(C,t),M.forEach(t=>{t(C)})},D={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},V=(t={})=>{let[e,r]=(0,s.useState)(C),i=(0,s.useRef)(C);(0,s.useEffect)(()=>(i.current!==C&&r(C),M.push(r),()=>{let t=M.indexOf(r);t>-1&&M.splice(t,1)}),[]);let n=e.toasts.map(e=>{var r,i,n;return{...t,...t[e.type],...e,removeDelay:e.removeDelay||(null==(r=t[e.type])?void 0:r.removeDelay)||(null==t?void 0:t.removeDelay),duration:e.duration||(null==(i=t[e.type])?void 0:i.duration)||(null==t?void 0:t.duration)||D[e.type],style:{...t.style,...null==(n=t[e.type])?void 0:n.style,...e.style}}});return{...e,toasts:n}},j=(t,e="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:e,ariaProps:{role:"status","aria-live":"polite"},message:t,pauseDuration:0,...r,id:(null==r?void 0:r.id)||E()}),L=t=>(e,r)=>{let i=j(e,t,r);return R({type:2,toast:i}),i.id},O=(t,e)=>L("blank")(t,e);O.error=L("error"),O.success=L("success"),O.loading=L("loading"),O.custom=L("custom"),O.dismiss=t=>{R({type:3,toastId:t})},O.remove=t=>R({type:4,toastId:t}),O.promise=(t,e,r)=>{let i=O.loading(e.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof t&&(t=t()),t.then(t=>{let n=e.success?P(e.success,t):void 0;return n?O.success(n,{id:i,...r,...null==r?void 0:r.success}):O.dismiss(i),t}).catch(t=>{let n=e.error?P(e.error,t):void 0;n?O.error(n,{id:i,...r,...null==r?void 0:r.error}):O.dismiss(i)}),t};var F=(t,e)=>{R({type:1,toast:{id:t,height:e}})},N=()=>{R({type:5,time:Date.now()})},I=new Map,B=1e3,z=(t,e=B)=>{if(I.has(t))return;let r=setTimeout(()=>{I.delete(t),R({type:4,toastId:t})},e);I.set(t,r)},$=t=>{let{toasts:e,pausedAt:r}=V(t);(0,s.useEffect)(()=>{if(r)return;let t=Date.now(),i=e.map(e=>{if(e.duration===1/0)return;let r=(e.duration||0)+e.pauseDuration-(t-e.createdAt);if(r<0){e.visible&&O.dismiss(e.id);return}return setTimeout(()=>O.dismiss(e.id),r)});return()=>{i.forEach(t=>t&&clearTimeout(t))}},[e,r]);let i=(0,s.useCallback)(()=>{r&&R({type:6,time:Date.now()})},[r]),n=(0,s.useCallback)((t,r)=>{let{reverseOrder:i=!1,gutter:n=8,defaultPosition:o}=r||{},s=e.filter(e=>(e.position||o)===(t.position||o)&&e.height),a=s.findIndex(e=>e.id===t.id),l=s.filter((t,e)=>e<a&&t.visible).length;return s.filter(t=>t.visible).slice(...i?[l+1]:[0,l]).reduce((t,e)=>t+(e.height||0)+n,0)},[e]);return(0,s.useEffect)(()=>{e.forEach(t=>{if(t.dismissed)z(t.id,t.removeDelay);else{let e=I.get(t.id);e&&(clearTimeout(e),I.delete(t.id))}})},[e]),{toasts:e,handlers:{updateHeight:F,startPause:N,endPause:i,calculateOffset:n}}},U=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,W=w`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,_=w`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,H=T("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${U} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${W} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${t=>t.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${_} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Y=w`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,X=T("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${t=>t.secondary||"#e0e0e0"};
  border-right-color: ${t=>t.primary||"#616161"};
  animation: ${Y} 1s linear infinite;
`,Z=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,q=w`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,K=T("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Z} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${q} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${t=>t.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,G=T("div")`
  position: absolute;
`,J=T("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Q=w`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,tt=T("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Q} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,te=({toast:t})=>{let{icon:e,type:r,iconTheme:i}=t;return void 0!==e?"string"==typeof e?s.createElement(tt,null,e):e:"blank"===r?null:s.createElement(J,null,s.createElement(X,{...i}),"loading"!==r&&s.createElement(G,null,"error"===r?s.createElement(H,{...i}):s.createElement(K,{...i})))},tr=t=>`
0% {transform: translate3d(0,${-200*t}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,ti=t=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*t}%,-1px) scale(.6); opacity:0;}
`,tn=T("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,to=T("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ts=(t,e)=>{let r=t.includes("top")?1:-1,[i,n]=k()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[tr(r),ti(r)];return{animation:e?`${w(i)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${w(n)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},ta=s.memo(({toast:t,position:e,style:r,children:i})=>{let n=t.height?ts(t.position||e||"top-center",t.visible):{opacity:0},o=s.createElement(te,{toast:t}),a=s.createElement(to,{...t.ariaProps},P(t.message,t));return s.createElement(tn,{className:t.className,style:{...n,...r,...t.style}},"function"==typeof i?i({icon:o,message:a}):s.createElement(s.Fragment,null,o,a))});o=s.createElement,h.p=void 0,v=o,b=void 0,x=void 0;var tl=({id:t,className:e,style:r,onHeightUpdate:i,children:n})=>{let o=s.useCallback(e=>{if(e){let r=()=>{i(t,e.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(e,{subtree:!0,childList:!0,characterData:!0})}},[t,i]);return s.createElement("div",{ref:o,className:e,style:r},n)},tu=(t,e)=>{let r=t.includes("top"),i=t.includes("center")?{justifyContent:"center"}:t.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:k()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${e*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...i}},td=y`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,tc=({reverseOrder:t,position:e="top-center",toastOptions:r,gutter:i,children:n,containerStyle:o,containerClassName:a})=>{let{toasts:l,handlers:u}=$(r);return s.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...o},className:a,onMouseEnter:u.startPause,onMouseLeave:u.endPause},l.map(r=>{let o=r.position||e,a=tu(o,u.calculateOffset(r,{reverseOrder:t,gutter:i,defaultPosition:e}));return s.createElement(tl,{id:r.id,key:r.id,onHeightUpdate:u.updateHeight,className:r.visible?td:"",style:a},"custom"===r.type?P(r.message,r):n?n(r):s.createElement(ta,{toast:r,position:o}))}))},th=O},4769:function(t,e,r){r.d(e,{m6:function(){return q}});let i=t=>{let e=a(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:i}=t;return{getClassGroupId:t=>{let r=t.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,e)||s(t)},getConflictingClassGroupIds:(t,e)=>{let n=r[t]||[];return e&&i[t]?[...n,...i[t]]:n}}},n=(t,e)=>{if(0===t.length)return e.classGroupId;let r=t[0],i=e.nextPart.get(r),o=i?n(t.slice(1),i):void 0;if(o)return o;if(0===e.validators.length)return;let s=t.join("-");return e.validators.find(({validator:t})=>t(s))?.classGroupId},o=/^\[(.+)\]$/,s=t=>{if(o.test(t)){let e=o.exec(t)[1],r=e?.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},a=t=>{let{theme:e,prefix:r}=t,i={nextPart:new Map,validators:[]};return c(Object.entries(t.classGroups),r).forEach(([t,r])=>{l(r,i,t,e)}),i},l=(t,e,r,i)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=r;return}if("function"==typeof t){if(d(t)){l(t(i),e,r,i);return}e.validators.push({validator:t,classGroupId:r});return}Object.entries(t).forEach(([t,n])=>{l(n,u(e,t),r,i)})})},u=(t,e)=>{let r=t;return e.split("-").forEach(t=>{r.nextPart.has(t)||r.nextPart.set(t,{nextPart:new Map,validators:[]}),r=r.nextPart.get(t)}),r},d=t=>t.isThemeGetter,c=(t,e)=>e?t.map(([t,r])=>[t,r.map(t=>"string"==typeof t?e+t:"object"==typeof t?Object.fromEntries(Object.entries(t).map(([t,r])=>[e+t,r])):t)]):t,h=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,r=new Map,i=new Map,n=(n,o)=>{r.set(n,o),++e>t&&(e=0,i=r,r=new Map)};return{get(t){let e=r.get(t);return void 0!==e?e:void 0!==(e=i.get(t))?(n(t,e),e):void 0},set(t,e){r.has(t)?r.set(t,e):n(t,e)}}},p=t=>{let{separator:e,experimentalParseClassName:r}=t,i=1===e.length,n=e[0],o=e.length,s=t=>{let r;let s=[],a=0,l=0;for(let u=0;u<t.length;u++){let d=t[u];if(0===a){if(d===n&&(i||t.slice(u,u+o)===e)){s.push(t.slice(l,u)),l=u+o;continue}if("/"===d){r=u;continue}}"["===d?a++:"]"===d&&a--}let u=0===s.length?t:t.substring(l),d=u.startsWith("!"),c=d?u.substring(1):u;return{modifiers:s,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};return r?t=>r({className:t,parseClassName:s}):s},m=t=>{if(t.length<=1)return t;let e=[],r=[];return t.forEach(t=>{"["===t[0]?(e.push(...r.sort(),t),r=[]):r.push(t)}),e.push(...r.sort()),e},f=t=>({cache:h(t.cacheSize),parseClassName:p(t),...i(t)}),g=/\s+/,y=(t,e)=>{let{parseClassName:r,getClassGroupId:i,getConflictingClassGroupIds:n}=e,o=[],s=t.trim().split(g),a="";for(let t=s.length-1;t>=0;t-=1){let e=s[t],{modifiers:l,hasImportantModifier:u,baseClassName:d,maybePostfixModifierPosition:c}=r(e),h=!!c,p=i(h?d.substring(0,c):d);if(!p){if(!h||!(p=i(d))){a=e+(a.length>0?" "+a:a);continue}h=!1}let f=m(l).join(":"),g=u?f+"!":f,y=g+p;if(o.includes(y))continue;o.push(y);let v=n(p,h);for(let t=0;t<v.length;++t){let e=v[t];o.push(g+e)}a=e+(a.length>0?" "+a:a)}return a};function v(){let t,e,r=0,i="";for(;r<arguments.length;)(t=arguments[r++])&&(e=b(t))&&(i&&(i+=" "),i+=e);return i}let b=t=>{let e;if("string"==typeof t)return t;let r="";for(let i=0;i<t.length;i++)t[i]&&(e=b(t[i]))&&(r&&(r+=" "),r+=e);return r},x=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},w=/^\[(?:([a-z-]+):)?(.+)\]$/i,T=/^\d+\/\d+$/,S=new Set(["px","full","screen"]),P=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,E=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,k=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,A=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,M=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,C=t=>D(t)||S.has(t)||T.test(t),R=t=>_(t,"length",H),D=t=>!!t&&!Number.isNaN(Number(t)),V=t=>_(t,"number",D),j=t=>!!t&&Number.isInteger(Number(t)),L=t=>t.endsWith("%")&&D(t.slice(0,-1)),O=t=>w.test(t),F=t=>P.test(t),N=new Set(["length","size","percentage"]),I=t=>_(t,N,Y),B=t=>_(t,"position",Y),z=new Set(["image","url"]),$=t=>_(t,z,Z),U=t=>_(t,"",X),W=()=>!0,_=(t,e,r)=>{let i=w.exec(t);return!!i&&(i[1]?"string"==typeof e?i[1]===e:e.has(i[1]):r(i[2]))},H=t=>E.test(t)&&!k.test(t),Y=()=>!1,X=t=>A.test(t),Z=t=>M.test(t),q=function(t){let e,r,i;let n=function(s){return r=(e=f([].reduce((t,e)=>e(t),t()))).cache.get,i=e.cache.set,n=o,o(s)};function o(t){let n=r(t);if(n)return n;let o=y(t,e);return i(t,o),o}return function(){return n(v.apply(null,arguments))}}(()=>{let t=x("colors"),e=x("spacing"),r=x("blur"),i=x("brightness"),n=x("borderColor"),o=x("borderRadius"),s=x("borderSpacing"),a=x("borderWidth"),l=x("contrast"),u=x("grayscale"),d=x("hueRotate"),c=x("invert"),h=x("gap"),p=x("gradientColorStops"),m=x("gradientColorStopPositions"),f=x("inset"),g=x("margin"),y=x("opacity"),v=x("padding"),b=x("saturate"),w=x("scale"),T=x("sepia"),S=x("skew"),P=x("space"),E=x("translate"),k=()=>["auto","contain","none"],A=()=>["auto","hidden","clip","visible","scroll"],M=()=>["auto",O,e],N=()=>[O,e],z=()=>["",C,R],_=()=>["auto",D,O],H=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Y=()=>["solid","dashed","dotted","double","none"],X=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Z=()=>["start","end","center","between","around","evenly","stretch"],q=()=>["","0",O],K=()=>["auto","avoid","all","avoid-page","page","left","right","column"],G=()=>[D,O];return{cacheSize:500,separator:":",theme:{colors:[W],spacing:[C,R],blur:["none","",F,O],brightness:G(),borderColor:[t],borderRadius:["none","","full",F,O],borderSpacing:N(),borderWidth:z(),contrast:G(),grayscale:q(),hueRotate:G(),invert:q(),gap:N(),gradientColorStops:[t],gradientColorStopPositions:[L,R],inset:M(),margin:M(),opacity:G(),padding:N(),saturate:G(),scale:G(),sepia:q(),skew:G(),space:N(),translate:N()},classGroups:{aspect:[{aspect:["auto","square","video",O]}],container:["container"],columns:[{columns:[F]}],"break-after":[{"break-after":K()}],"break-before":[{"break-before":K()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...H(),O]}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",j,O]}],basis:[{basis:M()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",O]}],grow:[{grow:q()}],shrink:[{shrink:q()}],order:[{order:["first","last","none",j,O]}],"grid-cols":[{"grid-cols":[W]}],"col-start-end":[{col:["auto",{span:["full",j,O]},O]}],"col-start":[{"col-start":_()}],"col-end":[{"col-end":_()}],"grid-rows":[{"grid-rows":[W]}],"row-start-end":[{row:["auto",{span:[j,O]},O]}],"row-start":[{"row-start":_()}],"row-end":[{"row-end":_()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",O]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",O]}],gap:[{gap:[h]}],"gap-x":[{"gap-x":[h]}],"gap-y":[{"gap-y":[h]}],"justify-content":[{justify:["normal",...Z()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...Z(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...Z(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[P]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[P]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",O,e]}],"min-w":[{"min-w":[O,e,"min","max","fit"]}],"max-w":[{"max-w":[O,e,"none","full","min","max","fit","prose",{screen:[F]},F]}],h:[{h:[O,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[O,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[O,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[O,e,"auto","min","max","fit"]}],"font-size":[{text:["base",F,R]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",V]}],"font-family":[{font:[W]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",O]}],"line-clamp":[{"line-clamp":["none",D,V]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",C,O]}],"list-image":[{"list-image":["none",O]}],"list-style-type":[{list:["none","disc","decimal",O]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Y(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",C,R]}],"underline-offset":[{"underline-offset":["auto",C,O]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",O]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",O]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...H(),B]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",I]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},$]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[...Y(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:Y()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...Y()]}],"outline-offset":[{"outline-offset":[C,O]}],"outline-w":[{outline:[C,R]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:z()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[C,R]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",F,U]}],"shadow-color":[{shadow:[W]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":[...X(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":X()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[i]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",F,O]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[c]}],saturate:[{saturate:[b]}],sepia:[{sepia:[T]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[i]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[T]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",O]}],duration:[{duration:G()}],ease:[{ease:["linear","in","out","in-out",O]}],delay:[{delay:G()}],animate:[{animate:["none","spin","ping","pulse","bounce",O]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[j,O]}],"translate-x":[{"translate-x":[E]}],"translate-y":[{"translate-y":[E]}],"skew-x":[{"skew-x":[S]}],"skew-y":[{"skew-y":[S]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",O]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",O]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",O]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[C,R,V]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})}}]);