# TainoAI v1.0.11 - Custom AI Engine Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat python3 make g++
WORKDIR /app

# Install dependencies
COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build Next.js app and TypeScript AI engine
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Install runtime dependencies
RUN apk add --no-cache python3 py3-pip sqlite

# Create directories for AI engine
RUN mkdir -p ./models ./data ./documents ./cache ./temp
RUN chown -R nextjs:nodejs ./models ./data ./documents ./cache ./temp

# Install Python dependencies
COPY requirements.txt .
RUN pip3 install -r requirements.txt

# Copy Python application files
COPY *.py ./
COPY Procfile ./

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/src ./src
COPY --from=builder /app/node_modules ./node_modules

# Copy package.json for scripts
COPY --from=builder /app/package.json ./package.json

USER nextjs

EXPOSE 5000 8001

ENV PORT 5000
ENV HOSTNAME "0.0.0.0"

# Start both frontend and AI engine
CMD ["npm", "start"]
