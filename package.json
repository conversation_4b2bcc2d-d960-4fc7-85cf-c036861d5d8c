{"name": "tainoai", "version": "1.0.11", "description": "TainoAI - World-Class AI Assistant with LLaMA 2 + Mistral 7B + Claude", "private": true, "scripts": {"dev": "concurrently \"npm run python-server\" \"npm run ai-engine\" \"next dev -p 5000\"", "ai-engine": "nodemon --exec ts-node src/ai-engine/server.ts", "ai-engine:prod": "ts-node src/ai-engine/server.ts", "build": "next build && next export", "export": "next export", "start": "next start -p 5000", "lint": "next lint", "setup-models": "ts-node src/ai-engine/setup-models.ts", "python-server": "python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload", "python-server:prod": "gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app", "heroku-postbuild": "npm run build"}, "dependencies": {"@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^12.15.0", "lucide-react": "^0.294.0", "next": "14.0.4", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.2", "tailwind-merge": "^2.0.0", "better-sqlite3": "^9.2.2", "cheerio": "^1.0.0-rc.12", "puppeteer": "^21.6.1", "node-fetch": "^3.3.2", "ws": "^8.14.2", "concurrently": "^8.2.2", "axios": "^1.6.2", "express": "^4.18.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/ws": "^8.5.10", "@types/better-sqlite3": "^7.6.8", "@types/cheerio": "^0.22.35", "@types/express": "^4.17.21", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5", "ts-node": "^10.9.1", "nodemon": "^3.0.2"}, "keywords": ["ai", "chatgpt", "<PERSON><PERSON><PERSON>", "llama", "nextjs", "chatbot", "<PERSON><PERSON>"], "author": "TainoAI", "license": "MIT"}