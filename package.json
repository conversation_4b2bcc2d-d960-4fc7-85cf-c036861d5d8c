{"name": "tainoai", "version": "1.0.0", "description": "TainoAI - World-Class AI Assistant with LLaMA 2 + Mistral 7B", "private": true, "scripts": {"dev": "next dev -p 5000", "build": "next build", "export": "next export", "start": "next start -p 5000", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^12.15.0", "lucide-react": "^0.294.0", "next": "14.0.4", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.2", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}, "keywords": ["ai", "chatgpt", "<PERSON><PERSON><PERSON>", "llama", "nextjs", "chatbot", "<PERSON><PERSON>"], "author": "TainoAI", "license": "MIT"}