{"conversation_id": "default", "user_id": "user", "message": "<PERSON> <PERSON>", "response": "Hello! I'm TainoAI. I'm currently running in lightweight mode. How can I help you today?", "processing_time": 0.005001, "ai_model_info": {"model_name": "Mistral-7B-Instruct-v0.1", "quantization": "Q4_K_M", "model_loaded": false, "model_path": "./models/mistral-7b-instruct-v0.1.Q4_K_M.gguf", "max_tokens": 512, "temperature": 0.8, "threads": 2, "context_size": 2048, "version": "7.0.0"}, "timestamp": "2025-06-02T16:37:02.331944"}
{"conversation_id": "default", "user_id": "user", "message": "give me info about racecars?", "response": "I'm TainoAI running in lightweight mode. I received your message: 'give me info about racecars?'. While I don't have my full AI model loaded right now, I'm still here to help! What specific information are you looking for?", "processing_time": 0.001389, "ai_model_info": {"model_name": "Mistral-7B-Instruct-v0.1", "quantization": "Q4_K_M", "model_loaded": false, "model_path": "./models/mistral-7b-instruct-v0.1.Q4_K_M.gguf", "max_tokens": 512, "temperature": 0.8, "threads": 2, "context_size": 2048, "version": "7.0.0"}, "timestamp": "2025-06-02T16:37:12.120995"}
