#!/usr/bin/env python3
"""
Local Development Startup Script for GameDev AI Assistant
Simplified startup for testing without <PERSON><PERSON>
"""

import os
import sys
import asyncio
import logging
import json
from pathlib import Path
from typing import Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LocalGameDevAI:
    """Simplified local version for testing"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.config = self.load_config()
        self.setup_environment()
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration"""
        config_file = self.project_root / "gamedev-ai-config.json"
        if config_file.exists():
            with open(config_file, 'r') as f:
                return json.load(f)
        else:
            # Default minimal config
            return {
                "project": {
                    "name": "GameDev AI Assistant",
                    "version": "2.0.0"
                },
                "expert_modes": {
                    "game_dev_advisor": {
                        "model": "local",
                        "system_prompt": "You are an expert game developer assistant.",
                        "tools": ["code_generator"]
                    }
                },
                "memory_system": {
                    "embedding_model": "sentence-transformers/all-MiniLM-L6-v2",
                    "collections": {
                        "conversations": "User conversations"
                    }
                }
            }
    
    def setup_environment(self):
        """Setup environment variables"""
        os.environ["PYTHONPATH"] = str(self.project_root / "src")
        
        # Create required directories
        for dir_name in ["data", "logs", "models", "training"]:
            (self.project_root / dir_name).mkdir(exist_ok=True)
    
    def check_dependencies(self) -> bool:
        """Check if required dependencies are available"""
        logger.info("🔍 Checking dependencies...")
        
        required_packages = [
            "fastapi", "uvicorn", "pydantic", "sqlite3", 
            "json", "pathlib", "datetime", "typing"
        ]
        
        missing = []
        for package in required_packages:
            try:
                if package == "sqlite3":
                    import sqlite3
                elif package == "fastapi":
                    import fastapi
                elif package == "uvicorn":
                    import uvicorn
                elif package == "pydantic":
                    import pydantic
                else:
                    __import__(package)
                logger.info(f"✅ {package}")
            except ImportError:
                missing.append(package)
                logger.error(f"❌ {package}")
        
        if missing:
            logger.error(f"Missing packages: {missing}")
            logger.info("Install with: pip install fastapi uvicorn pydantic")
            return False
        
        return True
    
    def start_minimal_server(self):
        """Start minimal FastAPI server for testing"""
        logger.info("🚀 Starting minimal GameDev AI server...")
        
        try:
            # Add src to Python path
            sys.path.insert(0, str(self.project_root / "src"))
            
            from fastapi import FastAPI, HTTPException
            from fastapi.middleware.cors import CORSMiddleware
            from pydantic import BaseModel
            import uvicorn
            
            # Create FastAPI app
            app = FastAPI(
                title="GameDev AI Assistant (Local)",
                description="Local development version",
                version=self.config["project"]["version"]
            )
            
            # Add CORS
            app.add_middleware(
                CORSMiddleware,
                allow_origins=["*"],
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
            
            # Simple models
            class ChatMessage(BaseModel):
                content: str
                expert_mode: str = "game_dev_advisor"
            
            class ChatResponse(BaseModel):
                content: str
                expert_mode: str
                confidence: float = 0.8
            
            # Health check endpoint
            @app.get("/health")
            async def health_check():
                return {
                    "status": "healthy",
                    "version": self.config["project"]["version"],
                    "mode": "local_development"
                }
            
            # Simple chat endpoint
            @app.post("/api/chat", response_model=ChatResponse)
            async def chat_endpoint(message: ChatMessage):
                """Simple chat endpoint for testing"""
                
                # Simple response generation based on expert mode
                if message.expert_mode == "game_dev_advisor":
                    if "unity" in message.content.lower():
                        response = "For Unity development, I recommend using C# scripts. Here's a basic player controller example:\n\n```csharp\nusing UnityEngine;\n\npublic class PlayerController : MonoBehaviour\n{\n    public float speed = 5.0f;\n    \n    void Update()\n    {\n        float horizontal = Input.GetAxis(\"Horizontal\");\n        float vertical = Input.GetAxis(\"Vertical\");\n        \n        Vector3 movement = new Vector3(horizontal, 0, vertical) * speed * Time.deltaTime;\n        transform.Translate(movement);\n    }\n}\n```"
                    elif "unreal" in message.content.lower():
                        response = "For Unreal Engine development, you can use C++ or Blueprints. Here's a basic C++ approach for player movement."
                    elif "godot" in message.content.lower():
                        response = "For Godot development, you can use GDScript. Here's a simple player movement script."
                    else:
                        response = f"As a game development advisor, I can help you with Unity, Unreal, Godot, and general game development questions. You asked: '{message.content}'"
                
                elif message.expert_mode == "coding_companion":
                    response = f"As your coding companion, I can help you write, review, and optimize code. Regarding '{message.content}', let me provide some technical guidance."
                
                elif message.expert_mode == "marketing_strategist":
                    response = f"From a marketing perspective, regarding '{message.content}', I recommend focusing on your target audience and unique value proposition."
                
                elif message.expert_mode == "business_mentor":
                    response = f"As a business mentor, for '{message.content}', I suggest considering the market opportunity, resource requirements, and potential ROI."
                
                else:
                    response = f"I'm here to help with game development, coding, marketing, and business questions. You asked: '{message.content}'"
                
                return ChatResponse(
                    content=response,
                    expert_mode=message.expert_mode,
                    confidence=0.8
                )
            
            # Expert modes endpoint
            @app.get("/api/expert-modes")
            async def get_expert_modes():
                return {
                    "expert_modes": list(self.config["expert_modes"].keys()),
                    "current_mode": "game_dev_advisor"
                }
            
            # Models endpoint
            @app.get("/api/models/status")
            async def get_models_status():
                return {
                    "models": {
                        "local": {
                            "name": "Local Development Model",
                            "status": "ready",
                            "type": "mock"
                        }
                    },
                    "active_model": "local"
                }
            
            # Start server
            logger.info("✅ Minimal server configured")
            logger.info("🌐 Starting server on http://localhost:8000")
            logger.info("📚 API docs available at http://localhost:8000/docs")
            logger.info("🎮 Test the chat endpoint at http://localhost:8000/docs#/default/chat_endpoint_api_chat_post")
            
            # Run server
            import uvicorn
            uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
            
        except Exception as e:
            logger.error(f"❌ Failed to start server: {e}")
            raise
    
    def print_usage_examples(self):
        """Print usage examples"""
        logger.info("\n📋 Usage Examples:")
        logger.info("=" * 50)
        
        examples = [
            {
                "title": "Game Dev Advisor",
                "request": {
                    "content": "How do I implement player movement in Unity?",
                    "expert_mode": "game_dev_advisor"
                }
            },
            {
                "title": "Coding Companion", 
                "request": {
                    "content": "Review this C# code for performance issues",
                    "expert_mode": "coding_companion"
                }
            },
            {
                "title": "Marketing Strategist",
                "request": {
                    "content": "What's the best way to market an indie game?",
                    "expert_mode": "marketing_strategist"
                }
            }
        ]
        
        for example in examples:
            logger.info(f"\n🎯 {example['title']}:")
            logger.info(f"POST /api/chat")
            logger.info(f"Content-Type: application/json")
            logger.info(f"{json.dumps(example['request'], indent=2)}")

def main():
    """Main function"""
    logger.info("🎮 GameDev AI Assistant - Local Development Mode")
    logger.info("=" * 60)

    try:
        # Initialize local AI
        local_ai = LocalGameDevAI()

        # Check dependencies
        if not local_ai.check_dependencies():
            logger.error("❌ Missing dependencies. Please install required packages.")
            return

        # Print usage examples
        local_ai.print_usage_examples()

        # Start server
        local_ai.start_minimal_server()

    except KeyboardInterrupt:
        logger.info("\n⏹️ Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Startup failed: {e}")

if __name__ == "__main__":
    main()
