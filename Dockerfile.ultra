# TainoAI - Ultra-Lightweight Docker for Heroku (<450MB)
# Optimized for minimal slug size

# Stage 1: Build React Frontend (minimal)
FROM node:18-alpine AS frontend-builder

WORKDIR /app

# Copy only essential package files
COPY package.json package-lock.json ./

# Install only production dependencies
RUN npm ci --only=production --no-audit --no-fund --no-optional

# Copy minimal source code
COPY app/ ./app/
COPY components/ ./components/
COPY lib/ ./lib/
COPY *.config.js ./
COPY *.json ./

# Build and export static frontend
RUN npm run build && npm run export

# Stage 2: Ultra-Lightweight Python Runtime
FROM python:3.11-slim AS production

# Install only essential system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && apt-get autoremove -y

WORKDIR /app

# Copy requirements and install minimal Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt \
    && pip cache purge

# Copy built frontend (static files only)
COPY --from=frontend-builder /app/out/ ./static/

# Copy only essential backend files
COPY main.py ./
COPY document_processor.py ./

# Create minimal data directory
RUN mkdir -p data

# Remove unnecessary files and caches
RUN find /usr/local/lib/python3.11/site-packages -name "*.pyc" -delete \
    && find /usr/local/lib/python3.11/site-packages -name "__pycache__" -type d -exec rm -rf {} + \
    && find /usr/local/lib/python3.11/site-packages -name "*.pyo" -delete

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PORT=8000

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the application
CMD ["python", "-u", "main.py"]
