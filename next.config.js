/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  },

  env: {
    NEXT_PUBLIC_API_URL: process.env.NODE_ENV === 'production' ? '' : 'http://localhost:8000',
    NEXT_PUBLIC_APP_NAME: 'TainoAI',
    NEXT_PUBLIC_VERSION: '6.0.0-docker',
  },
  // Remove rewrites for static export
  // async rewrites() {
  //   return [
  //     {
  //       source: '/api/:path*',
  //       destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/:path*`,
  //     },
  //   ]
  // },
}

module.exports = nextConfig
