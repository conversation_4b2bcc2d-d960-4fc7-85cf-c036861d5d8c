/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  images: {
    unoptimized: true
  },

  env: {
    NEXT_PUBLIC_API_URL: process.env.NODE_ENV === 'production' ? 'https://tainoai-f3e79bc1885e.herokuapp.com' : 'http://localhost:8000',
    NEXT_PUBLIC_APP_NAME: 'TainoAI',
    NEXT_PUBLIC_VERSION: '6.0.0-docker',
    NEXT_PUBLIC_WS_URL: process.env.NODE_ENV === 'production'
      ? 'wss://tainoai-f3e79bc1885e.herokuapp.com'
      : 'ws://localhost:8001'
  }
}

module.exports = nextConfig
