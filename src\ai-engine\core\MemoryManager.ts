/**
 * TainoAI Memory Manager - Conversation and Context Management
 */

import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import { AIMessage, Conversation, AIMemory } from '../types';
import path from 'path';
import fs from 'fs';

export class MemoryManager {
  private db: sqlite3.Database;
  private dbPath: string;
  private isInitialized = false;

  constructor(dbPath: string = './data/tainoai.db') {
    this.dbPath = dbPath;
    this.ensureDirectoryExists();
  }

  private ensureDirectoryExists(): void {
    const dir = path.dirname(this.dbPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  }

  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          reject(err);
          return;
        }

        this.createTables()
          .then(() => {
            this.isInitialized = true;
            console.log('✅ Memory Manager initialized');
            resolve();
          })
          .catch(reject);
      });
    });
  }

  private async createTables(): Promise<void> {
    const run = promisify(this.db.run.bind(this.db));

    // Conversations table
    await run(`
      CREATE TABLE IF NOT EXISTS conversations (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        title TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        metadata TEXT
      )
    `);

    // Messages table
    await run(`
      CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        conversation_id TEXT NOT NULL,
        content TEXT NOT NULL,
        role TEXT NOT NULL,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        metadata TEXT,
        FOREIGN KEY (conversation_id) REFERENCES conversations (id)
      )
    `);

    // Knowledge base table
    await run(`
      CREATE TABLE IF NOT EXISTS knowledge_chunks (
        id TEXT PRIMARY KEY,
        content TEXT NOT NULL,
        source TEXT NOT NULL,
        embedding BLOB,
        metadata TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // User preferences table
    await run(`
      CREATE TABLE IF NOT EXISTS user_preferences (
        user_id TEXT PRIMARY KEY,
        preferences TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('📊 Database tables created/verified');
  }

  async createConversation(id: string, userId: string, title?: string): Promise<Conversation> {
    if (!this.isInitialized) {
      throw new Error('Memory Manager not initialized');
    }

    const run = promisify(this.db.run.bind(this.db));
    
    const conversation: Conversation = {
      id,
      userId,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: { title },
    };

    await run(
      'INSERT INTO conversations (id, user_id, title, metadata) VALUES (?, ?, ?, ?)',
      [id, userId, title || null, JSON.stringify(conversation.metadata)]
    );

    return conversation;
  }

  async getConversation(id: string): Promise<Conversation | null> {
    if (!this.isInitialized) {
      throw new Error('Memory Manager not initialized');
    }

    const get = promisify(this.db.get.bind(this.db));
    const all = promisify(this.db.all.bind(this.db));

    // Get conversation
    const convRow: any = await get(
      'SELECT * FROM conversations WHERE id = ?',
      [id]
    );

    if (!convRow) {
      return null;
    }

    // Get messages
    const messageRows: any[] = await all(
      'SELECT * FROM messages WHERE conversation_id = ? ORDER BY timestamp ASC',
      [id]
    );

    const messages: AIMessage[] = messageRows.map(row => ({
      id: row.id,
      content: row.content,
      role: row.role,
      timestamp: new Date(row.timestamp),
      metadata: row.metadata ? JSON.parse(row.metadata) : undefined,
    }));

    return {
      id: convRow.id,
      userId: convRow.user_id,
      messages,
      createdAt: new Date(convRow.created_at),
      updatedAt: new Date(convRow.updated_at),
      metadata: convRow.metadata ? JSON.parse(convRow.metadata) : undefined,
    };
  }

  async addMessage(conversationId: string, message: AIMessage): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Memory Manager not initialized');
    }

    const run = promisify(this.db.run.bind(this.db));

    // Insert message
    await run(
      'INSERT INTO messages (id, conversation_id, content, role, timestamp, metadata) VALUES (?, ?, ?, ?, ?, ?)',
      [
        message.id,
        conversationId,
        message.content,
        message.role,
        message.timestamp.toISOString(),
        message.metadata ? JSON.stringify(message.metadata) : null,
      ]
    );

    // Update conversation timestamp
    await run(
      'UPDATE conversations SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [conversationId]
    );
  }

  async getRecentConversations(userId: string, limit: number = 10): Promise<Conversation[]> {
    if (!this.isInitialized) {
      throw new Error('Memory Manager not initialized');
    }

    const all = promisify(this.db.all.bind(this.db));

    const rows: any[] = await all(
      'SELECT * FROM conversations WHERE user_id = ? ORDER BY updated_at DESC LIMIT ?',
      [userId, limit]
    );

    const conversations: Conversation[] = [];

    for (const row of rows) {
      const conversation = await this.getConversation(row.id);
      if (conversation) {
        conversations.push(conversation);
      }
    }

    return conversations;
  }

  async searchMessages(query: string, userId?: string, limit: number = 20): Promise<AIMessage[]> {
    if (!this.isInitialized) {
      throw new Error('Memory Manager not initialized');
    }

    const all = promisify(this.db.all.bind(this.db));

    let sql = `
      SELECT m.* FROM messages m
      JOIN conversations c ON m.conversation_id = c.id
      WHERE m.content LIKE ?
    `;
    const params: any[] = [`%${query}%`];

    if (userId) {
      sql += ' AND c.user_id = ?';
      params.push(userId);
    }

    sql += ' ORDER BY m.timestamp DESC LIMIT ?';
    params.push(limit);

    const rows: any[] = await all(sql, params);

    return rows.map(row => ({
      id: row.id,
      content: row.content,
      role: row.role,
      timestamp: new Date(row.timestamp),
      metadata: row.metadata ? JSON.parse(row.metadata) : undefined,
    }));
  }

  async storeKnowledgeChunk(
    id: string,
    content: string,
    source: string,
    embedding?: number[],
    metadata?: any
  ): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Memory Manager not initialized');
    }

    const run = promisify(this.db.run.bind(this.db));

    await run(
      'INSERT OR REPLACE INTO knowledge_chunks (id, content, source, embedding, metadata) VALUES (?, ?, ?, ?, ?)',
      [
        id,
        content,
        source,
        embedding ? Buffer.from(new Float32Array(embedding).buffer) : null,
        metadata ? JSON.stringify(metadata) : null,
      ]
    );
  }

  async getKnowledgeChunks(source?: string): Promise<any[]> {
    if (!this.isInitialized) {
      throw new Error('Memory Manager not initialized');
    }

    const all = promisify(this.db.all.bind(this.db));

    let sql = 'SELECT * FROM knowledge_chunks';
    const params: any[] = [];

    if (source) {
      sql += ' WHERE source = ?';
      params.push(source);
    }

    sql += ' ORDER BY created_at DESC';

    const rows: any[] = await all(sql, params);

    return rows.map(row => ({
      id: row.id,
      content: row.content,
      source: row.source,
      embedding: row.embedding ? Array.from(new Float32Array(row.embedding.buffer)) : null,
      metadata: row.metadata ? JSON.parse(row.metadata) : null,
      createdAt: new Date(row.created_at),
    }));
  }

  async deleteConversation(id: string): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Memory Manager not initialized');
    }

    const run = promisify(this.db.run.bind(this.db));

    // Delete messages first (foreign key constraint)
    await run('DELETE FROM messages WHERE conversation_id = ?', [id]);
    
    // Delete conversation
    await run('DELETE FROM conversations WHERE id = ?', [id]);
  }

  async getMemoryStats(): Promise<any> {
    if (!this.isInitialized) {
      throw new Error('Memory Manager not initialized');
    }

    const get = promisify(this.db.get.bind(this.db));

    const conversationCount: any = await get('SELECT COUNT(*) as count FROM conversations');
    const messageCount: any = await get('SELECT COUNT(*) as count FROM messages');
    const knowledgeCount: any = await get('SELECT COUNT(*) as count FROM knowledge_chunks');

    return {
      conversations: conversationCount.count,
      messages: messageCount.count,
      knowledgeChunks: knowledgeCount.count,
      databaseSize: fs.statSync(this.dbPath).size,
    };
  }

  async close(): Promise<void> {
    if (this.db) {
      return new Promise((resolve) => {
        this.db.close((err) => {
          if (err) {
            console.error('Error closing database:', err);
          }
          resolve();
        });
      });
    }
  }
}
