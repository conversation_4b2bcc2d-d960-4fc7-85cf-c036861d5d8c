'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Bot, User, Send, Sparkles, RotateCcw, Moon, Sun, Upload, FileText, Trash2, Zap, Brain, Globe, Code } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { TainoAIClient } from '@/src/ai-engine/client/TainoAIClient'

interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
}

interface EngineStatus {
  primary: string
  secondary: string
  claude: string
}

export default function TainoAI() {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const [isDark, setIsDark] = useState(true)
  const [showUpload, setShowUpload] = useState(false)
  const [documents, setDocuments] = useState<any[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [aiClient, setAiClient] = useState<TainoAIClient | null>(null)
  const [engineStatus, setEngineStatus] = useState<EngineStatus>({ 
    primary: 'initializing',
    secondary: 'initializing',
    claude: 'not configured'
  })
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Initialize TainoAI Engine connection
  useEffect(() => {
    const initializeAI = async () => {
      try {
        // Check API health first
        const healthCheck = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/health`)
        const health = await healthCheck.json()
        
        if (health.status === 'healthy') {
          toast.success('API connection established!')
          
          const client = new TainoAIClient({
            serverUrl: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8001',
            autoReconnect: true,
          })

          // Set up event handlers
          client.on('welcome', (data) => {
            console.log('🎉 TainoAI Engine connected:', data)
            setEngineStatus({
              primary: data.models?.primary || 'not available',
              secondary: data.models?.secondary || 'not available',
              claude: data.models?.claude || 'not configured'
            })
            setIsConnected(true)
            toast.success('TainoAI Engine connected!')
          })

          client.on('chat_response', (data) => {
            setIsTyping(false)
            setIsLoading(false)
            const assistantMessage: Message = {
              id: (Date.now() + 1).toString(),
              content: data.response,
              role: 'assistant',
              timestamp: new Date(),
            }
            setMessages(prev => [...prev, assistantMessage])
          })

          client.on('typing', (data) => {
            setIsTyping(data.isTyping)
          })

          client.on('error', (error) => {
            console.error('TainoAI Error:', error)
            toast.error(error.message || 'Connection error')
            setIsTyping(false)
            setIsLoading(false)
          })

          await client.connect()
          setAiClient(client)
        }
      } catch (error) {
        console.error('Failed to connect to TainoAI Engine:', error)
        toast.error('Failed to connect to AI Engine')
      }
    }

    initializeAI()
  }, [])

  const sendMessage = async () => {
    if (!input.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: input.trim(),
      role: 'user',
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsLoading(true)

    try {
      if (aiClient && isConnected) {
        // Use custom TainoAI Engine
        await aiClient.sendMessage(userMessage.content, {
          temperature: 0.8,
          maxTokens: 512,
          useTools: true,
          useKnowledge: true,
        })
      } else {
        // Fallback to original API
        const apiUrl = process.env.NODE_ENV === 'production' ? '/chat' : 'http://localhost:8003/chat'

        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: userMessage.content,
            conversation_id: 'default',
            user_id: 'user',
            temperature: 0.8,
            max_tokens: 512
          }),
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        setTimeout(() => {
          setIsTyping(false)
          const assistantMessage: Message = {
            id: (Date.now() + 1).toString(),
            content: data.response,
            role: 'assistant',
            timestamp: new Date(),
          }
          setMessages(prev => [...prev, assistantMessage])
        }, 1000)
      }

    } catch (error) {
      console.error('Error:', error)
      setIsTyping(false)
      toast.error('Failed to send message. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const resetConversation = () => {
    setMessages([])
    toast.success('Conversation reset!')
  }

  const toggleTheme = () => {
    setIsDark(!isDark)
    toast.success(`Switched to ${isDark ? 'light' : 'dark'} theme`)
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsUploading(true)

    try {
      // Read file content
      const content = await readFileContent(file)

      if (aiClient && isConnected) {
        // Use custom TainoAI Engine
        await aiClient.uploadDocument(content, file.name, {
          fileType: file.type,
          fileSize: file.size,
          uploadedAt: new Date().toISOString(),
        })
      } else {
        // Fallback to original API
        const formData = new FormData()
        formData.append('file', file)
        formData.append('user_id', 'default')

        const apiUrl = process.env.NODE_ENV === 'production' ? '/upload' : 'http://localhost:8003/upload'
        const response = await fetch(apiUrl, {
          method: 'POST',
          body: formData,
        })

        const result = await response.json()

        if (result.success) {
          toast.success(`Document uploaded successfully! ${result.message}`)
          loadDocuments()
        } else {
          toast.error(result.error || 'Upload failed')
        }
      }
    } catch (error) {
      console.error('Upload error:', error)
      toast.error('Failed to upload document')
    } finally {
      setIsUploading(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        resolve(content)
      }
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsText(file)
    })
  }

  const loadDocuments = async () => {
    try {
      const apiUrl = process.env.NODE_ENV === 'production' ? '/documents/default' : 'http://localhost:8003/documents/default'
      const response = await fetch(apiUrl)
      const data = await response.json()
      setDocuments(data.documents || [])
    } catch (error) {
      console.error('Error loading documents:', error)
    }
  }

  const deleteDocument = async (fileId: string) => {
    try {
      const apiUrl = process.env.NODE_ENV === 'production' ? `/documents/${fileId}?user_id=default` : `http://localhost:8003/documents/${fileId}?user_id=default`
      const response = await fetch(apiUrl, { method: 'DELETE' })
      const result = await response.json()

      if (result.success) {
        toast.success('Document deleted successfully')
        loadDocuments()
      } else {
        toast.error('Failed to delete document')
      }
    } catch (error) {
      console.error('Delete error:', error)
      toast.error('Failed to delete document')
    }
  }

  useEffect(() => {
    loadDocuments()
  }, [])

  return (
    <div className={`min-h-screen transition-all duration-500 ${
      isDark 
        ? 'bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900' 
        : 'bg-gradient-to-br from-purple-100 via-blue-100 to-indigo-100'
    }`}>
      <div className="container mx-auto max-w-4xl h-screen flex flex-col p-4">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`backdrop-blur-md rounded-2xl border p-6 mb-4 ${
            isDark 
              ? 'bg-white/10 border-white/20 text-white' 
              : 'bg-white/40 border-white/60 text-gray-800'
          }`}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Avatar className="w-12 h-12 border-2 border-purple-400">
                <AvatarFallback className="bg-gradient-to-r from-purple-600 to-blue-600">
                  <Bot className="w-6 h-6 text-white" />
                </AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                  TainoAI v1.0.11
                </h1>
                <div className="flex items-center space-x-2">
                  <p className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                    Multi-Model AI Engine
                  </p>
                  <div className="flex items-center space-x-1">
                    {isConnected ? (
                      <>
                        <Zap className="w-3 h-3 text-green-400" />
                        <span className="text-xs text-green-400">LIVE</span>
                        {engineStatus.claude === 'available' && (
                          <span className="ml-2 text-xs text-purple-400">+ Claude</span>
                        )}
                      </>
                    ) : (
                      <>
                        <span className="text-xs text-yellow-400">Initializing...</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowUpload(!showUpload)}
                className={`${isDark ? 'hover:bg-white/10' : 'hover:bg-black/10'}`}
              >
                <Upload className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleTheme}
                className={`${isDark ? 'hover:bg-white/10' : 'hover:bg-black/10'}`}
              >
                {isDark ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={resetConversation}
                className={`${isDark ? 'hover:bg-white/10' : 'hover:bg-black/10'}`}
              >
                <RotateCcw className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Upload Panel */}
        {showUpload && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className={`backdrop-blur-md rounded-2xl border p-6 mb-4 ${
              isDark
                ? 'bg-white/10 border-white/20 text-white'
                : 'bg-white/40 border-white/60 text-gray-800'
            }`}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">📚 Document Learning</h3>
              <span className="text-sm opacity-70">Upload documents to teach TainoAI</span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Upload Section */}
              <div>
                <div className={`border-2 border-dashed rounded-xl p-6 text-center ${
                  isDark ? 'border-white/30' : 'border-gray-300'
                }`}>
                  <Upload className="w-8 h-8 mx-auto mb-2 opacity-60" />
                  <p className="mb-4">
                    <strong>Supported formats:</strong><br />
                    PDF, Word, Excel, Text, CSV
                  </p>
                  <input
                    ref={fileInputRef}
                    type="file"
                    onChange={handleFileUpload}
                    accept=".pdf,.docx,.doc,.xlsx,.xls,.txt,.md,.csv"
                    className="hidden"
                  />
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
                  >
                    {isUploading ? 'Uploading...' : 'Choose File'}
                  </Button>
                </div>
              </div>

              {/* Documents List */}
              <div>
                <h4 className="font-medium mb-3">📄 Your Documents ({documents.length})</h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {documents.length === 0 ? (
                    <p className="text-sm opacity-60">No documents uploaded yet</p>
                  ) : (
                    documents.map((doc) => (
                      <div
                        key={doc.id}
                        className={`flex items-center justify-between p-3 rounded-lg ${
                          isDark ? 'bg-white/10' : 'bg-white/30'
                        }`}
                      >
                        <div className="flex items-center space-x-2">
                          <FileText className="w-4 h-4" />
                          <div>
                            <p className="text-sm font-medium">{doc.filename}</p>
                            <p className="text-xs opacity-60">{doc.word_count} words</p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteDocument(doc.id)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Messages Area */}
        <div className={`flex-1 backdrop-blur-md rounded-2xl border p-4 mb-4 overflow-hidden ${
          isDark 
            ? 'bg-white/5 border-white/10' 
            : 'bg-white/30 border-white/40'
        }`}>
          <ScrollArea className="h-full">
            <div className="space-y-4 pr-4">
              <AnimatePresence>
                {messages.length === 0 ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="text-center py-12"
                  >
                    <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-center">
                      <Sparkles className="w-10 h-10 text-white" />
                    </div>
                    <h2 className={`text-2xl font-bold mb-2 ${
                      isDark ? 'text-white' : 'text-gray-800'
                    }`}>
                      Welcome to TainoAI v1.0.11
                    </h2>
                    <p className={`mb-6 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                      {isConnected
                        ? `🚀 Multi-Model AI Engine with LLaMA/Mistral${engineStatus.claude === 'available' ? ' + Claude' : ''} + Tools`
                        : '⚡ Fully Independent AI System + Cloud Fallback'
                      }
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
                      {[
                        "Explain quantum computing",
                        "Write a Python function",
                        "Latest AI research trends",
                        "Create a business plan"
                      ].map((suggestion, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          className={`h-auto p-4 text-left backdrop-blur-sm ${
                            isDark 
                              ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white' 
                              : 'bg-white/30 border-white/40 hover:bg-white/50 text-gray-800'
                          }`}
                          onClick={() => setInput(suggestion)}
                        >
                          {suggestion}
                        </Button>
                      ))}
                    </div>
                  </motion.div>
                ) : (
                  messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`flex items-start space-x-3 max-w-[80%] ${
                        message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                      }`}>
                        <Avatar className="w-8 h-8 flex-shrink-0">
                          <AvatarFallback className={
                            message.role === 'user' 
                              ? 'bg-blue-600' 
                              : 'bg-gradient-to-r from-purple-600 to-blue-600'
                          }>
                            {message.role === 'user' ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
                          </AvatarFallback>
                        </Avatar>
                        <div className={`backdrop-blur-sm rounded-2xl p-4 ${
                          message.role === 'user'
                            ? isDark 
                              ? 'bg-blue-600/80 text-white' 
                              : 'bg-blue-500/80 text-white'
                            : isDark 
                              ? 'bg-white/10 border border-white/20 text-white' 
                              : 'bg-white/40 border border-white/40 text-gray-800'
                        }`}>
                          <p className="whitespace-pre-wrap">{message.content}</p>
                        </div>
                      </div>
                    </motion.div>
                  ))
                )}
              </AnimatePresence>

              {/* Typing Indicator */}
              {isTyping && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex justify-start"
                >
                  <div className="flex items-start space-x-3">
                    <Avatar className="w-8 h-8">
                      <AvatarFallback className="bg-gradient-to-r from-purple-600 to-blue-600">
                        <Bot className="w-4 h-4" />
                      </AvatarFallback>
                    </Avatar>
                    <div className={`backdrop-blur-sm rounded-2xl p-4 ${
                      isDark 
                        ? 'bg-white/10 border border-white/20' 
                        : 'bg-white/40 border border-white/40'
                    }`}>
                      <div className="flex space-x-1">
                        <div className={`w-2 h-2 rounded-full animate-bounce ${
                          isDark ? 'bg-white' : 'bg-gray-600'
                        }`} style={{ animationDelay: '0ms' }}></div>
                        <div className={`w-2 h-2 rounded-full animate-bounce ${
                          isDark ? 'bg-white' : 'bg-gray-600'
                        }`} style={{ animationDelay: '150ms' }}></div>
                        <div className={`w-2 h-2 rounded-full animate-bounce ${
                          isDark ? 'bg-white' : 'bg-gray-600'
                        }`} style={{ animationDelay: '300ms' }}></div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>
        </div>

        {/* Input Area */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`backdrop-blur-md rounded-2xl border p-4 ${
            isDark 
              ? 'bg-white/10 border-white/20' 
              : 'bg-white/40 border-white/60'
          }`}
        >
          <div className="flex space-x-4">
            <Input
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything..."
              className={`flex-1 backdrop-blur-sm border-0 rounded-xl ${
                isDark 
                  ? 'bg-white/10 text-white placeholder:text-white/60' 
                  : 'bg-white/30 text-gray-800 placeholder:text-gray-600'
              }`}
              disabled={isLoading}
            />
            <Button
              onClick={sendMessage}
              disabled={!input.trim() || isLoading}
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl px-6"
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
          <div className={`mt-2 text-xs text-center ${
            isDark ? 'text-gray-400' : 'text-gray-600'
          }`}>
            TainoAI v1.0.11 - {isConnected ? 'Custom AI Engine LIVE' : 'Independent AI System'}
            {isConnected && (
              <span className="ml-2 inline-flex items-center">
                <Zap className="w-3 h-3 text-green-400 mr-1" />
                <span className="text-green-400">LIVE</span>
              </span>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  )
}
