# TainoAI - Llama.cpp with Mistral 7B deployment
# Choose your deployment type by changing the Dockerfile

# Option 1: Ultra-lightweight (no AI model) - <450MB
# build:
#   docker:
#     web: Dockerfile.ultra

# Option 2: Full AI with Llama.cpp + Mistral 7B - <500MB
build:
  docker:
    web: Dockerfile.llama

run:
  web: python main.py

release:
  image: web
  command:
    - mkdir -p data
    - mkdir -p models
