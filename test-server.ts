/**
 * Minimal <PERSON>I Engine Test Server
 */

console.log('TEST: <PERSON>rip<PERSON> started');

import { WebSocketServer, WebSocket } from 'ws';
import http from 'http';

console.log('🎬 Starting TainoAI Engine Test Server...');

// Find available port
function findAvailablePort(): number {
  // Use a random high port to avoid conflicts
  return Math.floor(Math.random() * (65535 - 49152) + 49152);
}

async function startServer() {
  const port = 7777; // Use a fixed port for testing
  
  try {
    console.log('🔧 Creating HTTP server...');
    const server = http.createServer();
    
    console.log('🔧 Creating WebSocket server...');
    const wss = new WebSocketServer({ server });
    
    console.log('🔧 Setting up WebSocket handlers...');
    wss.on('connection', (ws: WebSocket) => {
      console.log('🔌 New client connected');
      
      // Send welcome message
      ws.send(JSON.stringify({
        type: 'welcome',
        data: {
          message: '¡Ho<PERSON>! TainoAI Engine Test Server is ready!',
          version: '1.0.11',
          status: 'ready',
          port: port,
        },
      }));
      
      ws.on('message', (data: Buffer) => {
        try {
          const message = JSON.parse(data.toString());
          console.log('📨 Received message:', message.type);
          
          // Echo back with mock response
          if (message.type === 'chat') {
            ws.send(JSON.stringify({
              type: 'chat_response',
              data: {
                response: `Hello! I'm TainoAI running on port ${port}. You said: "${message.data.message}". This is a test response from the custom AI engine!`,
                metadata: { model: 'test', port: port },
              },
            }));
          }
        } catch (error) {
          console.error('❌ Error handling message:', error);
        }
      });
      
      ws.on('close', () => {
        console.log('🔌 Client disconnected');
      });
    });
    
    console.log(`🔧 Starting server on port ${port}...`);
    server.listen(port, () => {
      console.log(`🌐 TainoAI Engine Test Server running on port ${port}`);
      console.log(`📡 WebSocket endpoint: ws://localhost:${port}`);
      console.log('🎯 Ready to accept connections!');
    });
    
    server.on('error', (error: any) => {
      if (error.code === 'EADDRINUSE') {
        console.warn(`⚠️ Port ${port} is in use, please try another port`);
      } else {
        console.error('❌ Server error:', error);
      }
    });
    
  } catch (error) {
    console.error('❌ Failed to start test server:', error);
  }
}

startServer();
