"""
TainoAI - Claude API Integration
Provides Claude API capabilities as an additional model option
"""

import os
import logging
import anthropic
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class ClaudeEngine:
    def __init__(self):
        self.client = anthropic.Anthropic(
            api_key=os.getenv("CLAUDE_API_KEY")
        )
        self.model = "claude-3-opus-20240229"  # Using the latest Claude model
        self.max_tokens = 4096
        
    async def generate_response(self, 
                              prompt: str,
                              max_tokens: Optional[int] = None,
                              temperature: Optional[float] = None) -> str:
        """Generate a response using Claude API"""
        try:
            response = self.client.messages.create(
                model=self.model,
                max_tokens=max_tokens or self.max_tokens,
                temperature=temperature or 0.7,
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            )
            return response.content[0].text
        except Exception as e:
            logger.error(f"Claude API error: {e}")
            return ""

# Global instance
claude_engine = ClaudeEngine()
