/**
 * TainoAI Client - Frontend Integration for Custom AI Engine
 */

export interface TainoAIMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  metadata?: any;
}

export interface TainoAIResponse {
  content: string;
  confidence: number;
  metadata: {
    model: string;
    tokens: number;
    processingTime: number;
    memoryUsed: number;
  };
}

export interface TainoAIClientOptions {
  serverUrl?: string;
  autoReconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export class TainoAIClient {
  private ws: WebSocket | null = null;
  private serverUrl: string;
  private autoReconnect: boolean;
  private reconnectInterval: number;
  private maxReconnectAttempts: number;
  private reconnectAttempts = 0;
  private isConnected = false;
  private messageHandlers: Map<string, (data: any) => void> = new Map();
  private conversationId: string;

  constructor(options: TainoAIClientOptions = {}) {
    this.serverUrl = options.serverUrl || 'ws://localhost:8001';
    this.autoReconnect = options.autoReconnect !== false;
    this.reconnectInterval = options.reconnectInterval || 3000;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
    this.conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.serverUrl);

        this.ws.onopen = () => {
          console.log('🔌 Connected to TainoAI Engine');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('❌ Failed to parse message:', error);
          }
        };

        this.ws.onclose = () => {
          console.log('🔌 Disconnected from TainoAI Engine');
          this.isConnected = false;
          
          if (this.autoReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`🔄 Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            setTimeout(() => this.connect(), this.reconnectInterval);
          }
        };

        this.ws.onerror = (error) => {
          console.error('❌ WebSocket error:', error);
          reject(error);
        };

      } catch (error) {
        reject(error);
      }
    });
  }

  private handleMessage(message: any): void {
    const { type, data } = message;
    
    // Call registered handlers
    const handler = this.messageHandlers.get(type);
    if (handler) {
      handler(data);
    }

    // Default handling for common message types
    switch (type) {
      case 'welcome':
        console.log('🎉 TainoAI Engine:', data.message);
        break;
      
      case 'error':
        console.error('❌ TainoAI Error:', data.error);
        break;
      
      case 'typing':
        // Handle typing indicators
        break;
    }
  }

  on(eventType: string, handler: (data: any) => void): void {
    this.messageHandlers.set(eventType, handler);
  }

  off(eventType: string): void {
    this.messageHandlers.delete(eventType);
  }

  async sendMessage(
    message: string,
    options: {
      temperature?: number;
      maxTokens?: number;
      useTools?: boolean;
      useKnowledge?: boolean;
    } = {}
  ): Promise<void> {
    if (!this.isConnected || !this.ws) {
      throw new Error('Not connected to TainoAI Engine');
    }

    const payload = {
      type: 'chat',
      data: {
        message,
        conversationId: this.conversationId,
        userId: 'default',
        options,
      },
    };

    this.ws.send(JSON.stringify(payload));
  }

  async uploadDocument(
    content: string,
    filename: string,
    metadata: any = {}
  ): Promise<void> {
    if (!this.isConnected || !this.ws) {
      throw new Error('Not connected to TainoAI Engine');
    }

    const payload = {
      type: 'upload_document',
      data: {
        content,
        filename,
        metadata,
      },
    };

    this.ws.send(JSON.stringify(payload));
  }

  async searchKnowledge(
    query: string,
    maxResults: number = 5,
    source?: string
  ): Promise<void> {
    if (!this.isConnected || !this.ws) {
      throw new Error('Not connected to TainoAI Engine');
    }

    const payload = {
      type: 'search_knowledge',
      data: {
        query,
        maxResults,
        source,
      },
    };

    this.ws.send(JSON.stringify(payload));
  }

  async executeCode(
    code: string,
    language: 'python' | 'javascript' | 'bash',
    timeout?: number,
    args?: string[]
  ): Promise<void> {
    if (!this.isConnected || !this.ws) {
      throw new Error('Not connected to TainoAI Engine');
    }

    const payload = {
      type: 'execute_code',
      data: {
        code,
        language,
        timeout,
        args,
      },
    };

    this.ws.send(JSON.stringify(payload));
  }

  async webSearch(
    query: string,
    maxResults: number = 5,
    source: 'duckduckgo' | 'bing' | 'direct' = 'duckduckgo'
  ): Promise<void> {
    if (!this.isConnected || !this.ws) {
      throw new Error('Not connected to TainoAI Engine');
    }

    const payload = {
      type: 'web_search',
      data: {
        query,
        maxResults,
        source,
      },
    };

    this.ws.send(JSON.stringify(payload));
  }

  async getStats(): Promise<void> {
    if (!this.isConnected || !this.ws) {
      throw new Error('Not connected to TainoAI Engine');
    }

    const payload = {
      type: 'get_stats',
      data: {},
    };

    this.ws.send(JSON.stringify(payload));
  }

  ping(): void {
    if (!this.isConnected || !this.ws) {
      return;
    }

    const payload = {
      type: 'ping',
      data: { timestamp: Date.now() },
    };

    this.ws.send(JSON.stringify(payload));
  }

  disconnect(): void {
    this.autoReconnect = false;
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    this.isConnected = false;
  }

  getConnectionStatus(): {
    isConnected: boolean;
    reconnectAttempts: number;
    conversationId: string;
  } {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      conversationId: this.conversationId,
    };
  }

  newConversation(): void {
    this.conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Utility methods for React integration
  createChatHook() {
    return {
      sendMessage: this.sendMessage.bind(this),
      uploadDocument: this.uploadDocument.bind(this),
      searchKnowledge: this.searchKnowledge.bind(this),
      executeCode: this.executeCode.bind(this),
      webSearch: this.webSearch.bind(this),
      getStats: this.getStats.bind(this),
      newConversation: this.newConversation.bind(this),
      getConnectionStatus: this.getConnectionStatus.bind(this),
      on: this.on.bind(this),
      off: this.off.bind(this),
    };
  }
}

// React Hook for TainoAI
export function useTainoAI(options?: TainoAIClientOptions) {
  const client = new TainoAIClient(options);
  
  return {
    client,
    connect: () => client.connect(),
    disconnect: () => client.disconnect(),
    ...client.createChatHook(),
  };
}

export default TainoAIClient;
