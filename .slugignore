# TainoAI Heroku Slug Optimization - Exclude Large Files

# Development files
.env
.env.local
.env.development
.env.test
.env.production
.vscode/
.idea/
*.swp
*.swo
*~

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
venv/
.venv/
env/
.env/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity
.next/cache/

# Large model files (should be downloaded during deployment)
models/
*.gguf
*.bin
*.ckpt
*.safetensors
*.pt
*.pth

# Cache and data directories
.cache/
cache/
data/
documents/
logs/
uploads/

# Keep essential files
!data/.gitkeep
!documents/.gitkeep
!cache/.gitkeep
!logs/.gitkeep
!uploads/.gitkeep

# Development and build artifacts
static/_next/
out/
.vercel/
.turbo/
