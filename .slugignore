# TainoAI Heroku Slug Optimization - Exclude Large Files

# Development files
.env
.env.local
.env.development
.env.test
.env.production
.vscode/
.idea/
*.swp
*.swo
*~

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
venv/
.venv/
env/
.env/

# Node.js (if you have frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Cache directories
.cache/
.huggingface/
.transformers_cache/
.torch/
.pytorch/

# Model files (will be downloaded at runtime)
*.pt
*.pth
*.bin
*.safetensors
models/
checkpoints/
weights/

# Data files
data/
datasets/
*.csv
*.json
*.parquet
*.h5
*.hdf5

# Database files
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log
log.txt

# Documentation
docs/
*.md
README.md
DEPLOYMENT_COMMANDS.md
PYTORCH_VERSION_FIX.md
HEROKU_FIX.md

# Test files
tests/
test_*
*_test.py
pytest_cache/
.coverage
htmlcov/

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/

# Large media files
*.mp4
*.avi
*.mov
*.mp3
*.wav
*.png
*.jpg
*.jpeg
*.gif
*.pdf

# Temporary files
tmp/
temp/
*.tmp
*.temp

# OS files
.DS_Store
Thumbs.db
desktop.ini

# Git (if needed)
.git/
.gitignore

# Docker (if not needed on Heroku)
Dockerfile
docker-compose.yml
.dockerignore
