# TainoAI Production .gitignore

# Environment variables
.env
.env.local
.env.*.local

# Large model files (handled in Docker)
models/
*.gguf
*.bin
*.ckpt
*.safetensors
*.pt
*.pth
*.h5
*.pkl
*.joblib

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so

# Virtual environments
env/
venv/
.venv/
ENV/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Version directories (cleanup artifacts)
[0-9]*.[0-9]*.[0-9]*/

# Development files (already removed)
*_old.*
*_backup.*
*.bak
*.tmp

# Logs
*.log
logs/

# Dependencies
node_modules/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover

# Testing
.pytest_cache/
.tox/

# Build artifacts
build/
dist/
*.egg-info/
