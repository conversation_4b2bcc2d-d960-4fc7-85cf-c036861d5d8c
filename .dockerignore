# TainoAI - <PERSON>er ignore for minimal slug size
# Exclude large model files and development artifacts

# Model files (downloaded during Docker build)
models/
*.gguf
*.bin
*.ckpt
*.safetensors
*.pt
*.pth
*.h5
*.pkl
*.joblib

# Development files
.git
.gitignore
README.md
*.md

# Node.js
node_modules
.next
out
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# IDEs
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test files
test_*.py
*_test.py
tests/
test_document.txt

# Large files that shouldn't be in Docker
*.zip
*.tar.gz
*.rar
*.7z

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Data files (will be created at runtime)
data/

# Build artifacts
build/
dist/
*.egg-info/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Heroku
.heroku/

# Docker
Dockerfile
Dockerfile.*
docker-compose.yml
docker-compose.yaml

# Documentation
docs/
*.pdf
*.doc
*.docx
