# TainoAI - Full AI Power 🤖

**Advanced AI Assistant with Full Mistral 7B Model (3.9GB)**  
*Optimized for Heroku deployment with llama.cpp integration*

## 🚀 Features

### Full AI Capabilities
- **🧠 Mistral 7B Model**: Full 3.9GB quantized model for production-grade responses
- **⚡ llama.cpp Integration**: Optimized C++ inference engine
- **💬 Smart Conversations**: Context-aware responses with memory
- **📚 Document Learning**: Upload and learn from PDF, Word, Excel files
- **🎯 Enhanced Responses**: Human-like, contextually relevant answers

### Frontend
- **🎨 Modern UI**: Next.js 14 with purple glassmorphism theme
- **📱 Responsive Design**: Works on desktop and mobile
- **🔄 Real-time Chat**: Smooth conversation experience
- **📤 File Upload**: Drag-and-drop document upload

### Backend
- **🔥 FastAPI**: High-performance Python backend
- **🐳 Docker**: Containerized deployment
- **☁️ Heroku Ready**: Optimized for cloud deployment
- **📊 Performance Tracking**: Request metrics and monitoring

## 🏗️ Architecture

```
TainoAI/
├── 🎨 Frontend (Next.js + TailwindCSS)
│   ├── app/                 # Next.js app directory
│   ├── components/          # React components
│   └── lib/                 # Utilities
├── 🤖 Backend (FastAPI + llama.cpp)
│   ├── main.py             # FastAPI application
│   ├── llama_engine.py     # AI engine with full model
│   ├── document_processor.py # Document handling
│   └── download_model.py   # Model downloader
├── 🐳 Docker
│   ├── Dockerfile.llama    # Full AI deployment
│   └── heroku.yml         # Heroku configuration
└── 📦 Models
    └── mistral-7b-instruct-v0.1.Q4_K_M.gguf (3.9GB)
```

## 🚀 Quick Start

### 1. Download Full AI Model
```bash
python download_model.py
```

### 2. Build Docker Container
```bash
docker build -f Dockerfile.llama -t tainoai .
```

### 3. Run Locally
```bash
docker run -p 8000:8000 tainoai
```

### 4. Deploy to Heroku
```bash
heroku create your-tainoai-app
git push heroku main
```

## 🔧 Configuration

### Environment Variables
```bash
# AI Model Configuration
MODEL_PATH=./models/mistral-7b-instruct-v0.1.Q4_K_M.gguf
LLAMA_BINARY=./llama.cpp/main
LLAMA_THREADS=4
LLAMA_CONTEXT_SIZE=4096
LLAMA_MAX_TOKENS=1024

# TainoAI Settings
TAINOAI_MODE=FULL_AI
TAINOAI_VERSION=8.0.0
PORT=8000
```

### Performance Optimization
- **Memory**: 512MB+ RAM recommended
- **CPU**: Multi-core for better performance
- **Storage**: 5GB+ for model and dependencies
- **Network**: Stable connection for model download

## 📚 Usage

### Chat Interface
1. Open your deployed TainoAI URL
2. Start chatting with the AI
3. Upload documents for enhanced responses
4. Enjoy human-like conversations!

### API Endpoints
- `POST /chat` - Send messages to AI
- `POST /upload` - Upload documents
- `GET /health` - Health check
- `GET /docs` - API documentation

### Document Upload
Supported formats:
- 📄 PDF files
- 📝 Word documents (.docx)
- 📊 Excel spreadsheets (.xlsx)
- 📋 Text files (.txt, .md)

## 🎯 AI Capabilities

### Full Model Features
- **Complex Reasoning**: Advanced problem-solving
- **Context Awareness**: Remembers conversation history
- **Document Analysis**: Learns from uploaded files
- **Creative Writing**: Stories, poems, content creation
- **Technical Help**: Coding, explanations, tutorials
- **Multilingual**: Supports multiple languages

### Response Quality
- **Human-like**: Natural conversation flow
- **Accurate**: Fact-based responses
- **Detailed**: Comprehensive explanations
- **Contextual**: Relevant to your questions
- **Adaptive**: Learns your communication style

## 🐳 Docker Deployment

### Multi-stage Build
1. **Stage 1**: Build llama.cpp with optimizations
2. **Stage 2**: Build React frontend
3. **Stage 3**: Production runtime with full model

### Optimizations
- **Model Caching**: Efficient model loading
- **Memory Management**: Optimized for Heroku limits
- **Performance**: Fast response times
- **Reliability**: Robust error handling

## 📊 Monitoring

### Performance Metrics
- Request count and response times
- Model loading status
- Memory and CPU usage
- Error rates and logs

### Health Checks
- Model availability
- API responsiveness
- File system access
- Database connectivity

## 🔒 Security

- **Input Validation**: Secure message processing
- **File Scanning**: Safe document uploads
- **Rate Limiting**: Prevents abuse
- **Error Handling**: Secure error responses

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make your changes
4. Test thoroughly
5. Submit pull request

## 📝 License

MIT License - See LICENSE file for details

## 🆘 Support

- **Issues**: GitHub Issues
- **Documentation**: `/docs` endpoint
- **Health Check**: `/health` endpoint

---

**TainoAI v8.0.0** - Full AI Power with Mistral 7B Model  
*Built with ❤️ for intelligent conversations*
