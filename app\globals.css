@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262 83% 58%;
    --radius: 0.75rem;
  }

  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
}

/* Custom scrollbar for glassmorphism */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Glassmorphism utilities */
.glass {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-light {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
}

/* Smooth transitions */
.transition-glass {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-glass:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
}

/* Loading animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce-dot {
  animation: bounce 1.4s infinite ease-in-out;
}

/* Markdown support */
.markdown h1, .markdown h2, .markdown h3 {
  @apply font-bold mb-2;
}

.markdown h1 { @apply text-xl; }
.markdown h2 { @apply text-lg; }
.markdown h3 { @apply text-base; }

.markdown p {
  @apply mb-2;
}

.markdown ul, .markdown ol {
  @apply ml-4 mb-2;
}

.markdown li {
  @apply mb-1;
}

.markdown code {
  @apply bg-black/20 px-1 py-0.5 rounded text-sm font-mono;
}

.markdown pre {
  @apply bg-black/30 p-3 rounded-lg overflow-x-auto mb-2;
}

.markdown pre code {
  @apply bg-transparent p-0;
}

.markdown blockquote {
  @apply border-l-4 border-white/30 pl-4 italic mb-2;
}

/* Focus states for accessibility */
.focus-visible:focus {
  @apply outline-none ring-2 ring-purple-400 ring-offset-2 ring-offset-transparent;
}
