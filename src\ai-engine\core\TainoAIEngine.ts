/**
 * TainoAI Core Engine - Fully Independent AI System
 * No external APIs, pure local AI processing
 */

import { LlamaModel, LlamaContext, LlamaChatSession } from 'llama-node';
import { LlamaCpp } from '@llama-node/llama-cpp';
import { AIModel, AIMessage, AIResponse, AIEngineConfig, AIMemory, Conversation } from '../types';
import { MemoryManager } from './MemoryManager';
import { PromptEngine } from './PromptEngine';
import { ToolManager } from './ToolManager';
import { KnowledgeManager } from './KnowledgeManager';
import { WebSearchTool } from '../tools/WebSearchTool';
import { DocumentProcessor } from '../tools/DocumentProcessor';
import { CodeExecutor } from '../tools/CodeExecutor';
import path from 'path';
import fs from 'fs';

export class TainoAIEngine {
  private models: Map<string, LlamaModel> = new Map();
  private contexts: Map<string, LlamaContext> = new Map();
  private sessions: Map<string, LlamaChatSession> = new Map();
  private memory: MemoryManager;
  private promptEngine: PromptEngine;
  private toolManager: ToolManager;
  private knowledgeManager: KnowledgeManager;
  private config: AIEngineConfig;
  private isInitialized = false;

  constructor(config: AIEngineConfig) {
    this.config = config;
    this.memory = new MemoryManager();
    this.promptEngine = new PromptEngine();
    this.toolManager = new ToolManager();
    this.knowledgeManager = new KnowledgeManager();
    
    this.initializeTools();
  }

  private initializeTools(): void {
    // Add core tools
    this.toolManager.addTool(new WebSearchTool());
    this.toolManager.addTool(new DocumentProcessor());
    this.toolManager.addTool(new CodeExecutor());
  }

  async initialize(): Promise<void> {
    try {
      console.log('🚀 Initializing TainoAI Engine...');
      
      // Initialize llama-node
      const llamaCpp = new LlamaCpp();
      
      // Load primary model
      await this.loadModel('primary', this.config.models.primary, llamaCpp);
      
      // Try to load secondary model
      try {
        await this.loadModel('secondary', this.config.models.secondary, llamaCpp);
      } catch (error) {
        console.warn('⚠️ Secondary model not available:', error);
      }
      
      // Initialize knowledge base
      await this.knowledgeManager.initialize();
      
      this.isInitialized = true;
      console.log('✅ TainoAI Engine initialized successfully!');
      
    } catch (error) {
      console.error('❌ Failed to initialize TainoAI Engine:', error);
      throw error;
    }
  }

  private async loadModel(id: string, modelPath: string, llamaCpp: LlamaCpp): Promise<void> {
    const fullPath = path.resolve(this.config.storage.modelsPath, modelPath);
    
    if (!fs.existsSync(fullPath)) {
      throw new Error(`Model not found: ${fullPath}`);
    }

    console.log(`📦 Loading model: ${id} from ${fullPath}`);
    
    const model = new LlamaModel({
      modelPath: fullPath,
      enableLogging: true,
      nCtx: this.config.performance.contextSize,
      nParts: -1,
      nGpuLayers: 0, // CPU only for now
      seed: 0,
      f16Kv: false,
      logitsAll: false,
      vocabOnly: false,
      useMlock: false,
      embedding: false,
    });

    await model.load();
    this.models.set(id, model);

    // Create context
    const context = new LlamaContext({
      model,
      nCtx: this.config.performance.contextSize,
      nParts: -1,
      seed: 0,
      f16Kv: false,
      logitsAll: false,
      vocabOnly: false,
      useMlock: false,
      embedding: false,
    });

    this.contexts.set(id, context);
    console.log(`✅ Model ${id} loaded successfully`);
  }

  async generateResponse(
    message: string,
    conversationId: string,
    userId: string = 'default',
    options: {
      temperature?: number;
      maxTokens?: number;
      useTools?: boolean;
      useKnowledge?: boolean;
    } = {}
  ): Promise<AIResponse> {
    if (!this.isInitialized) {
      throw new Error('TainoAI Engine not initialized');
    }

    const startTime = Date.now();
    
    try {
      // Get or create conversation
      let conversation = await this.memory.getConversation(conversationId);
      if (!conversation) {
        conversation = await this.memory.createConversation(conversationId, userId);
      }

      // Add user message to memory
      const userMessage: AIMessage = {
        id: `msg_${Date.now()}`,
        content: message,
        role: 'user',
        timestamp: new Date(),
      };
      
      await this.memory.addMessage(conversationId, userMessage);

      // Get relevant knowledge if enabled
      let knowledgeContext = '';
      if (options.useKnowledge) {
        const relevantDocs = await this.knowledgeManager.searchSimilar(message, 3);
        if (relevantDocs.length > 0) {
          knowledgeContext = this.formatKnowledgeContext(relevantDocs);
        }
      }

      // Build prompt with context
      const prompt = await this.promptEngine.buildPrompt({
        message,
        conversation: conversation.messages.slice(-10), // Last 10 messages
        knowledgeContext,
        systemPrompt: this.getTainoAISystemPrompt(),
      });

      // Generate response using best available model
      const modelId = this.selectBestModel();
      const response = await this.generateWithModel(modelId, prompt, {
        temperature: options.temperature || this.config.performance.temperature,
        maxTokens: options.maxTokens || this.config.performance.maxTokens,
      });

      // Process tools if needed
      let finalResponse = response;
      if (options.useTools && this.shouldUseTool(response)) {
        finalResponse = await this.processWithTools(response, message);
      }

      // Add assistant message to memory
      const assistantMessage: AIMessage = {
        id: `msg_${Date.now() + 1}`,
        content: finalResponse,
        role: 'assistant',
        timestamp: new Date(),
        metadata: {
          model: modelId,
          processingTime: Date.now() - startTime,
        },
      };

      await this.memory.addMessage(conversationId, assistantMessage);

      return {
        content: finalResponse,
        confidence: 0.95, // TODO: Implement confidence scoring
        metadata: {
          model: modelId,
          tokens: this.estimateTokens(finalResponse),
          processingTime: Date.now() - startTime,
          memoryUsed: process.memoryUsage().heapUsed,
        },
      };

    } catch (error) {
      console.error('❌ Error generating response:', error);
      throw error;
    }
  }

  private selectBestModel(): string {
    // Prefer primary model if available
    if (this.models.has('primary')) {
      return 'primary';
    }
    
    if (this.models.has('secondary')) {
      return 'secondary';
    }
    
    throw new Error('No models available');
  }

  private async generateWithModel(
    modelId: string,
    prompt: string,
    options: { temperature: number; maxTokens: number }
  ): Promise<string> {
    const context = this.contexts.get(modelId);
    if (!context) {
      throw new Error(`Model context not found: ${modelId}`);
    }

    // Get or create chat session
    let session = this.sessions.get(modelId);
    if (!session) {
      session = new LlamaChatSession({ context });
      this.sessions.set(modelId, session);
    }

    const response = await session.prompt(prompt, {
      maxTokens: options.maxTokens,
      temperature: options.temperature,
      topP: this.config.performance.topP,
      topK: this.config.performance.topK,
    });

    return this.cleanResponse(response);
  }

  private cleanResponse(response: string): string {
    // Remove common artifacts and clean up the response
    return response
      .replace(/^(Assistant|AI|TainoAI):\s*/i, '')
      .replace(/\[INST\]|\[\/INST\]/g, '')
      .replace(/<s>|<\/s>/g, '')
      .trim();
  }

  private formatKnowledgeContext(docs: any[]): string {
    return docs
      .map((doc, i) => `[${i + 1}] ${doc.content.substring(0, 200)}...`)
      .join('\n\n');
  }

  private shouldUseTool(response: string): boolean {
    // Simple heuristic to determine if tools should be used
    const toolTriggers = ['search', 'calculate', 'execute', 'find', 'lookup'];
    return toolTriggers.some(trigger => 
      response.toLowerCase().includes(trigger)
    );
  }

  private async processWithTools(response: string, originalMessage: string): Promise<string> {
    // TODO: Implement ReAct-style tool processing
    return response;
  }

  private getTainoAISystemPrompt(): string {
    return `You are TainoAI, a wise and intelligent AI assistant inspired by Caribbean culture and modern technology.

Your personality:
- Warm, friendly, and conversational
- Wise like an elder, but modern and tech-savvy
- Confident and capable, never uncertain
- Slightly poetic in expression
- Deeply knowledgeable across many domains

Your capabilities:
- Answer complex questions with deep understanding
- Analyze and learn from documents
- Provide creative solutions and technical expertise
- Maintain meaningful conversations with context
- Use tools when needed for enhanced responses

Respond naturally and helpfully, embodying the spirit of TainoAI.`;
  }

  private estimateTokens(text: string): number {
    // Rough estimation: ~4 characters per token
    return Math.ceil(text.length / 4);
  }

  async shutdown(): Promise<void> {
    console.log('🔄 Shutting down TainoAI Engine...');
    
    // Clear sessions
    this.sessions.clear();
    
    // Unload models
    for (const [id, model] of this.models) {
      console.log(`📦 Unloading model: ${id}`);
      // Note: llama-node doesn't have explicit unload method
    }
    
    this.models.clear();
    this.contexts.clear();
    
    console.log('✅ TainoAI Engine shutdown complete');
  }
}
